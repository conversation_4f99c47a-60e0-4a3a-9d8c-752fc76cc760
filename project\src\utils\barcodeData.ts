// Comprehensive Barcode Data Management for Aptiv System
// TypeScript interfaces and data for barcode operations

export interface AptivProduct {
  code: string;
  format: string;
  name: string;
  category: string;
  description: string;
  price?: number;
  stock?: number;
  minStock?: number;
  supplier?: string;
  location?: string;
  lastUpdated?: string;
}

export interface StandardProduct {
  code: string;
  format: string;
  name: string;
  brand: string;
  category: string;
  country: string;
  price?: number;
  description?: string;
}

export interface InternalCode {
  code: string;
  format: string;
  type: string;
  description: string;
  quantity?: number;
  date?: string;
  status?: string;
  inspector?: string;
  location?: string;
}

export interface BarcodeFormat {
  code: string;
  name: string;
  description: string;
  charset: string;
  maxLength?: number;
  length?: number;
  checksum: string;
  usage: string;
  advantages: string[];
  applications: string[];
}

// Aptiv Products Database
export const aptivProducts: AptivProduct[] = [
  {
    code: "APTIV-CBL-HT-001",
    format: "CODE128",
    name: "Câble Haute Tension 16AWG",
    category: "Câbles",
    description: "Câble haute tension pour systèmes automobiles, 16AWG, isolation renforcée",
    price: 12.50,
    stock: 2450,
    minStock: 500,
    supplier: "Nexans France",
    location: "A1-B2-C3"
  },
  {
    code: "APTIV-CON-IP67-004",
    format: "CODE128",
    name: "Connecteur Étanche IP67",
    category: "Connecteurs",
    description: "Connecteur étanche IP67 pour environnement automobile",
    price: 8.75,
    stock: 1250,
    minStock: 200,
    supplier: "Prysmian Group",
    location: "B2-C3-D4"
  },
  {
    code: "APTIV-FCX-ECU-007",
    format: "CODE128",
    name: "Faisceau de Câbles ECU",
    category: "Faisceaux",
    description: "Faisceau principal pour unité de contrôle électronique",
    price: 45.00,
    stock: 875,
    minStock: 100,
    supplier: "Aptiv Internal",
    location: "C3-D4-E5"
  },
  {
    code: "APTIV-SEN-TEMP-012",
    format: "CODE128",
    name: "Capteur de Température",
    category: "Capteurs",
    description: "Capteur de température haute précision pour moteurs",
    price: 25.30,
    stock: 650,
    minStock: 150,
    supplier: "Bosch",
    location: "D4-E5-F6"
  },
  {
    code: "APTIV-REL-PWR-008",
    format: "CODE128",
    name: "Relais de Puissance 40A",
    category: "Relais",
    description: "Relais de puissance pour systèmes électriques automobiles",
    price: 18.90,
    stock: 320,
    minStock: 80,
    supplier: "TE Connectivity",
    location: "E5-F6-G7"
  },
  {
    code: "APTIV-FUS-MINI-025",
    format: "CODE128",
    name: "Fusible Mini 25A",
    category: "Fusibles",
    description: "Fusible mini format 25A pour protection circuits",
    price: 2.15,
    stock: 5000,
    minStock: 1000,
    supplier: "Littelfuse",
    location: "F6-G7-H8"
  }
];

// Standard Products Database
export const standardProducts: StandardProduct[] = [
  {
    code: "3760123456789",
    format: "EAN13",
    name: "Kit de Câblage Automobile Premium",
    brand: "Aptiv",
    category: "Kits",
    country: "France",
    price: 89.99,
    description: "Kit complet de câblage pour véhicules premium"
  },
  {
    code: "3760234567890",
    format: "EAN13",
    name: "Connecteur Multi-Pin 24 Voies",
    brand: "Aptiv",
    category: "Connecteurs",
    country: "France",
    price: 34.50,
    description: "Connecteur haute performance 24 voies"
  },
  {
    code: "3760345678901",
    format: "EAN13",
    name: "Capteur de Position Intelligent",
    brand: "Aptiv",
    category: "Capteurs",
    country: "France",
    price: 67.80,
    description: "Capteur de position avec intelligence embarquée"
  }
];

// Internal Codes Database
export const internalCodes: InternalCode[] = [
  {
    code: "LOT-2024-001",
    format: "CODE39",
    type: "Lot Number",
    description: "Lot de production Janvier 2024",
    quantity: 1000,
    date: "2024-01-15",
    status: "Active"
  },
  {
    code: "QC-PASS-2024-A",
    format: "CODE39",
    type: "Quality Control",
    description: "Contrôle qualité réussi - Série A",
    inspector: "Marie Martin",
    date: "2024-01-20",
    status: "Certified"
  },
  {
    code: "SHIP-FR-001",
    format: "CODE39",
    type: "Shipping",
    description: "Expédition France - Lot 001",
    date: "2024-01-25",
    status: "Dispatched"
  }
];

// Barcode Format Specifications
export const formatSpecs: Record<string, BarcodeFormat> = {
  CODE128: {
    code: "CODE128",
    name: "Code 128",
    description: "Alphanumeric, haute densité",
    charset: "ASCII complet (128 caractères)",
    maxLength: 50,
    checksum: "Modulo 103",
    usage: "Produits internes, traçabilité, logistique",
    advantages: ["Haute densité", "Charset complet", "Fiable"],
    applications: ["Inventaire", "Traçabilité", "Logistique interne"]
  },
  CODE39: {
    code: "CODE39",
    name: "Code 39",
    description: "Alphanumeric, largement utilisé",
    charset: "A-Z, 0-9, -. $/+%",
    maxLength: 43,
    checksum: "Optionnel (Modulo 43)",
    usage: "Identification interne, maintenance, lots",
    advantages: ["Simple", "Pas de checksum obligatoire", "Robuste"],
    applications: ["Maintenance", "Lots de production", "Identification équipements"]
  },
  EAN13: {
    code: "EAN13",
    name: "EAN-13",
    description: "13 chiffres, produits de consommation",
    charset: "0-9 uniquement",
    length: 13,
    checksum: "Modulo 10",
    usage: "Produits commerciaux, retail, distribution",
    advantages: ["Standard mondial", "Reconnaissance universelle", "Retail"],
    applications: ["Produits finis", "Distribution", "Vente au détail"]
  }
};

// Utility Functions
export class BarcodeDataManager {
  // Get all products
  static getAllProducts(): (AptivProduct | StandardProduct | InternalCode)[] {
    return [...aptivProducts, ...standardProducts, ...internalCodes];
  }

  // Search by barcode
  static findByBarcode(code: string): AptivProduct | StandardProduct | InternalCode | null {
    const allProducts = this.getAllProducts();
    return allProducts.find(product => product.code === code) || null;
  }

  // Search by category
  static searchByCategory(category: string): AptivProduct[] {
    return aptivProducts.filter(product => 
      product.category.toLowerCase().includes(category.toLowerCase())
    );
  }

  // Get random sample
  static getRandomSample(): AptivProduct | StandardProduct | InternalCode {
    const allProducts = this.getAllProducts();
    return allProducts[Math.floor(Math.random() * allProducts.length)];
  }

  // Validate barcode format
  static validateBarcode(code: string, format: string): boolean {
    const patterns: Record<string, RegExp> = {
      EAN13: /^\d{13}$/,
      EAN8: /^\d{8}$/,
      UPC: /^\d{12}$/,
      CODE39: /^[A-Z0-9\-. $/+%]+$/,
      ITF14: /^\d{14}$/,
      CODE128: /^.{1,50}$/
    };
    return patterns[format] ? patterns[format].test(code) : false;
  }

  // Generate random barcode for testing
  static generateRandomBarcode(format: string): string {
    switch (format) {
      case 'EAN13':
        return '376' + Math.random().toString().substr(2, 10);
      case 'EAN8':
        return '376' + Math.random().toString().substr(2, 5);
      case 'UPC':
        return Math.random().toString().substr(2, 12);
      case 'CODE39':
        return 'TEST-' + Math.random().toString(36).substr(2, 6).toUpperCase();
      case 'ITF14':
        return '0' + Math.random().toString().substr(2, 13);
      case 'CODE128':
      default:
        return 'APTIV-' + Math.random().toString(36).substr(2, 8).toUpperCase();
    }
  }

  // Get low stock items
  static getLowStockItems(): AptivProduct[] {
    return aptivProducts.filter(product => 
      product.stock !== undefined && 
      product.minStock !== undefined && 
      product.stock <= product.minStock
    );
  }

  // Get products by supplier
  static getProductsBySupplier(supplier: string): AptivProduct[] {
    return aptivProducts.filter(product => 
      product.supplier?.toLowerCase().includes(supplier.toLowerCase())
    );
  }
}

export default BarcodeDataManager;
