<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Système de Code-Barres Intégré - Aptiv</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📊</text></svg>">
    <style>
        /* Additional styles for integration */
        .integration-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .api-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 1rem 0;
        }
        
        .integration-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin: 2rem 0;
        }
        
        .integration-btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            font-weight: bold;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .integration-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>

<body>
    <div class="integration-header">
        <h1>🔍 Système de Code-Barres Intégré</h1>
        <p>Solution complète pour la génération et la lecture de codes-barres</p>
        <p><strong>Aptiv - Twisting Monitoring Tool</strong></p>
    </div>

    <div class="container">
        <!-- Navigation Buttons -->
        <div class="integration-buttons">
            <a href="#generator" class="integration-btn btn-primary">
                📊 Générateur
            </a>
            <a href="#scanner" class="integration-btn btn-secondary">
                📷 Scanner
            </a>
            <a href="#api" class="integration-btn btn-success">
                🔧 API Integration
            </a>
            <a href="Untitled-1.html" class="integration-btn btn-primary">
                🚀 Lancer l'Application
            </a>
        </div>

        <!-- Features Grid -->
        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <h3>Génération de Code-Barres</h3>
                <ul>
                    <li>Support de 6 formats standards</li>
                    <li>Code 128, Code 39, EAN-13, EAN-8, UPC-A, ITF-14</li>
                    <li>Validation automatique des données</li>
                    <li>Export en PNG haute qualité</li>
                    <li>Aperçu en temps réel</li>
                </ul>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📷</div>
                <h3>Scanner de Code-Barres</h3>
                <ul>
                    <li>Scan via caméra en temps réel</li>
                    <li>Upload d'images pour analyse</li>
                    <li>Saisie manuelle de secours</li>
                    <li>Historique des scans</li>
                    <li>Détection automatique du format</li>
                </ul>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🔧</div>
                <h3>Intégration Système</h3>
                <ul>
                    <li>API JavaScript simple</li>
                    <li>Composants React prêts à l'emploi</li>
                    <li>Compatible avec votre système Aptiv</li>
                    <li>Gestion des événements</li>
                    <li>Configuration personnalisable</li>
                </ul>
            </div>
        </div>

        <!-- API Integration Section -->
        <div id="api" class="api-section">
            <h2>🔧 Intégration API</h2>
            <p>Intégrez facilement les fonctionnalités de code-barres dans votre application Aptiv :</p>

            <h3>1. Génération de Code-Barres</h3>
            <div class="code-block">
// Génération simple
JsBarcode("#barcode", "1234567890123", {
    format: "EAN13",
    width: 2,
    height: 100,
    displayValue: true
});

// Avec validation
function generateProductBarcode(productId) {
    const barcodeElement = document.getElementById('product-barcode');
    try {
        JsBarcode(barcodeElement, productId, {
            format: "CODE128",
            width: 2,
            height: 80,
            margin: 10,
            background: "#ffffff",
            lineColor: "#000000"
        });
        return true;
    } catch (error) {
        console.error('Erreur génération:', error);
        return false;
    }
}
            </div>

            <h3>2. Scanner Integration</h3>
            <div class="code-block">
// Intégration du scanner dans votre composant React
import Scanner from './components/Scanner';

function ProductManagement() {
    const handleBarcodeScanned = (data, format) => {
        console.log('Code-barres scanné:', data);
        // Traiter les données scannées
        updateProductInfo(data);
    };

    return (
        &lt;Scanner onBarcodeScanned={handleBarcodeScanned} /&gt;
    );
}
            </div>

            <h3>3. Configuration pour Aptiv</h3>
            <div class="code-block">
// Configuration spécifique Aptiv
const aptivBarcodeConfig = {
    // Formats supportés pour les produits Aptiv
    supportedFormats: ['CODE128', 'CODE39', 'EAN13'],
    
    // Préfixes pour les codes internes
    internalPrefixes: ['APTIV-', 'TWS-', 'CBL-'],
    
    // Validation des codes produits
    validateProductCode: (code) => {
        return /^(APTIV|TWS|CBL)-[A-Z0-9]{3,}-[0-9]{3}$/.test(code);
    },
    
    // Intégration avec l'inventaire
    onProductScanned: (productCode) => {
        // Rechercher dans la base de données produits
        return findProductByCode(productCode);
    }
};
            </div>
        </div>

        <!-- Usage Examples -->
        <div class="api-section">
            <h2>📋 Exemples d'Usage</h2>
            
            <h3>Cas d'usage Aptiv :</h3>
            <ul style="text-align: left; max-width: 600px; margin: 0 auto;">
                <li><strong>Gestion d'inventaire :</strong> Scanner les codes produits pour mise à jour automatique</li>
                <li><strong>Traçabilité :</strong> Générer des codes uniques pour chaque lot de production</li>
                <li><strong>Contrôle qualité :</strong> Scanner pour vérifier la conformité des produits</li>
                <li><strong>Expédition :</strong> Générer des étiquettes de transport avec codes-barres</li>
                <li><strong>Maintenance :</strong> Codes-barres pour identifier les équipements</li>
            </ul>
        </div>

        <!-- Integration Steps -->
        <div class="api-section">
            <h2>🚀 Étapes d'Intégration</h2>
            <div style="text-align: left; max-width: 800px; margin: 0 auto;">
                <ol>
                    <li><strong>Installation :</strong> Copier les fichiers dans votre projet Aptiv</li>
                    <li><strong>Dépendances :</strong> Inclure JsBarcode via CDN ou npm</li>
                    <li><strong>Composants :</strong> Importer BarcodeGenerator et Scanner</li>
                    <li><strong>Configuration :</strong> Adapter les paramètres à vos besoins</li>
                    <li><strong>Test :</strong> Vérifier le fonctionnement avec vos données</li>
                    <li><strong>Déploiement :</strong> Intégrer dans votre workflow de production</li>
                </ol>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="integration-buttons">
            <a href="Untitled-1.html" class="integration-btn btn-primary">
                🚀 Tester l'Application
            </a>
            <a href="http://localhost:5176/" class="integration-btn btn-secondary" target="_blank">
                🔗 Ouvrir App React
            </a>
            <button onclick="downloadIntegrationFiles()" class="integration-btn btn-success">
                💾 Télécharger les Fichiers
            </button>
        </div>

        <!-- Footer -->
        <div style="margin-top: 3rem; padding: 2rem; background: #f8f9fa; border-radius: 15px; text-align: center;">
            <h3>📞 Support Technique</h3>
            <p>Pour toute question sur l'intégration des codes-barres dans votre système Aptiv :</p>
            <p><strong>Email :</strong> <EMAIL></p>
            <p><strong>Documentation :</strong> <a href="#" style="color: #667eea;">Guide d'intégration complet</a></p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script>
        // Smooth scrolling for navigation
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });

        // Download integration files function
        function downloadIntegrationFiles() {
            alert('Fonctionnalité de téléchargement : Les fichiers sont déjà disponibles dans votre projet !\n\n' +
                  'Fichiers inclus :\n' +
                  '- BarcodeGenerator.tsx\n' +
                  '- Scanner.tsx (mis à jour)\n' +
                  '- styles.css\n' +
                  '- script.js\n' +
                  '- Untitled-1.html\n' +
                  '- barcode-integration.html');
        }

        // Demo barcode generation
        window.addEventListener('load', function() {
            // Generate a demo barcode
            setTimeout(() => {
                const demoElement = document.createElement('div');
                demoElement.innerHTML = '<svg id="demo-barcode"></svg>';
                demoElement.style.textAlign = 'center';
                demoElement.style.margin = '2rem 0';
                
                if (window.JsBarcode) {
                    JsBarcode("#demo-barcode", "APTIV-DEMO-001", {
                        format: "CODE128",
                        width: 1,
                        height: 50,
                        displayValue: true,
                        fontSize: 12,
                        margin: 5
                    });
                }
            }, 1000);
        });
    </script>
</body>

</html>
