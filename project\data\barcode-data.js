// Comprehensive Barcode Data for Aptiv System
// This file contains various barcode data for testing and real-world usage

// Aptiv Product Codes
export const aptivProducts = [
    {
        code: "APTIV-CBL-HT-001",
        format: "CODE128",
        name: "Câble Haute Tension 16AWG",
        category: "Câbles",
        description: "Câble haute tension pour systèmes automobiles",
        price: 12.50,
        stock: 2450,
        supplier: "Nexans France",
        location: "A1-B2-C3"
    },
    {
        code: "APTIV-CON-IP67-004",
        format: "CODE128", 
        name: "Connecteur Étanche IP67",
        category: "Connecteurs",
        description: "Connecteur étanche pour environnement automobile",
        price: 8.75,
        stock: 1250,
        supplier: "Prysmian Group",
        location: "B2-C3-D4"
    },
    {
        code: "APTIV-FCX-ECU-007",
        format: "CODE128",
        name: "Faisceau de Câbles ECU",
        category: "Faisceaux",
        description: "Faisceau principal pour unité de contrôle électronique",
        price: 45.00,
        stock: 875,
        supplier: "Aptiv Internal",
        location: "C3-D4-E5"
    },
    {
        code: "APTIV-SEN-TEMP-012",
        format: "CODE128",
        name: "Capteur de Température",
        category: "Capteurs",
        description: "Capteur de température haute précision",
        price: 25.30,
        stock: 650,
        supplier: "Bosch",
        location: "D4-E5-F6"
    },
    {
        code: "APTIV-REL-PWR-008",
        format: "CODE128",
        name: "Relais de Puissance 40A",
        category: "Relais",
        description: "Relais de puissance pour systèmes électriques",
        price: 18.90,
        stock: 320,
        supplier: "TE Connectivity",
        location: "E5-F6-G7"
    }
];

// Standard Product Barcodes (EAN-13)
export const standardProducts = [
    {
        code: "3760123456789",
        format: "EAN13",
        name: "Produit Automobile Standard",
        brand: "Aptiv",
        category: "Composants",
        country: "France"
    },
    {
        code: "3760234567890",
        format: "EAN13",
        name: "Kit de Câblage Premium",
        brand: "Aptiv",
        category: "Kits",
        country: "France"
    },
    {
        code: "3760345678901",
        format: "EAN13",
        name: "Connecteur Multi-Pin",
        brand: "Aptiv",
        category: "Connecteurs",
        country: "France"
    },
    {
        code: "3760456789012",
        format: "EAN13",
        name: "Capteur Intelligent",
        brand: "Aptiv",
        category: "Capteurs",
        country: "France"
    },
    {
        code: "3760567890123",
        format: "EAN13",
        name: "Module de Contrôle",
        brand: "Aptiv",
        category: "Modules",
        country: "France"
    }
];

// UPC Codes for North American Market
export const upcProducts = [
    {
        code: "012345678905",
        format: "UPC",
        name: "Automotive Harness Kit",
        brand: "Aptiv",
        region: "North America",
        category: "Harnesses"
    },
    {
        code: "023456789016",
        format: "UPC",
        name: "High Voltage Cable Assembly",
        brand: "Aptiv",
        region: "North America",
        category: "Cables"
    },
    {
        code: "034567890127",
        format: "UPC",
        name: "Smart Sensor Module",
        brand: "Aptiv",
        region: "North America",
        category: "Sensors"
    }
];

// Code 39 for Internal Tracking
export const internalCodes = [
    {
        code: "LOT-2024-001",
        format: "CODE39",
        type: "Lot Number",
        description: "Lot de production Janvier 2024",
        quantity: 1000,
        date: "2024-01-15"
    },
    {
        code: "QC-PASS-2024-A",
        format: "CODE39",
        type: "Quality Control",
        description: "Contrôle qualité réussi - Série A",
        inspector: "Marie Martin",
        date: "2024-01-20"
    },
    {
        code: "SHIP-FR-001",
        format: "CODE39",
        type: "Shipping",
        description: "Expédition France - Lot 001",
        destination: "Renault Flins",
        date: "2024-01-25"
    },
    {
        code: "MAINT-EQ-012",
        format: "CODE39",
        type: "Maintenance",
        description: "Équipement de maintenance 012",
        location: "Atelier B",
        nextMaintenance: "2024-06-15"
    }
];

// ITF-14 for Packaging
export const packagingCodes = [
    {
        code: "01234567890128",
        format: "ITF14",
        type: "Case Code",
        description: "Caisse de 50 connecteurs",
        innerCode: "3760123456789",
        quantity: 50
    },
    {
        code: "02345678901239",
        format: "ITF14",
        type: "Pallet Code", 
        description: "Palette de 20 caisses",
        caseQuantity: 20,
        totalItems: 1000
    }
];

// Test Data for Development
export const testData = [
    {
        code: "TEST-001",
        format: "CODE128",
        description: "Code de test basique"
    },
    {
        code: "1234567890123",
        format: "EAN13",
        description: "EAN-13 de test"
    },
    {
        code: "12345678",
        format: "EAN8",
        description: "EAN-8 de test"
    },
    {
        code: "HELLO WORLD",
        format: "CODE39",
        description: "Texte simple Code 39"
    },
    {
        code: "123456789012",
        format: "UPC",
        description: "UPC-A de test"
    }
];

// Sample Scan History
export const sampleScanHistory = [
    {
        code: "APTIV-CBL-HT-001",
        format: "CODE128",
        timestamp: new Date("2024-01-20T10:30:00"),
        user: "Jean Dupont",
        action: "Inventory Check",
        location: "Warehouse A"
    },
    {
        code: "3760123456789",
        format: "EAN13",
        timestamp: new Date("2024-01-20T11:15:00"),
        user: "Marie Martin",
        action: "Quality Control",
        location: "QC Station 2"
    },
    {
        code: "LOT-2024-001",
        format: "CODE39",
        timestamp: new Date("2024-01-20T14:45:00"),
        user: "Pierre Bernard",
        action: "Production Tracking",
        location: "Production Line 1"
    }
];

// Barcode Format Specifications
export const formatSpecs = {
    CODE128: {
        name: "Code 128",
        description: "Alphanumeric, haute densité",
        charset: "ASCII complet",
        maxLength: 50,
        checksum: "Modulo 103",
        usage: "Produits internes, traçabilité"
    },
    CODE39: {
        name: "Code 39", 
        description: "Alphanumeric, largement utilisé",
        charset: "A-Z, 0-9, -. $/+%",
        maxLength: 43,
        checksum: "Optionnel",
        usage: "Identification interne, maintenance"
    },
    EAN13: {
        name: "EAN-13",
        description: "13 chiffres, produits de consommation",
        charset: "0-9 uniquement",
        length: 13,
        checksum: "Modulo 10",
        usage: "Produits commerciaux, retail"
    },
    EAN8: {
        name: "EAN-8",
        description: "8 chiffres, petits produits",
        charset: "0-9 uniquement", 
        length: 8,
        checksum: "Modulo 10",
        usage: "Petits produits, espace limité"
    },
    UPC: {
        name: "UPC-A",
        description: "12 chiffres, États-Unis/Canada",
        charset: "0-9 uniquement",
        length: 12,
        checksum: "Modulo 10",
        usage: "Marché nord-américain"
    },
    ITF14: {
        name: "ITF-14",
        description: "14 chiffres, emballages",
        charset: "0-9 uniquement",
        length: 14,
        checksum: "Modulo 10",
        usage: "Caisses, palettes, logistique"
    }
};

// Utility Functions
export const barcodeUtils = {
    // Generate random barcode for testing
    generateRandomBarcode: (format) => {
        switch (format) {
            case 'EAN13':
                return '376' + Math.random().toString().substr(2, 10);
            case 'EAN8':
                return '376' + Math.random().toString().substr(2, 5);
            case 'UPC':
                return Math.random().toString().substr(2, 12);
            case 'CODE39':
                return 'TEST-' + Math.random().toString(36).substr(2, 6).toUpperCase();
            case 'ITF14':
                return '0' + Math.random().toString().substr(2, 13);
            case 'CODE128':
            default:
                return 'APTIV-' + Math.random().toString(36).substr(2, 8).toUpperCase();
        }
    },

    // Validate barcode format
    validateBarcode: (code, format) => {
        const patterns = {
            EAN13: /^\d{13}$/,
            EAN8: /^\d{8}$/,
            UPC: /^\d{12}$/,
            CODE39: /^[A-Z0-9\-. $/+%]+$/,
            ITF14: /^\d{14}$/,
            CODE128: /^.{1,50}$/
        };
        return patterns[format] ? patterns[format].test(code) : false;
    },

    // Get product info by barcode
    getProductInfo: (code) => {
        const allProducts = [...aptivProducts, ...standardProducts, ...upcProducts];
        return allProducts.find(product => product.code === code);
    },

    // Search products by category
    searchByCategory: (category) => {
        return aptivProducts.filter(product => 
            product.category.toLowerCase().includes(category.toLowerCase())
        );
    }
};

// Export all data
export const allBarcodeData = {
    aptivProducts,
    standardProducts,
    upcProducts,
    internalCodes,
    packagingCodes,
    testData,
    sampleScanHistory,
    formatSpecs,
    barcodeUtils
};
