import React, { useState, useRef } from 'react';
import { ScanLine, Camera, Upload, CheckCircle, XCircle, Package, Search } from 'lucide-react';
import { useData } from '../../contexts/DataContext';
import { Product } from '../../types';

const Scanner: React.FC = () => {
  const { products } = useData();
  const [isScanning, setIsScanning] = useState(false);
  const [scannedCode, setScannedCode] = useState('');
  const [scanResult, setScanResult] = useState<Product | null>(null);
  const [scanHistory, setScanHistory] = useState<Array<{
    code: string;
    product: Product | null;
    timestamp: Date;
    success: boolean;
  }>>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleScan = (code: string) => {
    const foundProduct = products.find(p => 
      p.sku.toLowerCase() === code.toLowerCase() || 
      p.name.toLowerCase().includes(code.toLowerCase())
    );
    
    setScanResult(foundProduct || null);
    setScanHistory(prev => [{
      code,
      product: foundProduct || null,
      timestamp: new Date(),
      success: !!foundProduct
    }, ...prev.slice(0, 9)]); // Keep last 10 scans
  };

  const handleManualScan = () => {
    if (scannedCode.trim()) {
      handleScan(scannedCode.trim());
      setScannedCode('');
    }
  };

  const simulateBarcodeScan = () => {
    setIsScanning(true);
    // Simulate scanning delay
    setTimeout(() => {
      const randomProduct = products[Math.floor(Math.random() * products.length)];
      handleScan(randomProduct.sku);
      setIsScanning(false);
    }, 2000);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // In a real application, this would process the image to extract barcode
      // For demo purposes, we'll simulate finding a random product
      setIsScanning(true);
      setTimeout(() => {
        const randomProduct = products[Math.floor(Math.random() * products.length)];
        handleScan(randomProduct.sku);
        setIsScanning(false);
      }, 1500);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const getStockStatus = (product: Product) => {
    if (product.stockQuantity <= product.minStockLevel) {
      return { status: 'Rupture', color: 'text-red-600', bg: 'bg-red-100' };
    }
    if (product.stockQuantity <= product.minStockLevel * 2) {
      return { status: 'Faible', color: 'text-yellow-600', bg: 'bg-yellow-100' };
    }
    return { status: 'Disponible', color: 'text-green-600', bg: 'bg-green-100' };
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Scanner de Codes-Barres</h1>
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <ScanLine className="w-4 h-4" />
          <span>Scan automatique et manuel disponible</span>
        </div>
      </div>

      {/* Scanner Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Scanner un Produit</h2>
          
          <div className="space-y-4">
            {/* Camera Scan */}
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              {isScanning ? (
                <div className="space-y-4">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="text-gray-600">Scan en cours...</p>
                </div>
              ) : (
                <div className="space-y-4">
                  <Camera className="w-16 h-16 text-gray-400 mx-auto" />
                  <div>
                    <p className="text-gray-600 mb-4">Utilisez la caméra pour scanner un code-barres</p>
                    <button
                      onClick={simulateBarcodeScan}
                      className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 mx-auto"
                    >
                      <ScanLine className="w-5 h-5" />
                      <span>Démarrer le Scan</span>
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* File Upload */}
            <div className="border border-gray-300 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Ou télécharger une image</span>
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2"
                >
                  <Upload className="w-4 h-4" />
                  <span>Télécharger</span>
                </button>
              </div>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
              />
            </div>

            {/* Manual Input */}
            <div className="border border-gray-300 rounded-lg p-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Saisie manuelle du code
              </label>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={scannedCode}
                  onChange={(e) => setScannedCode(e.target.value)}
                  placeholder="Entrez le code SKU ou nom du produit"
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  onKeyPress={(e) => e.key === 'Enter' && handleManualScan()}
                />
                <button
                  onClick={handleManualScan}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
                >
                  <Search className="w-4 h-4" />
                  <span>Rechercher</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Scan Result */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Résultat du Scan</h2>
          
          {scanResult ? (
            <div className="space-y-4">
              <div className="flex items-center space-x-2 text-green-600">
                <CheckCircle className="w-5 h-5" />
                <span className="font-medium">Produit trouvé</span>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Nom du produit</label>
                  <p className="text-lg font-semibold text-gray-900">{scanResult.name}</p>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Code SKU</label>
                    <p className="text-sm text-gray-900">{scanResult.sku}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Catégorie</label>
                    <p className="text-sm text-gray-900">{scanResult.category}</p>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">Description</label>
                  <p className="text-sm text-gray-900">{scanResult.description}</p>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Prix unitaire</label>
                    <p className="text-lg font-semibold text-gray-900">{formatCurrency(scanResult.unitPrice)}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Stock disponible</label>
                    <div className="flex items-center space-x-2">
                      <span className="text-lg font-semibold text-gray-900">{scanResult.stockQuantity}</span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStockStatus(scanResult).bg} ${getStockStatus(scanResult).color}`}>
                        {getStockStatus(scanResult).status}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700">Valeur totale en stock</label>
                  <p className="text-lg font-semibold text-blue-600">
                    {formatCurrency(scanResult.stockQuantity * scanResult.unitPrice)}
                  </p>
                </div>
              </div>
            </div>
          ) : scanHistory.length > 0 && !scanHistory[0].success ? (
            <div className="space-y-4">
              <div className="flex items-center space-x-2 text-red-600">
                <XCircle className="w-5 h-5" />
                <span className="font-medium">Produit non trouvé</span>
              </div>
              <div className="bg-red-50 rounded-lg p-4">
                <p className="text-sm text-red-700">
                  Le code "{scanHistory[0].code}" ne correspond à aucun produit dans l'inventaire.
                </p>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <Package className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Aucun scan effectué</p>
              <p className="text-sm text-gray-400 mt-2">Utilisez l'un des méthodes de scan ci-contre</p>
            </div>
          )}
        </div>
      </div>

      {/* Scan History */}
      {scanHistory.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Historique des Scans</h2>
          
          <div className="space-y-3">
            {scanHistory.map((scan, index) => (
              <div key={index} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                <div className="flex items-center space-x-3">
                  {scan.success ? (
                    <CheckCircle className="w-5 h-5 text-green-500" />
                  ) : (
                    <XCircle className="w-5 h-5 text-red-500" />
                  )}
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {scan.product ? scan.product.name : `Code: ${scan.code}`}
                    </p>
                    <p className="text-xs text-gray-500">
                      {scan.timestamp.toLocaleString('fr-FR')}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  {scan.product && (
                    <div>
                      <p className="text-sm font-medium text-gray-900">{scan.product.sku}</p>
                      <p className="text-xs text-gray-500">{scan.product.stockQuantity} unités</p>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Scanner;