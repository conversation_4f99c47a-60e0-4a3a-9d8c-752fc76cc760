<!DOCTYPE html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Scanner de Code-Barres</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>

<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-search me-2"></i>
                            Test Scanner de Code-Barres
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="testCode" class="form-label">Code à tester:</label>
                            <div class="input-group">
                                <input type="text" id="testCode" class="form-control"
                                    placeholder="Entrez un code (ex: EK9-HAB-TAB3U)" value="EK9-HAB-TAB3U">
                                <button class="btn btn-primary" onclick="testSearch()">
                                    <i class="fas fa-search me-1"></i> Tester
                                </button>
                            </div>
                        </div>

                        <div class="mb-3">
                            <h6>Codes d'exemple à tester:</h6>

                            <div class="mb-2">
                                <small class="text-muted">Data Twist (Production):</small>
                                <div class="d-flex flex-wrap gap-2 mt-1">
                                    <button class="btn btn-outline-primary btn-sm"
                                        onclick="setTestCode('EK9-HAB-TAB3U')">EK9-HAB-TAB3U</button>
                                    <button class="btn btn-outline-primary btn-sm"
                                        onclick="setTestCode('EK9-HAB-TAB3V')">EK9-HAB-TAB3V</button>
                                    <button class="btn btn-outline-primary btn-sm"
                                        onclick="setTestCode('EK9-HAB-TAB3W')">EK9-HAB-TAB3W</button>
                                    <button class="btn btn-outline-primary btn-sm"
                                        onclick="setTestCode('EK9-HAB-TAB3X')">EK9-HAB-TAB3X</button>
                                </div>
                            </div>

                            <div class="mb-2">
                                <small class="text-muted">Squib-Commande (CODE128):</small>
                                <div class="d-flex flex-wrap gap-2 mt-1">
                                    <button class="btn btn-outline-success btn-sm"
                                        onclick="setTestCode('EK9-HAB-Brg40')">EK9-HAB-Brg40</button>
                                    <button class="btn btn-outline-success btn-sm"
                                        onclick="setTestCode('EK9-HAB-Brg41')">EK9-HAB-Brg41</button>
                                    <button class="btn btn-outline-success btn-sm"
                                        onclick="setTestCode('EK9-HAB-Brg42')">EK9-HAB-Brg42</button>
                                    <button class="btn btn-outline-success btn-sm"
                                        onclick="setTestCode('EK9-HAB-Brg43')">EK9-HAB-Brg43</button>
                                </div>
                            </div>
                        </div>

                        <div id="testResult" class="mt-4">
                            <!-- Results will appear here -->
                        </div>

                        <div class="mt-4">
                            <h6>Statistiques de la base de données:</h6>
                            <div id="dbStats" class="text-muted">
                                Chargement...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/data-loader.js"></script>
    <script>
        async function testSearch() {
            const code = document.getElementById('testCode').value.trim();
            const resultDiv = document.getElementById('testResult');

            if (!code) {
                resultDiv.innerHTML = '<div class="alert alert-warning">Veuillez entrer un code à tester.</div>';
                return;
            }

            resultDiv.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Recherche...</span>
                    </div>
                    <p class="mt-2">Recherche du code: <strong>${code}</strong></p>
                </div>
            `;

            try {
                const result = await searchBarcode(code);

                if (result) {
                    resultDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>Code trouvé!</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Code:</strong> ${result.code}</p>
                                    <p><strong>Nom:</strong> ${result.name}</p>
                                    <p><strong>Format:</strong> ${result.format}</p>
                                    <p><strong>Catégorie:</strong> ${result.category}</p>
                                    <p><strong>Section:</strong> ${result.section}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Description:</strong> ${result.description || 'N/A'}</p>
                                    <p><strong>Machine:</strong> ${result.machine || 'N/A'}</p>
                                    <p><strong>Statut:</strong> ${result.status || 'N/A'}</p>
                                    <p><strong>Ordre:</strong> ${result.order || 'N/A'}</p>
                                    <p><strong>Reste:</strong> ${result.remaining || 'N/A'}</p>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Code non trouvé</h6>
                            <p class="mb-0">Le code <strong>${code}</strong> n'existe pas dans la base de données.</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-times me-2"></i>Erreur</h6>
                        <p class="mb-0">Erreur lors de la recherche: ${error.message}</p>
                    </div>
                `;
            }
        }

        function setTestCode(code) {
            document.getElementById('testCode').value = code;
        }

        async function loadStats() {
            try {
                const database = await loadFullBarcodeDatabase();
                const csvData = await loadBarcodeData();

                if (database && csvData) {
                    const stats = {
                        totalCSV: csvData.length,
                        aptivProducts: database.aptivProducts?.length || 0,
                        standardProducts: database.standardProducts?.length || 0,
                        internalCodes: database.internalCodes?.length || 0,
                        packagingCodes: database.packagingCodes?.length || 0,
                        testData: database.testData?.length || 0
                    };

                    document.getElementById('dbStats').innerHTML = `
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Total CSV:</strong> ${stats.totalCSV} enregistrements</p>
                                <p><strong>Produits Aptiv:</strong> ${stats.aptivProducts}</p>
                                <p><strong>Produits Standards:</strong> ${stats.standardProducts}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Codes Internes:</strong> ${stats.internalCodes}</p>
                                <p><strong>Codes Emballage:</strong> ${stats.packagingCodes}</p>
                                <p><strong>Données Test:</strong> ${stats.testData}</p>
                            </div>
                        </div>
                    `;
                } else {
                    document.getElementById('dbStats').innerHTML = '<p class="text-danger">Erreur lors du chargement des statistiques.</p>';
                }
            } catch (error) {
                document.getElementById('dbStats').innerHTML = `<p class="text-danger">Erreur: ${error.message}</p>`;
            }
        }

        // Load stats on page load
        document.addEventListener('DOMContentLoaded', loadStats);

        // Allow Enter key to trigger search
        document.getElementById('testCode').addEventListener('keypress', function (e) {
            if (e.key === 'Enter') {
                testSearch();
            }
        });


    </script>
</body>

</html>