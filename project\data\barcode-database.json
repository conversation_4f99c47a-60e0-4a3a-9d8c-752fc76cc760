{"aptivProducts": [{"code": "APTIV-CBL-HT-001", "format": "CODE128", "name": "Câble Haute Tension 16AWG", "category": "<PERSON><PERSON><PERSON>", "description": "Câble haute tension pour systèmes automobiles, 16AWG, isolation renforcée", "price": 12.5, "stock": 2450, "minStock": 500, "supplier": "Nexans France", "location": "A1-B2-C3", "lastUpdated": "2024-01-20T10:30:00Z"}, {"code": "APTIV-CON-IP67-004", "format": "CODE128", "name": "Connecteur Étanche IP67", "category": "Connecteurs", "description": "Connecteur étanche IP67 pour environnement automobile", "price": 8.75, "stock": 1250, "minStock": 200, "supplier": "Prysmian Group", "location": "B2-C3-D4", "lastUpdated": "2024-01-19T14:15:00Z"}, {"code": "APTIV-FCX-ECU-007", "format": "CODE128", "name": "Faisceau de Câbles ECU", "category": "<PERSON><PERSON><PERSON><PERSON>", "description": "Faisceau principal pour unité de contrôle électronique", "price": 45.0, "stock": 875, "minStock": 100, "supplier": "Aptiv Internal", "location": "C3-D4-E5", "lastUpdated": "2024-01-18T09:45:00Z"}, {"code": "APTIV-SEN-TEMP-012", "format": "CODE128", "name": "Capteur <PERSON>", "category": "Capteurs", "description": "Capteur de température haute précision pour moteurs", "price": 25.3, "stock": 650, "minStock": 150, "supplier": "<PERSON><PERSON>", "location": "D4-E5-F6", "lastUpdated": "2024-01-17T16:20:00Z"}, {"code": "APTIV-REL-PWR-008", "format": "CODE128", "name": "Relais de Puissance 40A", "category": "<PERSON><PERSON><PERSON>", "description": "Relais de puissance pour systèmes électriques automobiles", "price": 18.9, "stock": 320, "minStock": 80, "supplier": "TE Connectivity", "location": "E5-F6-G7", "lastUpdated": "2024-01-16T11:30:00Z"}, {"code": "APTIV-FUS-MINI-025", "format": "CODE128", "name": "Fusible Mini 25A", "category": "Fusibles", "description": "Fusible mini format 25A pour protection circuits", "price": 2.15, "stock": 5000, "minStock": 1000, "supplier": "Littelfuse", "location": "F6-G7-H8", "lastUpdated": "2024-01-15T13:45:00Z"}, {"code": "APTIV-LED-IND-003", "format": "CODE128", "name": "LED Indicateur Rouge", "category": "Indicateurs", "description": "LED indicateur rouge 12V pour tableau de bord", "price": 3.25, "stock": 2800, "minStock": 500, "supplier": "<PERSON><PERSON><PERSON>", "location": "G7-H8-I9", "lastUpdated": "2024-01-14T08:15:00Z"}, {"code": "APTIV-SW-PUSH-019", "format": "CODE128", "name": "Bouton Poussoir Étanche", "category": "Commutateurs", "description": "Bouton poussoir étanche IP65 pour applications extérieures", "price": 12.8, "stock": 450, "minStock": 100, "supplier": "C&K Components", "location": "H8-I9-J10", "lastUpdated": "2024-01-13T15:30:00Z"}], "standardProducts": [{"code": "3760123456789", "format": "EAN13", "name": "Kit de Câblage Automobile Premium", "brand": "Aptiv", "category": "Kits", "country": "France", "price": 89.99, "description": "Kit complet de câblage pour véhicules premium"}, {"code": "3760234567890", "format": "EAN13", "name": "Connecteur Multi-Pin 24 Voies", "brand": "Aptiv", "category": "Connecteurs", "country": "France", "price": 34.5, "description": "Connecteur haute performance 24 voies"}, {"code": "3760345678901", "format": "EAN13", "name": "Capteur de Position Intelligent", "brand": "Aptiv", "category": "Capteurs", "country": "France", "price": 67.8, "description": "Capteur de position avec intelligence embarquée"}, {"code": "3760456789012", "format": "EAN13", "name": "Module de Contrôle ECU", "brand": "Aptiv", "category": "<PERSON><PERSON><PERSON>", "country": "France", "price": 245.0, "description": "Module de contrôle électronique avancé"}, {"code": "3760567890123", "format": "EAN13", "name": "Faisceau de Câbles Hybride", "brand": "Aptiv", "category": "<PERSON><PERSON><PERSON><PERSON>", "country": "France", "price": 156.75, "description": "Faisceau spécialisé pour véhicules hybrides"}], "upcProducts": [{"code": "012345678905", "format": "UPC", "name": "Automotive Harness Kit Pro", "brand": "Aptiv", "region": "North America", "category": "<PERSON><PERSON><PERSON>", "price": 125.99, "description": "Professional grade automotive harness kit"}, {"code": "023456789016", "format": "UPC", "name": "High Voltage Cable Assembly", "brand": "Aptiv", "region": "North America", "category": "Cables", "price": 89.5, "description": "High voltage cable assembly for electric vehicles"}, {"code": "034567890127", "format": "UPC", "name": "Smart Sensor Module V2", "brand": "Aptiv", "region": "North America", "category": "Sensors", "price": 199.99, "description": "Next generation smart sensor module"}], "internalCodes": [{"code": "LOT-2024-001", "format": "CODE39", "type": "Lot Number", "description": "Lot de production Janvier 2024", "quantity": 1000, "productionDate": "2024-01-15", "expiryDate": "2026-01-15", "status": "Active"}, {"code": "QC-PASS-2024-A", "format": "CODE39", "type": "Quality Control", "description": "Contrôle qualité réussi - Série A", "inspector": "<PERSON>", "testDate": "2024-01-20", "certificationLevel": "Premium"}, {"code": "SHIP-FR-001", "format": "CODE39", "type": "Shipping", "description": "Expédition France - Lot 001", "destination": "Renault Flins", "shipDate": "2024-01-25", "carrier": "DHL Express", "trackingNumber": "1234567890"}, {"code": "MAINT-EQ-012", "format": "CODE39", "type": "Maintenance", "description": "Équipement de maintenance 012", "location": "Atelier B", "lastMaintenance": "2024-01-15", "nextMaintenance": "2024-06-15", "status": "Operational"}, {"code": "RMA-2024-0001", "format": "CODE39", "type": "Return", "description": "Retour client - Défaut mineur", "returnDate": "2024-01-22", "reason": "Défaut cosmétique", "status": "Processing"}], "packagingCodes": [{"code": "01234567890128", "format": "ITF14", "type": "Case Code", "description": "Caisse de 50 connecteurs IP67", "innerCode": "3760234567890", "quantity": 50, "weight": "2.5kg", "dimensions": "30x20x15cm"}, {"code": "02345678901239", "format": "ITF14", "type": "Pallet Code", "description": "Palette de 20 caisses connecteurs", "caseQuantity": 20, "totalItems": 1000, "weight": "50kg", "dimensions": "120x80x150cm"}, {"code": "03456789012340", "format": "ITF14", "type": "Container Code", "description": "Conteneur de 100 palettes", "palletQuantity": 100, "totalItems": 100000, "weight": "5000kg", "containerType": "20ft"}], "testData": [{"code": "TEST-BASIC-001", "format": "CODE128", "description": "Code de test basique pour développement", "category": "Test"}, {"code": "1234567890123", "format": "EAN13", "description": "EAN-13 de test standard", "category": "Test"}, {"code": "12345678", "format": "EAN8", "description": "EAN-8 de test compact", "category": "Test"}, {"code": "HELLO WORLD", "format": "CODE39", "description": "Texte simple Code 39 pour test", "category": "Test"}, {"code": "123456789012", "format": "UPC", "description": "UPC-A de test pour marché US", "category": "Test"}, {"code": "APTIV-TEST-999", "format": "CODE128", "description": "Code de test Aptiv spécifique", "category": "Test"}], "sampleScanHistory": [{"code": "APTIV-CBL-HT-001", "format": "CODE128", "timestamp": "2024-01-20T10:30:00Z", "user": "<PERSON>", "action": "Inventory Check", "location": "Warehouse A", "result": "Stock Updated"}, {"code": "3760123456789", "format": "EAN13", "timestamp": "2024-01-20T11:15:00Z", "user": "<PERSON>", "action": "Quality Control", "location": "QC Station 2", "result": "Passed"}, {"code": "LOT-2024-001", "format": "CODE39", "timestamp": "2024-01-20T14:45:00Z", "user": "<PERSON>", "action": "Production Tracking", "location": "Production Line 1", "result": "Lot Validated"}, {"code": "SHIP-FR-001", "format": "CODE39", "timestamp": "2024-01-20T16:20:00Z", "user": "<PERSON>", "action": "Shipping Scan", "location": "Shipping Dock", "result": "Package Dispatched"}], "formatSpecs": {"CODE128": {"name": "Code 128", "description": "Alphanumeric, haute densité", "charset": "ASCII complet (128 caractères)", "maxLength": 50, "checksum": "Modulo 103", "usage": "Produits internes, traçabilité, logistique", "advantages": ["Haute densité", "Charset complet", "Fiable"], "applications": ["Inventaire", "Traçabilité", "Logistique interne"]}, "CODE39": {"name": "Code 39", "description": "Alphanumeric, largement utilisé", "charset": "A-Z, 0-9, -. $/+%", "maxLength": 43, "checksum": "Optionnel (Modulo 43)", "usage": "Identification interne, maintenance, lots", "advantages": ["Simple", "Pas de checksum obligatoire", "<PERSON><PERSON><PERSON>"], "applications": ["Maintenance", "Lots de production", "Identification équipements"]}, "EAN13": {"name": "EAN-13", "description": "13 chiffres, produits de consommation", "charset": "0-9 uniquement", "length": 13, "checksum": "Modulo 10", "usage": "Produits commerciaux, retail, distribution", "advantages": ["Standard mondial", "Reconnaissance universelle", "Retail"], "applications": ["Produits finis", "Distribution", "Vente au détail"]}, "EAN8": {"name": "EAN-8", "description": "8 chiffres, petits produits", "charset": "0-9 uniquement", "length": 8, "checksum": "Modulo 10", "usage": "Petits produits, espace limité", "advantages": ["Compact", "Standard", "Espace réduit"], "applications": ["Petits composants", "Étiquettes réduites", "Pièces détachées"]}, "UPC": {"name": "UPC-A", "description": "12 chiffres, États-Unis/Canada", "charset": "0-9 uniquement", "length": 12, "checksum": "Modulo 10", "usage": "<PERSON><PERSON>am<PERSON><PERSON>", "advantages": ["Standard US/Canada", "Retail", "Distribution"], "applications": ["Export US/Canada", "Produits retail", "Distribution Amérique du Nord"]}, "ITF14": {"name": "ITF-14", "description": "14 chiffres, emballages et logistique", "charset": "0-9 uniquement", "length": 14, "checksum": "Modulo 10", "usage": "Caisses, palettes, logistique", "advantages": ["Logistique", "Emballages", "Traçabilité supply chain"], "applications": ["Caisses", "Palettes", "Conteneurs", "Logistique"]}}}