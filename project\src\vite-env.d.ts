/// <reference types="vite/client" />

// JsBarcode global declaration
declare global {
    interface Window {
        JsBarcode: any;
    }
}

// JsBarcode module declaration
declare module 'jsbarcode' {
    interface JsBarcodeOptions {
        format?: string;
        width?: number;
        height?: number;
        displayValue?: boolean;
        fontSize?: number;
        margin?: number;
        background?: string;
        lineColor?: string;
        text?: string;
        textAlign?: string;
        textPosition?: string;
        textMargin?: number;
        fontOptions?: string;
        font?: string;
        valid?: (valid: boolean) => void;
    }

    function JsBarcode(element: Element | string, text: string, options?: JsBarcodeOptions): void;

    export = JsBarcode;
}
