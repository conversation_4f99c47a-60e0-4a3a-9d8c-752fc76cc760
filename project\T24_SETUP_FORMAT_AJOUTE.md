# T24 SETUP - Format de Scan Ajouté

## 🎯 Nouvelle Fonctionnalité

✅ **Format T24 SETUP** identique au document papier  
✅ **Affichage automatique** lors du scan d'un code unicos  
✅ **Bouton T24 Setup** dans l'en-tête avec compteur  
✅ **Impression directe** du format T24  
✅ **Export JSON** des données T24  
✅ **Liste complète** des entrées T24  
✅ **Gestion persistante** dans localStorage  

## 📋 Interface T24 SETUP

### **Boutons d'Accès:**
```
┌─────────────────────────────────────────────────────────┐
│ 🔄 Scanner Codes Unicos - Data Twist                   │
│    [⚙️ Scan Setup] [📋 T24 Setup 3] [📊] [❓]          │
└─────────────────────────────────────────────────────────┘
```

### **Format T24 Affiché lors du Scan:**
```
┌─────────────────────────────────────────────────────────┐
│                    T w i s t                           │
│                    A r e a                             │
│                                                         │
│         2025-07-17 09:37 | shift: MATIN               │
│                                                         │
│                   T24 SETUP                            │
│                   ________                             │
│                                                         │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ Unico    │ Groupe │ Ordre │ Reste │ Date           │ │
│ ├─────────────────────────────────────────────────────┤ │
│ │ EK9-PPL- │ TWIST  │  400  │  400  │ 17/07          │ │
│ │ TRA1T    │        │       │       │ 09:37          │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
│    [🖨️ Imprimer T24] [📥 Exporter] [➕ Ajouter Setup] │
└─────────────────────────────────────────────────────────┘
```

## 🔧 Fonctionnalités T24

### **1. Affichage Automatique**
- **Déclenchement**: Lors du scan d'un code unicos
- **Format**: Identique au document papier T24 SETUP
- **Données**: Code, groupe, ordre, reste, date/heure
- **Style**: Police Courier New, bordures, mise en page authentique

### **2. Actions Disponibles**

#### **🖨️ Imprimer T24:**
- **Fonction**: `printT24Setup(code)`
- **Résultat**: Ouvre fenêtre d'impression avec format T24
- **Contenu**: Document complet prêt à imprimer
- **Style**: Optimisé pour impression A4

#### **📥 Exporter:**
- **Fonction**: `exportT24Setup(code)`
- **Format**: Fichier JSON avec métadonnées
- **Nom**: `T24-SETUP-[CODE]-[DATE].json`
- **Contenu**: Données structurées + timestamp

#### **➕ Ajouter au Setup:**
- **Fonction**: `addToT24Setup(code)`
- **Action**: Ajoute à la liste persistante
- **Stockage**: localStorage
- **Limite**: 50 entrées maximum

### **3. Liste T24 Setup**

#### **Bouton T24 Setup:**
```
[📋 T24 Setup 3] ← Compteur d'entrées
```

#### **Modal Liste Complète:**
```
┌─────────────────────────────────────────────────────────┐
│ 📋 T24 SETUP - Liste Complète (3 entrées)        [✕]  │
├─────────────────────────────────────────────────────────┤
│                                                         │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ Unico      │ Groupe │ Ordre │ Reste │ Date/Heure │🗑️│ │
│ ├─────────────────────────────────────────────────────┤ │
│ │ EK9-PPL-   │ TWIST  │  400  │  400  │ 17/07      │🗑️│ │
│ │ TRA1T      │        │       │       │ 09:37      │  │ │
│ ├─────────────────────────────────────────────────────┤ │
│ │ EK9-PDB-   │ TWIST  │  800  │  800  │ 17/07      │🗑️│ │
│ │ TAB2C      │        │       │       │ 08:06      │  │ │
│ └─────────────────────────────────────────────────────┘ │
│                                                         │
├─────────────────────────────────────────────────────────┤
│  [📥 Exporter Tout] [🗑️ Vider Liste] [Fermer]         │
└─────────────────────────────────────────────────────────┘
```

## 📊 Structure des Données

### **Format JSON Exporté:**
```json
{
  "title": "T24 SETUP",
  "area": "Twist Area",
  "date": "2025-07-17",
  "time": "09:37",
  "shift": "MATIN",
  "entries": [
    {
      "unico": "EK9-PPL-TRA1T",
      "groupe": "TWIST",
      "ordre": "400",
      "reste": "400",
      "date": "17/07",
      "time": "09:37"
    }
  ],
  "generated": "2025-07-17T09:37:00.000Z",
  "version": "1.0"
}
```

### **Stockage localStorage:**
```javascript
// Clé: 't24SetupList'
[
  {
    "id": 1721203020000,
    "unico": "EK9-PPL-TRA1T",
    "groupe": "TWIST",
    "ordre": "400",
    "reste": "400",
    "date": "2025-07-17",
    "time": "09:37",
    "shift": "MATIN",
    "addedAt": "2025-07-17T09:37:00.000Z",
    "status": "Active"
  }
]
```

## 🎨 Styles et Apparence

### **Format T24 Container:**
- **Police**: Courier New (monospace)
- **Arrière-plan**: #f8f9fa avec bordure
- **Titre**: "T w i s t / A r e a" avec espacement
- **Tableau**: Bordures 2px, style industriel
- **Couleurs**: Ligne scannée en vert (#d4edda)

### **Tableau Setup:**
```css
.setup-table {
  border-collapse: collapse;
  font-family: 'Courier New', monospace;
}

.setup-table th, td {
  border: 2px solid #333;
  padding: 12px 8px;
  text-align: center;
}

.scanned-row {
  background-color: #d4edda;
  border-left: 4px solid #28a745;
}
```

### **Responsive Design:**
- **Desktop**: Tableau complet, boutons horizontaux
- **Tablet**: Tableau adapté, police réduite
- **Mobile**: Boutons empilés, format compact

## 🔄 Flux d'Utilisation

### **Scan → T24 Automatique:**
1. **Scanner** un code unicos (ex: EK9-PPL-TRA1T)
2. **Voir** le format T24 SETUP s'afficher automatiquement
3. **Choisir** une action: Imprimer / Exporter / Ajouter

### **Gestion de la Liste:**
1. **Cliquer** "T24 Setup" (avec compteur)
2. **Voir** la liste complète des entrées
3. **Gérer**: Supprimer / Exporter tout / Vider

### **Impression:**
1. **Cliquer** "Imprimer T24" sur un scan
2. **Voir** la fenêtre d'impression s'ouvrir
3. **Imprimer** le document formaté

### **Export:**
1. **Export individuel**: Depuis un scan
2. **Export complet**: Depuis la liste T24
3. **Fichier JSON**: Téléchargé automatiquement

## 📱 Fonctionnalités Avancées

### **Parsing de Code Unicos:**
```javascript
// EK9-PPL-TRA1T → 
{
  prefix: 'EK9',
  groupe: 'PPL', 
  type: 'TRA1T',
  fullCode: 'EK9-PPL-TRA1T'
}
```

### **Gestion des Doublons:**
- **Détection**: Vérification par code unicos
- **Action**: Mise à jour de l'entrée existante
- **Message**: Notification appropriée

### **Limite de Stockage:**
- **Maximum**: 50 entrées dans la liste
- **Gestion**: Suppression automatique des plus anciennes
- **Performance**: Optimisée pour usage quotidien

### **Persistance:**
- **Stockage**: localStorage du navigateur
- **Survie**: Entre les sessions
- **Sauvegarde**: Automatique à chaque action

## 🎯 Cas d'Usage

### **Production Quotidienne:**
1. **Scanner** les codes unicos de production
2. **Voir** immédiatement le format T24
3. **Imprimer** pour documentation papier
4. **Accumuler** dans la liste T24

### **Suivi de Shift:**
1. **Début de shift**: Vider la liste T24
2. **Pendant production**: Scanner et ajouter
3. **Fin de shift**: Exporter la liste complète
4. **Documentation**: Impression des T24 individuels

### **Audit et Traçabilité:**
1. **Export JSON**: Données structurées
2. **Horodatage**: Précis à la seconde
3. **Historique**: Persistant localement
4. **Format standard**: Compatible avec systèmes

## 🎉 Résultat Final

**Format T24 SETUP identique au document papier!**

✅ **Affichage automatique** lors du scan unicos  
✅ **Format authentique** avec police Courier New  
✅ **3 actions** par scan (Imprimer/Exporter/Ajouter)  
✅ **Liste persistante** avec compteur en temps réel  
✅ **Modal de gestion** complète des entrées  
✅ **Export JSON** individuel et complet  
✅ **Impression optimisée** pour format A4  
✅ **Design responsive** pour tous écrans  
✅ **Gestion des doublons** intelligente  
✅ **Limite de 50 entrées** pour performance  

### **Test Rapide:**
1. **Scanner** un code unicos (ex: EK9-PPL-TRA1T)
2. **Voir** le format T24 SETUP s'afficher
3. **Cliquer** "Ajouter au Setup"
4. **Voir** le compteur T24 Setup passer à 1
5. **Cliquer** "T24 Setup" pour voir la liste

**Le scanner affiche maintenant le format T24 SETUP exactement comme le document papier!** 📋✅🎯
