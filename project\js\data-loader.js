// Charger les données depuis le fichier CSV
async function loadBarcodeData() {
    try {
        const response = await fetch('data/barcode-data-simple.csv');
        const csvText = await response.text();

        // Parser le CSV
        const lines = csvText.split('\n');
        const headers = lines[0].split(',');
        const data = [];

        for (let i = 1; i < lines.length; i++) {
            if (lines[i].trim()) {
                const values = lines[i].split(',');
                const item = {};
                headers.forEach((header, index) => {
                    item[header.trim()] = values[index] ? values[index].trim() : '';
                });
                data.push(item);
            }
        }

        return data;
    } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        return [];
    }
}

// Charger les données depuis la base de données JSON complète
async function loadFullBarcodeDatabase() {
    try {
        const response = await fetch('data/barcode-database.json');
        const database = await response.json();
        return database;
    } catch (error) {
        console.error('Erreur lors du chargement de la base de données:', error);
        return null;
    }
}

// Rechercher un code-barres dans la base de données
async function searchBarcode(code) {
    try {
        const database = await loadFullBarcodeDatabase();
        if (!database) return null;

        // Rechercher dans toutes les sections
        const sections = [
            'aptivProducts',
            'standardProducts',
            'upcProducts',
            'internalCodes',
            'packagingCodes',
            'testData'
        ];

        for (const section of sections) {
            if (database[section]) {
                const found = database[section].find(item => item.code === code);
                if (found) {
                    return {
                        ...found,
                        section: section
                    };
                }
            }
        }

        return null;
    } catch (error) {
        console.error('Erreur lors de la recherche:', error);
        return null;
    }
}

// Filtrer les données par catégorie
function filterByCategory(data, category) {
    if (!category || category === 'all') return data;
    return data.filter(item => item.Category === category);
}

// Filtrer les données par format de code-barres
function filterByFormat(data, format) {
    if (!format || format === 'all') return data;
    return data.filter(item => item.Format === format);
}

// Filtrer les données par statut
function filterByStatus(data, status) {
    if (!status || status === 'all') return data;
    return data.filter(item => item.Status === status);
}

// Rechercher dans les données par texte
function searchInData(data, searchText) {
    if (!searchText) return data;

    const text = searchText.toLowerCase();
    return data.filter(item =>
        item.Code.toLowerCase().includes(text) ||
        item.Name.toLowerCase().includes(text) ||
        item.Description.toLowerCase().includes(text) ||
        item.Category.toLowerCase().includes(text) ||
        item.Supplier.toLowerCase().includes(text)
    );
}

// Obtenir les statistiques des données
function getDataStatistics(data) {
    const stats = {
        total: data.length,
        byCategory: {},
        byFormat: {},
        byStatus: {},
        lowStock: 0,
        totalValue: 0
    };

    data.forEach(item => {
        // Par catégorie
        stats.byCategory[item.Category] = (stats.byCategory[item.Category] || 0) + 1;

        // Par format
        stats.byFormat[item.Format] = (stats.byFormat[item.Format] || 0) + 1;

        // Par statut
        stats.byStatus[item.Status] = (stats.byStatus[item.Status] || 0) + 1;

        // Stock faible
        const stock = parseInt(item.Stock) || 0;
        const minStock = parseInt(item.MinStock) || 0;
        if (stock > 0 && minStock > 0 && stock <= minStock) {
            stats.lowStock++;
        }

        // Valeur totale
        const price = parseFloat(item.Price) || 0;
        if (price > 0 && stock > 0) {
            stats.totalValue += price * stock;
        }
    });

    return stats;
}

// Enregistrer un scan de code-barres
async function recordBarcodeScan(code, user, action, location) {
    try {
        const scanRecord = {
            code: code,
            timestamp: new Date().toISOString(),
            user: user || 'Anonymous',
            action: action || 'Scan',
            location: location || 'Unknown',
            result: 'Scanned'
        };

        // Dans un vrai système, ceci serait envoyé à un serveur
        console.log('Scan enregistré:', scanRecord);

        // Simuler l'enregistrement local
        const scans = JSON.parse(localStorage.getItem('barcodeScanHistory') || '[]');
        scans.push(scanRecord);

        // Garder seulement les 100 derniers scans
        if (scans.length > 100) {
            scans.splice(0, scans.length - 100);
        }

        localStorage.setItem('barcodeScanHistory', JSON.stringify(scans));

        return scanRecord;
    } catch (error) {
        console.error('Erreur lors de l\'enregistrement du scan:', error);
        return null;
    }
}

// Obtenir l'historique des scans
function getScanHistory() {
    try {
        return JSON.parse(localStorage.getItem('barcodeScanHistory') || '[]');
    } catch (error) {
        console.error('Erreur lors de la récupération de l\'historique:', error);
        return [];
    }
}