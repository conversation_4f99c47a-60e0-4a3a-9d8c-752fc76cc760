// JavaScript function to load CSV barcode data into web application
// Aptiv - Twisting Monitoring Tool

class BarcodeDataLoader {
    constructor() {
        this.data = [];
        this.isLoaded = false;
    }

    // Load CSV data from file
    async loadFromCSV(csvFilePath = 'data/barcode-data-simple.csv') {
        try {
            const response = await fetch(csvFilePath);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const csvText = await response.text();
            this.data = this.parseCSV(csvText);
            this.isLoaded = true;
            
            console.log(`✅ Loaded ${this.data.length} barcode records`);
            return this.data;
        } catch (error) {
            console.error('Error loading CSV data:', error);
            // Fallback to embedded data
            this.loadEmbeddedData();
            return this.data;
        }
    }

    // Parse CSV text into JavaScript objects
    parseCSV(csvText) {
        const lines = csvText.trim().split('\n');
        const headers = lines[0].split(',');
        const data = [];

        for (let i = 1; i < lines.length; i++) {
            const values = lines[i].split(',');
            const record = {};
            
            headers.forEach((header, index) => {
                record[header.trim()] = values[index] ? values[index].trim() : '';
            });
            
            data.push(record);
        }

        return data;
    }

    // Fallback embedded data (subset of full data)
    loadEmbeddedData() {
        this.data = [
            {
                Code: "APTIV-CBL-HT-001",
                Format: "CODE128",
                Name: "Cable Haute Tension 16AWG",
                Category: "Cables",
                Description: "Cable haute tension pour systemes automobiles",
                Price: "12.50",
                Stock: "2450",
                MinStock: "500",
                Supplier: "Nexans France",
                Location: "A1-B2-C3",
                Status: "Active"
            },
            {
                Code: "APTIV-CON-IP67-004",
                Format: "CODE128",
                Name: "Connecteur Etanche IP67",
                Category: "Connecteurs",
                Description: "Connecteur etanche IP67 pour environnement automobile",
                Price: "8.75",
                Stock: "1250",
                MinStock: "200",
                Supplier: "Prysmian Group",
                Location: "B2-C3-D4",
                Status: "Active"
            },
            {
                Code: "3760123456789",
                Format: "EAN13",
                Name: "Kit de Cablage Automobile Premium",
                Category: "Kits",
                Description: "Kit complet de cablage pour vehicules premium",
                Price: "89.99",
                Stock: "125",
                MinStock: "25",
                Supplier: "Aptiv France",
                Location: "P16-Q17-R18",
                Status: "Active"
            },
            {
                Code: "LOT-2024-001",
                Format: "CODE39",
                Name: "Lot de production Janvier 2024",
                Category: "Lots",
                Description: "Lot de production Janvier 2024",
                Price: "",
                Stock: "",
                MinStock: "",
                Supplier: "",
                Location: "Production Line 1",
                Status: "Active"
            },
            {
                Code: "QC-PASS-2024-A",
                Format: "CODE39",
                Name: "Controle qualite reussi - Serie A",
                Category: "Quality Control",
                Description: "Controle qualite reussi - Serie A",
                Price: "",
                Stock: "",
                MinStock: "",
                Supplier: "",
                Location: "QC Station 1",
                Status: "Certified"
            }
        ];
        
        this.isLoaded = true;
        console.log(`✅ Loaded ${this.data.length} embedded barcode records`);
    }

    // Search functions
    findByCode(code) {
        return this.data.find(item => item.Code === code);
    }

    findByCategory(category) {
        return this.data.filter(item => 
            item.Category.toLowerCase().includes(category.toLowerCase())
        );
    }

    findByFormat(format) {
        return this.data.filter(item => item.Format === format);
    }

    findBySupplier(supplier) {
        return this.data.filter(item => 
            item.Supplier && item.Supplier.toLowerCase().includes(supplier.toLowerCase())
        );
    }

    // Get low stock items
    getLowStockItems() {
        return this.data.filter(item => {
            const stock = parseFloat(item.Stock);
            const minStock = parseFloat(item.MinStock);
            return !isNaN(stock) && !isNaN(minStock) && stock <= minStock;
        });
    }

    // Get random sample
    getRandomSample() {
        if (this.data.length === 0) return null;
        const randomIndex = Math.floor(Math.random() * this.data.length);
        return this.data[randomIndex];
    }

    // Get statistics
    getStatistics() {
        const stats = {
            total: this.data.length,
            byFormat: {},
            byCategory: {},
            byStatus: {},
            lowStock: this.getLowStockItems().length
        };

        this.data.forEach(item => {
            // Count by format
            stats.byFormat[item.Format] = (stats.byFormat[item.Format] || 0) + 1;
            
            // Count by category
            stats.byCategory[item.Category] = (stats.byCategory[item.Category] || 0) + 1;
            
            // Count by status
            stats.byStatus[item.Status] = (stats.byStatus[item.Status] || 0) + 1;
        });

        return stats;
    }

    // Export functions
    exportToJSON() {
        return JSON.stringify(this.data, null, 2);
    }

    exportToCSV() {
        if (this.data.length === 0) return '';
        
        const headers = Object.keys(this.data[0]);
        const csvRows = [headers.join(',')];
        
        this.data.forEach(item => {
            const values = headers.map(header => item[header] || '');
            csvRows.push(values.join(','));
        });
        
        return csvRows.join('\n');
    }

    // Integration with existing barcode system
    updateBarcodeDatabase() {
        if (typeof window !== 'undefined' && window.barcodeDatabase) {
            // Update the existing barcodeDatabase object
            window.barcodeDatabase.csvData = this.data;
            
            // Update sample data arrays
            const aptivProducts = this.findByCategory('Cables')
                .concat(this.findByCategory('Connecteurs'))
                .concat(this.findByCategory('Capteurs'));
            
            const standardProducts = this.findByFormat('EAN13')
                .concat(this.findByFormat('UPC'));
            
            const internalCodes = this.findByFormat('CODE39');
            
            if (window.barcodeDatabase.aptivProducts) {
                window.barcodeDatabase.aptivProducts = aptivProducts;
            }
            if (window.barcodeDatabase.standardProducts) {
                window.barcodeDatabase.standardProducts = standardProducts;
            }
            if (window.barcodeDatabase.internalCodes) {
                window.barcodeDatabase.internalCodes = internalCodes;
            }
            
            console.log('✅ Updated existing barcode database');
        }
    }
}

// Global instance
const barcodeDataLoader = new BarcodeDataLoader();

// Auto-load data when script is included
if (typeof window !== 'undefined') {
    window.addEventListener('DOMContentLoaded', async () => {
        await barcodeDataLoader.loadFromCSV();
        barcodeDataLoader.updateBarcodeDatabase();
        
        // Dispatch custom event when data is loaded
        window.dispatchEvent(new CustomEvent('barcodeDataLoaded', {
            detail: {
                data: barcodeDataLoader.data,
                stats: barcodeDataLoader.getStatistics()
            }
        }));
    });
    
    // Make available globally
    window.barcodeDataLoader = barcodeDataLoader;
}

// Node.js export
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BarcodeDataLoader;
}

// Usage examples:
/*
// Wait for data to load
window.addEventListener('barcodeDataLoaded', (event) => {
    console.log('Barcode data loaded:', event.detail.stats);
    
    // Use the data
    const aptivProducts = barcodeDataLoader.findByCategory('Cables');
    const lowStock = barcodeDataLoader.getLowStockItems();
    const randomSample = barcodeDataLoader.getRandomSample();
});

// Or use directly after page load
setTimeout(() => {
    if (barcodeDataLoader.isLoaded) {
        const product = barcodeDataLoader.findByCode('APTIV-CBL-HT-001');
        console.log('Found product:', product);
    }
}, 1000);
*/
