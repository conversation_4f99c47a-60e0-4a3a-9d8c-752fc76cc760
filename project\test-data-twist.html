<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Data Twist - Scanner</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h4 class="mb-0">
                            <i class="fas fa-sync-alt me-2"></i>
                            Test Data Twist - Codes Unicos
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>À propos</h6>
                            <p class="mb-0">Cette page permet de tester les codes unicos du fichier Data twist.xlsx dans le scanner de code-barres.</p>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h5>🎯 Codes de Test Data Twist</h5>
                                <div class="list-group">
                                    <button class="list-group-item list-group-item-action" onclick="testCode('EK9-HAB-TAB3U')">
                                        <strong>EK9-HAB-TAB3U</strong>
                                        <small class="text-muted d-block">Groupe: HAB</small>
                                    </button>
                                    <button class="list-group-item list-group-item-action" onclick="testCode('EK9-HAB-TAB3Y')">
                                        <strong>EK9-HAB-TAB3Y</strong>
                                        <small class="text-muted d-block">Groupe: HAB</small>
                                    </button>
                                    <button class="list-group-item list-group-item-action" onclick="testCode('EK9-HAB-TPB2LA')">
                                        <strong>EK9-HAB-TPB2LA</strong>
                                        <small class="text-muted d-block">Groupe: HAB</small>
                                    </button>
                                    <button class="list-group-item list-group-item-action" onclick="testCode('EK9-PPL-TPC3I')">
                                        <strong>EK9-PPL-TPC3I</strong>
                                        <small class="text-muted d-block">Groupe: PPL</small>
                                    </button>
                                    <button class="list-group-item list-group-item-action" onclick="testCode('EK9-PPL-TPC3J')">
                                        <strong>EK9-PPL-TPC3J</strong>
                                        <small class="text-muted d-block">Groupe: PPL</small>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h5>📋 Résultats du Test</h5>
                                <div id="testResults" class="border rounded p-3 bg-light">
                                    <p class="text-muted">Cliquez sur un code pour le tester</p>
                                </div>
                                
                                <div class="mt-3">
                                    <h6>Test Manuel</h6>
                                    <div class="input-group">
                                        <input type="text" id="manualCode" class="form-control" placeholder="Entrez un code unicos">
                                        <button class="btn btn-primary" onclick="testManualCode()">
                                            <i class="fas fa-search me-1"></i>Tester
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <div class="row">
                            <div class="col-md-12">
                                <h5>🔗 Navigation</h5>
                                <div class="d-grid gap-2 d-md-flex">
                                    <a href="index.html" class="btn btn-success">
                                        <i class="fas fa-search me-2"></i>Scanner Principal
                                    </a>
                                    <a href="test-scanner.html" class="btn btn-info">
                                        <i class="fas fa-flask me-2"></i>Page de Test
                                    </a>
                                    <button class="btn btn-warning" onclick="loadAllDataTwist()">
                                        <i class="fas fa-database me-2"></i>Charger Tous les Codes
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <div class="alert alert-light">
                                <h6><i class="fas fa-lightbulb me-2"></i>Instructions</h6>
                                <ol>
                                    <li><strong>Cliquez</strong> sur un code de test pour le scanner</li>
                                    <li><strong>Vérifiez</strong> que les informations Data twist s'affichent</li>
                                    <li><strong>Testez</strong> manuellement d'autres codes unicos</li>
                                    <li><strong>Utilisez</strong> le scanner principal pour l'usage normal</li>
                                </ol>
                                
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        <strong>Note:</strong> Les codes Data twist sont automatiquement intégrés dans le scanner principal.
                                        Cette page est uniquement pour les tests.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/data-loader.js"></script>
    <script>
        async function testCode(code) {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<p class="text-info"><i class="fas fa-spinner fa-spin me-2"></i>Test en cours...</p>';
            
            try {
                const result = await searchBarcode(code);
                
                if (result) {
                    resultsDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>Code trouvé!</h6>
                            <p><strong>Code:</strong> ${result.code}</p>
                            <p><strong>Nom:</strong> ${result.name}</p>
                            <p><strong>Format:</strong> ${result.format}</p>
                            <p><strong>Catégorie:</strong> ${result.category}</p>
                            <p><strong>Machine:</strong> ${result.machine || 'N/A'}</p>
                            <p><strong>Ordre:</strong> ${result.order || 'N/A'}</p>
                            <p><strong>Reste:</strong> ${result.remaining || 'N/A'}</p>
                            <p><strong>Statut:</strong> ${result.status}</p>
                            <p><strong>Section:</strong> ${result.section}</p>
                            <p><strong>Description:</strong> ${result.description}</p>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-times-circle me-2"></i>Code non trouvé</h6>
                            <p>Le code <strong>${code}</strong> n'a pas été trouvé dans la base de données.</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Erreur lors du test:', error);
                resultsDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Erreur</h6>
                        <p>Erreur lors du test: ${error.message}</p>
                    </div>
                `;
            }
        }
        
        function testManualCode() {
            const code = document.getElementById('manualCode').value.trim();
            if (code) {
                testCode(code);
            } else {
                alert('Veuillez entrer un code à tester.');
            }
        }
        
        async function loadAllDataTwist() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<p class="text-info"><i class="fas fa-spinner fa-spin me-2"></i>Chargement de tous les codes Data twist...</p>';
            
            try {
                const dataTwistCodes = await loadDataTwistCodes();
                
                resultsDiv.innerHTML = `
                    <div class="alert alert-success">
                        <h6><i class="fas fa-database me-2"></i>Codes Data Twist Chargés</h6>
                        <p><strong>Total:</strong> ${dataTwistCodes.length} codes unicos</p>
                        <p><strong>Premiers codes:</strong></p>
                        <ul>
                            ${dataTwistCodes.slice(0, 10).map(item => `<li>${item.code} (${item.category})</li>`).join('')}
                        </ul>
                        ${dataTwistCodes.length > 10 ? `<p><small class="text-muted">... et ${dataTwistCodes.length - 10} autres codes</small></p>` : ''}
                    </div>
                `;
            } catch (error) {
                console.error('Erreur lors du chargement:', error);
                resultsDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Erreur</h6>
                        <p>Erreur lors du chargement: ${error.message}</p>
                    </div>
                `;
            }
        }
        
        // Test automatique au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            // Tester automatiquement le premier code
            setTimeout(() => {
                testCode('EK9-HAB-TAB3U');
            }, 1000);
        });
    </script>
</body>
</html>
