import React, { useState } from 'react';
import { FileText, Download, Calendar, Filter, BarChart3, PieChart, TrendingUp } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

const ReportCenter: React.FC = () => {
  const { hasPermission } = useAuth();
  const [selectedReport, setSelectedReport] = useState('');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [isGenerating, setIsGenerating] = useState(false);

  const reportTypes = [
    {
      id: 'inventory',
      name: 'Rapport d\'Inventaire',
      description: 'État détaillé des stocks et valeurs',
      icon: BarChart3,
      color: 'text-blue-600 bg-blue-100',
    },
    {
      id: 'sales',
      name: 'Rapport de Ventes',
      description: 'Analyse des ventes par période',
      icon: TrendingUp,
      color: 'text-green-600 bg-green-100',
    },
    {
      id: 'financial',
      name: 'Rapport Financier',
      description: 'Bilan financier et cash-flow',
      icon: Pie<PERSON>hart,
      color: 'text-purple-600 bg-purple-100',
    },
    {
      id: 'customer',
      name: 'Rapport Clients',
      description: 'Analyse des comptes clients',
      icon: FileText,
      color: 'text-orange-600 bg-orange-100',
    },
  ];

  const handleGenerateReport = async () => {
    if (!selectedReport) return;
    
    setIsGenerating(true);
    // Simulate report generation
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsGenerating(false);
    
    // In a real application, this would trigger the actual report generation
    alert(`Rapport ${reportTypes.find(r => r.id === selectedReport)?.name} généré avec succès!`);
  };

  if (!hasPermission('reports')) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-600">Accès Restreint</h2>
          <p className="text-gray-500 mt-2">Vous n'avez pas les permissions pour accéder aux rapports.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Centre de Rapports</h1>
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <Calendar className="w-4 h-4" />
          <span>Génération automatisée disponible</span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {reportTypes.map((report) => {
          const Icon = report.icon;
          const isSelected = selectedReport === report.id;
          
          return (
            <div
              key={report.id}
              onClick={() => setSelectedReport(report.id)}
              className={`bg-white rounded-lg shadow-sm border-2 cursor-pointer transition-all hover:shadow-md ${
                isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="p-6">
                <div className={`w-12 h-12 rounded-lg flex items-center justify-center mb-4 ${report.color}`}>
                  <Icon className="w-6 h-6" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{report.name}</h3>
                <p className="text-sm text-gray-600">{report.description}</p>
              </div>
            </div>
          );
        })}
      </div>

      {selectedReport && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Configuration du Rapport
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Date de début
              </label>
              <input
                type="date"
                value={dateRange.start}
                onChange={(e) => setDateRange({ ...dateRange, start: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Date de fin
              </label>
              <input
                type="date"
                value={dateRange.end}
                onChange={(e) => setDateRange({ ...dateRange, end: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <div className="mt-6 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                <Filter className="w-4 h-4" />
                <span>Filtres avancés</span>
              </button>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={handleGenerateReport}
                disabled={isGenerating}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isGenerating ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
                ) : (
                  <Download className="w-4 h-4" />
                )}
                <span>{isGenerating ? 'Génération...' : 'Générer PDF'}</span>
              </button>
              
              <button
                onClick={handleGenerateReport}
                disabled={isGenerating}
                className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isGenerating ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
                ) : (
                  <Download className="w-4 h-4" />
                )}
                <span>{isGenerating ? 'Génération...' : 'Générer Excel'}</span>
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Rapports Récents</h2>
        
        <div className="space-y-3">
          {[
            { name: 'Rapport d\'Inventaire - Novembre 2024', date: '2024-12-01', size: '2.4 MB' },
            { name: 'Rapport Financier - Q4 2024', date: '2024-11-30', size: '1.8 MB' },
            { name: 'Analyse Clients - Novembre 2024', date: '2024-11-28', size: '1.2 MB' },
          ].map((report, index) => (
            <div key={index} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
              <div className="flex items-center space-x-3">
                <FileText className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium text-gray-900">{report.name}</p>
                  <p className="text-xs text-gray-500">Généré le {new Date(report.date).toLocaleDateString('fr-FR')} • {report.size}</p>
                </div>
              </div>
              <button className="text-blue-600 hover:text-blue-800 transition-colors">
                <Download className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ReportCenter;