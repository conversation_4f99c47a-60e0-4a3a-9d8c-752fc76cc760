<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Préchargement Scans Data Twist</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-database me-2"></i>
                            Préchargement des Scans Data Twist
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>À propos</h6>
                            <p class="mb-0">Cette page permet de précharger des scans récents basés sur les données du fichier Data twist.xlsx dans l'historique du scanner.</p>
                        </div>

                        <div class="mb-3">
                            <button class="btn btn-primary" onclick="preloadDataTwistScans()">
                                <i class="fas fa-upload me-2"></i>Précharger les Scans Data Twist
                            </button>
                            <button class="btn btn-warning ms-2" onclick="clearScanHistory()">
                                <i class="fas fa-trash me-2"></i>Vider l'Historique
                            </button>
                        </div>

                        <div id="status" class="mt-3">
                            <!-- Status messages will appear here -->
                        </div>

                        <div class="mt-4">
                            <h6>Scans qui seront préchargés:</h6>
                            <div id="previewScans" class="border rounded p-3 bg-light">
                                <p class="text-muted">Cliquez sur "Précharger" pour voir les scans</p>
                            </div>
                        </div>

                        <div class="mt-4">
                            <div class="d-grid">
                                <a href="index.html" class="btn btn-success">
                                    <i class="fas fa-arrow-right me-2"></i>Aller au Scanner Principal
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/data-loader.js"></script>
    <script>
        async function preloadDataTwistScans() {
            const statusDiv = document.getElementById('status');
            const previewDiv = document.getElementById('previewScans');
            
            statusDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin me-2"></i>Chargement des données Data Twist...</div>';
            
            try {
                // Load CSV data
                const csvData = await loadBarcodeData();
                
                // Filter for Data Twist codes
                const dataTwistCodes = csvData.filter(item => 
                    item.Code && item.Code.startsWith('EK9-') && 
                    (item.Category === 'HAB' || item.Category === 'PPL' || item.Category === 'TAB' || item.Category === 'Production')
                ).slice(0, 15); // Get first 15 codes
                
                if (dataTwistCodes.length === 0) {
                    statusDiv.innerHTML = '<div class="alert alert-warning">Aucun code Data Twist trouvé dans la base de données.</div>';
                    return;
                }
                
                // Create scan records
                const baseTime = Date.now() - (48 * 60 * 60 * 1000); // 48 hours ago
                const scanRecords = dataTwistCodes.map((item, index) => ({
                    code: item.Code,
                    timestamp: new Date(baseTime + (index * 3 * 60 * 60 * 1000)).toISOString(), // 3 hours apart
                    user: 'Production System',
                    action: 'Production Scan',
                    location: item.Location || 'Production Line',
                    result: 'Scanned'
                }));
                
                // Save to localStorage
                localStorage.setItem('barcodeScanHistory', JSON.stringify(scanRecords));
                
                // Show success message
                statusDiv.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check me-2"></i>
                        <strong>Succès!</strong> ${scanRecords.length} scans Data Twist ont été préchargés dans l'historique.
                    </div>
                `;
                
                // Show preview
                const previewHtml = scanRecords.slice(0, 10).map(scan => `
                    <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                        <div>
                            <strong>${scan.code}</strong>
                            <small class="text-muted d-block">${scan.action} - ${new Date(scan.timestamp).toLocaleString('fr-FR')}</small>
                        </div>
                        <span class="badge bg-primary">Data Twist</span>
                    </div>
                `).join('');
                
                previewDiv.innerHTML = `
                    <h6>Aperçu des scans préchargés (10 premiers):</h6>
                    ${previewHtml}
                    ${scanRecords.length > 10 ? `<p class="text-muted mt-2">... et ${scanRecords.length - 10} autres scans</p>` : ''}
                `;
                
            } catch (error) {
                console.error('Erreur lors du préchargement:', error);
                statusDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-times me-2"></i>
                        <strong>Erreur:</strong> ${error.message}
                    </div>
                `;
            }
        }
        
        function clearScanHistory() {
            if (confirm('Êtes-vous sûr de vouloir vider l\'historique des scans ?')) {
                localStorage.removeItem('barcodeScanHistory');
                
                document.getElementById('status').innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-trash me-2"></i>
                        L'historique des scans a été vidé.
                    </div>
                `;
                
                document.getElementById('previewScans').innerHTML = '<p class="text-muted">Historique vidé</p>';
            }
        }
        
        // Show current scan count on page load
        document.addEventListener('DOMContentLoaded', function() {
            const scans = JSON.parse(localStorage.getItem('barcodeScanHistory') || '[]');
            if (scans.length > 0) {
                document.getElementById('status').innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Il y a actuellement ${scans.length} scans dans l'historique.
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
