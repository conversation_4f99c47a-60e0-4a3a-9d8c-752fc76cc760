import React, { useState, useRef, useEffect } from 'react';
import { Download, Camera, Square, RotateCcw, Zap } from 'lucide-react';

interface BarcodeGeneratorProps {
  onBarcodeGenerated?: (data: string, format: string) => void;
}

const BarcodeGenerator: React.FC<BarcodeGeneratorProps> = ({ onBarcodeGenerated }) => {
  const [inputText, setInputText] = useState('');
  const [selectedFormat, setSelectedFormat] = useState('CODE128');
  const [error, setError] = useState('');
  const [barcodeInfo, setBarcodeInfo] = useState<any>(null);
  const barcodeRef = useRef<SVGSVGElement>(null);

  const formats = [
    { code: 'CODE128', name: 'Code 128', description: 'Alphanumeric, haute densité' },
    { code: 'CODE39', name: 'Code 39', description: 'Alphanumeric, largement utilisé' },
    { code: 'EAN13', name: 'EAN-13', description: '13 chiffres, produits de consommation' },
    { code: 'EAN8', name: 'EAN-8', description: '8 chiffres, petits produits' },
    { code: 'UPC', name: 'UPC-A', description: '12 chiffres, États-Unis/Canada' },
    { code: 'ITF14', name: 'ITF-14', description: '14 chiffres, emballages' }
  ];

  const sampleData = [
    { text: 'APTIV-CBL-HT-001', format: 'CODE128', description: 'Câble Haute Tension 16AWG', category: 'Câbles', stock: 2450 },
    { text: 'APTIV-CON-IP67-004', format: 'CODE128', description: 'Connecteur Étanche IP67', category: 'Connecteurs', stock: 1250 },
    { text: '3760123456789', format: 'EAN13', description: 'Kit de Câblage Automobile Premium', price: 89.99 },
    { text: '3760234567890', format: 'EAN13', description: 'Connecteur Multi-Pin 24 Voies', price: 34.50 },
    { text: 'LOT-2024-001', format: 'CODE39', description: 'Lot de production Janvier 2024', type: 'Lot Number' },
    { text: 'QC-PASS-2024-A', format: 'CODE39', description: 'Contrôle qualité réussi - Série A', type: 'Quality Control' },
    { text: '12345678', format: 'EAN8', description: 'EAN-8 de test compact' },
    { text: '123456789012', format: 'UPC', description: 'UPC-A de test pour marché US' },
    { text: 'APTIV-TEST-999', format: 'CODE128', description: 'Code de test Aptiv spécifique' }
  ];

  useEffect(() => {
    if (inputText.trim()) {
      generateBarcode();
    }
  }, [inputText, selectedFormat]);

  const validateInput = (text: string, format: string): boolean => {
    switch (format) {
      case 'EAN13':
        return /^\d{12,13}$/.test(text);
      case 'EAN8':
        return /^\d{7,8}$/.test(text);
      case 'UPC':
        return /^\d{11,12}$/.test(text);
      case 'CODE39':
        return /^[A-Z0-9\-. $/+%]*$/.test(text.toUpperCase());
      case 'ITF14':
        return /^\d{13,14}$/.test(text);
      case 'CODE128':
      default:
        return text.length > 0 && text.length <= 50;
    }
  };

  const generateBarcode = () => {
    if (!inputText.trim()) {
      setError('');
      setBarcodeInfo(null);
      return;
    }

    if (!validateInput(inputText, selectedFormat)) {
      setError(`Format invalide pour ${selectedFormat}. Vérifiez les exigences du format.`);
      setBarcodeInfo(null);
      return;
    }

    try {
      setError('');

      if (barcodeRef.current && window.JsBarcode) {
        window.JsBarcode(barcodeRef.current, inputText, {
          format: selectedFormat,
          width: 2,
          height: 100,
          displayValue: true,
          fontSize: 14,
          margin: 10,
          background: '#ffffff',
          lineColor: '#000000'
        });

        const info = {
          text: inputText,
          format: selectedFormat,
          length: inputText.length,
          generatedAt: new Date().toLocaleString('fr-FR'),
          isValid: true
        };

        setBarcodeInfo(info);
        onBarcodeGenerated?.(inputText, selectedFormat);
      }
    } catch (err: any) {
      setError(`Erreur lors de la génération: ${err.message}`);
      setBarcodeInfo(null);
    }
  };

  const downloadBarcode = () => {
    if (!barcodeRef.current) return;

    try {
      const svg = barcodeRef.current;
      const svgData = new XMLSerializer().serializeToString(svg);
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        canvas.width = img.naturalWidth;
        canvas.height = img.naturalHeight;
        ctx?.drawImage(img, 0, 0);

        const link = document.createElement('a');
        link.download = `barcode_${inputText}_${Date.now()}.png`;
        link.href = canvas.toDataURL('image/png');
        link.click();
      };

      img.src = `data:image/svg+xml;base64,${btoa(svgData)}`;
    } catch (err) {
      setError('Erreur lors du téléchargement');
    }
  };

  const loadSampleData = () => {
    const sample = sampleData[Math.floor(Math.random() * sampleData.length)];
    setInputText(sample.text);
    setSelectedFormat(sample.format);
  };

  const clearAll = () => {
    setInputText('');
    setError('');
    setBarcodeInfo(null);
    if (barcodeRef.current) {
      barcodeRef.current.innerHTML = '';
    }
  };

  const getFormatRequirements = (format: string): string => {
    switch (format) {
      case 'EAN13': return '12-13 chiffres uniquement';
      case 'EAN8': return '7-8 chiffres uniquement';
      case 'UPC': return '11-12 chiffres uniquement';
      case 'CODE39': return 'Lettres majuscules, chiffres, -. $/+%';
      case 'ITF14': return '13-14 chiffres uniquement';
      case 'CODE128': return 'Tous caractères, max 50';
      default: return '';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-4xl mx-auto">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center">
          <Square className="mr-2 text-blue-600" />
          Générateur de Code-Barres
        </h2>
        <p className="text-gray-600">Créez des codes-barres dans différents formats</p>
      </div>

      {/* Input Section */}
      <div className="mb-6">
        <label htmlFor="barcodeInput" className="block text-sm font-medium text-gray-700 mb-2">
          Texte ou numéro à encoder
        </label>
        <div className="flex gap-2">
          <input
            id="barcodeInput"
            type="text"
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder="Entrez votre texte ou numéro..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            maxLength={50}
          />
          <button
            onClick={loadSampleData}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
          >
            <Zap className="w-4 h-4 mr-1" />
            Test
          </button>
          <button
            onClick={clearAll}
            className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            <RotateCcw className="w-4 h-4" />
          </button>
        </div>
        <p className="text-xs text-gray-500 mt-1">
          {getFormatRequirements(selectedFormat)}
        </p>
      </div>

      {/* Format Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-3">Format du code-barres</label>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          {formats.map((format) => (
            <button
              key={format.code}
              onClick={() => setSelectedFormat(format.code)}
              className={`p-3 text-left border rounded-lg transition-colors ${selectedFormat === format.code
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-300 hover:border-gray-400'
                }`}
            >
              <div className="font-medium text-sm">{format.name}</div>
              <div className="text-xs text-gray-500">{format.description}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Barcode Display */}
      <div className="mb-6">
        <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center min-h-[200px] flex items-center justify-center">
          <svg ref={barcodeRef} className="max-w-full h-auto"></svg>
          {!inputText && (
            <p className="text-gray-500">Entrez un texte pour générer le code-barres</p>
          )}
        </div>
      </div>

      {/* Barcode Info */}
      {barcodeInfo && (
        <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-medium text-blue-900 mb-2">Informations du code-barres</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Texte:</span> {barcodeInfo.text}
            </div>
            <div>
              <span className="font-medium">Format:</span> {barcodeInfo.format}
            </div>
            <div>
              <span className="font-medium">Longueur:</span> {barcodeInfo.length} caractères
            </div>
            <div>
              <span className="font-medium">Généré le:</span> {barcodeInfo.generatedAt}
            </div>
          </div>
        </div>
      )}

      {/* Download Button */}
      {barcodeInfo && (
        <div className="flex justify-center">
          <button
            onClick={downloadBarcode}
            className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center"
          >
            <Download className="w-5 h-5 mr-2" />
            Télécharger PNG
          </button>
        </div>
      )}
    </div>
  );
};

export default BarcodeGenerator;
