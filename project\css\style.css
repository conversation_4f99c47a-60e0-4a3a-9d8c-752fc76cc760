/* Barcode Generator Styles */
.barcode-generator-section {
    margin-bottom: 2rem;
}

.barcode-generator-section .card-header {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    border: none;
}

.barcode-generator-section .card {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.barcode-display {
    background: #f8f9fa !important;
    border: 2px dashed #dee2e6;
    transition: all 0.3s ease;
}

.barcode-display:hover {
    border-color: #0d6efd;
    background: #e7f1ff !important;
}

.barcode-display svg {
    max-width: 100%;
    height: auto;
}

.btn-group .btn-check:checked+.btn {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

.barcode-generator-section .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
}

.barcode-generator-section .form-control-lg {
    padding: 0.75rem 1rem;
    font-size: 1.125rem;
    border-radius: 0.5rem;
}

.barcode-generator-section .btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
    border-radius: 0.5rem;
}

#barcodeInfo {
    background: rgba(13, 110, 253, 0.1);
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-top: 1rem;
}

/* Animation for barcode generation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.barcode-display svg {
    animation: fadeInUp 0.5s ease-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .barcode-generator-section .btn-group {
        flex-direction: column;
    }

    .barcode-generator-section .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 0.25rem;
    }

    .barcode-generator-section .btn-group-vertical {
        width: 100%;
    }
}

/* Scanner Barcode Visual Styles */
.recent-scans {
    max-height: 300px;
    overflow-y: auto;
}

.barcode-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 2px solid #e9ecef !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.barcode-container:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

.barcode-visual {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    margin: 0 auto;
    max-width: 300px;
    overflow: hidden;
}

.barcode-bars {
    min-height: 60px;
    background: linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%);
    border-radius: 2px;
    padding: 5px;
}

.barcode-text {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    font-weight: bold;
    color: #212529;
    letter-spacing: 1px;
    margin-top: 8px;
}

.bar {
    background: #000000 !important;
    border-radius: 0;
    transition: all 0.2s ease;
}

.barcode-bars:hover .bar {
    background: #333333 !important;
}

/* Barcode format badge */
.barcode-container small {
    background: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

/* Animation for barcode appearance */
@keyframes barcodeAppear {
    from {
        opacity: 0;
        transform: scale(0.9);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

.barcode-container {
    animation: barcodeAppear 0.5s ease-out;
}

/* Responsive barcode for scanner */
@media (max-width: 768px) {
    .barcode-visual {
        max-width: 250px;
    }

    .barcode-text {
        font-size: 12px;
    }
}



/* Scan Setup Modal Styles */
.modal-dialog.modal-lg {
    max-width: 900px;
}

#scanSetupModal .nav-tabs {
    border-bottom: 2px solid #dee2e6;
}

#scanSetupModal .nav-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-weight: 500;
    padding: 12px 20px;
}

#scanSetupModal .nav-tabs .nav-link:hover {
    border-bottom-color: #0dcaf0;
    color: #0dcaf0;
}

#scanSetupModal .nav-tabs .nav-link.active {
    background-color: transparent;
    border-bottom-color: #0dcaf0;
    color: #0dcaf0;
    font-weight: 600;
}

#scanSetupModal .tab-content {
    min-height: 400px;
    padding: 20px 0;
}

#scanSetupModal .form-check-input:checked {
    background-color: #0dcaf0;
    border-color: #0dcaf0;
}

#scanSetupModal .form-range::-webkit-slider-thumb {
    background-color: #0dcaf0;
}

#scanSetupModal .form-range::-moz-range-thumb {
    background-color: #0dcaf0;
    border: none;
}

/* Theme Styles */
.theme-default {
    /* Thème par défaut - déjà défini */
}

.theme-dark {
    background-color: #1a1a1a;
    color: #e9ecef;
}

.theme-dark .card {
    background-color: #2d2d2d;
    border-color: #495057;
}

.theme-dark .card-header {
    background-color: #343a40;
    border-bottom-color: #495057;
}

.theme-dark .form-control {
    background-color: #343a40;
    border-color: #495057;
    color: #e9ecef;
}

.theme-dark .form-control:focus {
    background-color: #495057;
    border-color: #0dcaf0;
    color: #e9ecef;
    box-shadow: 0 0 0 0.2rem rgba(13, 202, 240, 0.25);
}

.theme-dark .btn-outline-warning {
    color: #ffc107;
    border-color: #ffc107;
}

.theme-dark .btn-outline-warning:hover {
    background-color: #ffc107;
    color: #000;
}

.theme-high-contrast {
    background-color: #000;
    color: #fff;
}

.theme-high-contrast .card {
    background-color: #1a1a1a;
    border: 2px solid #fff;
}

.theme-high-contrast .card-header {
    background-color: #333;
    border-bottom: 2px solid #fff;
}

.theme-high-contrast .form-control {
    background-color: #000;
    border: 2px solid #fff;
    color: #fff;
}

.theme-high-contrast .form-control:focus {
    background-color: #1a1a1a;
    border-color: #ffff00;
    color: #fff;
    box-shadow: 0 0 0 0.2rem rgba(255, 255, 0, 0.5);
}

.theme-high-contrast .btn {
    border-width: 2px;
    font-weight: bold;
}

.theme-high-contrast .btn-outline-warning {
    color: #ffff00;
    border-color: #ffff00;
}

.theme-high-contrast .btn-outline-warning:hover {
    background-color: #ffff00;
    color: #000;
}

/* Configuration sections styling */
.setup-section {
    border-left: 4px solid #0dcaf0;
    padding-left: 15px;
    margin-bottom: 20px;
}

.setup-section h6 {
    color: #0dcaf0;
    font-weight: 600;
    margin-bottom: 15px;
}

/* Test buttons styling */
.test-buttons .btn {
    margin-right: 5px;
    margin-bottom: 5px;
}

/* Range slider custom styling */
.form-range {
    height: 6px;
}

.form-range::-webkit-slider-track {
    background-color: #dee2e6;
    border-radius: 3px;
}

.form-range::-webkit-slider-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Alert customizations for setup */
#scanSetupModal .alert {
    border-radius: 8px;
    border: none;
    font-size: 0.9rem;
}

#scanSetupModal .alert-info {
    background-color: rgba(13, 202, 240, 0.1);
    color: #0dcaf0;
}

#scanSetupModal .alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

/* Responsive adjustments for setup modal */
@media (max-width: 768px) {
    #scanSetupModal .modal-dialog {
        margin: 10px;
        max-width: calc(100% - 20px);
    }

    #scanSetupModal .nav-tabs .nav-link {
        padding: 8px 12px;
        font-size: 0.9rem;
    }

    #scanSetupModal .tab-content {
        min-height: 300px;
        padding: 15px 0;
    }

    .test-buttons .btn {
        width: 100%;
        margin-bottom: 10px;
    }
}

/* T24 SETUP Format Styles */
.t24-setup-container {
    background-color: #f8f9fa;
    border: 2px solid #6c757d;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    font-family: 'Courier New', monospace;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.t24-header {
    text-align: center;
    margin-bottom: 30px;
}

.twist-area-title {
    margin-bottom: 20px;
}

.twist-area-title h2 {
    font-size: 2.5rem;
    font-weight: bold;
    letter-spacing: 8px;
    margin: 5px 0;
    color: #333;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.t24-datetime {
    font-size: 1.1rem;
    margin: 15px 0;
    color: #495057;
    font-weight: 500;
}

.t24-setup-title h3 {
    font-size: 1.8rem;
    font-weight: bold;
    text-decoration: underline;
    margin: 20px 0;
    color: #212529;
}

.t24-table {
    margin: 30px 0;
}

.setup-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.setup-table th,
.setup-table td {
    border: 2px solid #333;
    padding: 12px 8px;
    text-align: center;
    font-size: 0.95rem;
    font-weight: 500;
}

.setup-table th {
    background-color: #e9ecef;
    font-weight: bold;
    color: #212529;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.setup-table .scanned-row {
    background-color: #d4edda;
    border-left: 4px solid #28a745;
}

.setup-table .unico-code {
    font-weight: bold;
    font-size: 1.1rem;
    color: #0d6efd;
    font-family: 'Courier New', monospace;
}

.setup-table .groupe {
    font-weight: bold;
    color: #6f42c1;
}

.setup-table .ordre,
.setup-table .reste {
    font-weight: bold;
    color: #fd7e14;
}

.setup-table .date-time {
    font-size: 0.85rem;
    line-height: 1.2;
    color: #6c757d;
}

.t24-actions {
    text-align: center;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

.t24-actions .btn {
    margin: 0 5px;
    font-weight: 500;
}

/* T24 Setup List Modal Styles */
#t24SetupListModal .table th {
    background-color: #343a40;
    color: white;
    border-color: #495057;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

#t24SetupListModal .table td {
    vertical-align: middle;
    font-family: 'Courier New', monospace;
}

#t24SetupListModal .table .unico-code {
    font-weight: bold;
    color: #0d6efd;
    font-size: 1rem;
}

/* T24 Setup Badge */
.t24-setup-badge {
    background-color: #ffc107;
    color: #000;
    font-size: 0.75rem;
    font-weight: bold;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 5px;
    display: none;
}

/* Print styles for T24 */
@media print {
    .t24-setup-container {
        border: none;
        box-shadow: none;
        background-color: white;
        margin: 0;
        padding: 20px;
    }

    .t24-actions {
        display: none;
    }

    .setup-table {
        box-shadow: none;
    }

    .setup-table th,
    .setup-table td {
        border: 2px solid #000;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .setup-table .scanned-row {
        background-color: #f0f0f0 !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
}

/* Responsive T24 styles */
@media (max-width: 768px) {
    .twist-area-title h2 {
        font-size: 2rem;
        letter-spacing: 4px;
    }

    .t24-setup-title h3 {
        font-size: 1.5rem;
    }

    .setup-table th,
    .setup-table td {
        padding: 8px 4px;
        font-size: 0.8rem;
    }

    .setup-table .unico-code {
        font-size: 0.9rem;
    }

    .t24-actions .btn {
        display: block;
        width: 100%;
        margin: 5px 0;
    }
}

@media (max-width: 576px) {
    .t24-setup-container {
        padding: 15px;
        margin: 10px 0;
    }

    .twist-area-title h2 {
        font-size: 1.5rem;
        letter-spacing: 2px;
    }

    .setup-table {
        font-size: 0.75rem;
    }

    .setup-table th,
    .setup-table td {
        padding: 6px 2px;
    }
}

/* Mini Barcode Styles for Recent Scans */
.recent-scan-item {
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 12px !important;
    margin-bottom: 8px;
}

.recent-scan-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mini-barcode-container {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 4px;
    max-width: 120px;
    transition: all 0.2s ease;
}

.mini-barcode-container:hover {
    border-color: #0d6efd;
    box-shadow: 0 1px 3px rgba(13, 110, 253, 0.2);
}

.mini-barcode-visual {
    background: linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%);
    border-radius: 2px;
    padding: 2px;
    overflow: hidden;
}

.mini-barcode-bars {
    min-height: 30px;
    background: #ffffff;
}

.mini-bar {
    background: #000000 !important;
    border-radius: 0;
    transition: all 0.2s ease;
}

.mini-barcode-container:hover .mini-bar {
    background: #333333 !important;
}

.recent-scan-item .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 4px;
}

.recent-scan-item .badge {
    font-size: 0.65rem;
    padding: 0.25em 0.5em;
}

/* Animation for recent scan items */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.recent-scan-item {
    animation: slideInRight 0.3s ease-out;
}

/* Responsive adjustments for recent scans */
@media (max-width: 768px) {
    .recent-scan-item {
        padding: 8px !important;
    }

    .mini-barcode-container {
        max-width: 100px;
    }

    .mini-barcode-bars {
        min-height: 25px;
    }

    .recent-scan-item .d-flex.flex-column {
        flex-direction: row !important;
        gap: 0.25rem !important;
    }
}