/* ===== STYLES GÉNÉRAUX ===== */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
    border-bottom: none;
    font-weight: 600;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* ===== GRAPHIQUES ===== */
.chart-container {
    position: relative;
    height: 400px;
    margin: 1rem 0;
}

/* ===== TABLEAUX ===== */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    background-color: #f8f9fa;
}

.table tbody tr:hover {
    background-color: #f5f5f5;
}

/* ===== BADGES ET STATUTS ===== */
.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

.status-badge {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease;
}

.slide-in-up {
    animation: slideInUp 0.5s ease;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }

    .chart-container {
        height: 300px;
    }

    .table-responsive {
        font-size: 0.875rem;
    }
}

/* ===== UTILITAIRES ===== */
.text-primary {
    color: #007bff !important;
}

.text-success {
    color: #28a745 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-info {
    color: #17a2b8 !important;
}

.bg-primary {
    background-color: #007bff !important;
}

.bg-success {
    background-color: #28a745 !important;
}

.bg-warning {
    background-color: #ffc107 !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.bg-info {
    background-color: #17a2b8 !important;
}

/* ===== FOOTER ===== */
.footer {
    background-color: #343a40;
    color: white;
    padding: 1rem 0;
    margin-top: 2rem;
}

.footer a {
    color: #adb5bd;
    text-decoration: none;
}

.footer a:hover {
    color: white;
}

/* ===== START PRODUCTION PAR HEURE STYLES ===== */
#startHourlyProductionBtn {
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.3s ease;
}

#startHourlyProductionBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.modal-header.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

#hourlyConfigSummary {
    background: rgba(13, 110, 253, 0.05);
    border-radius: 8px;
    padding: 1rem;
}

#hourlyConfigSummary p {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

#hourlyConfigSummary span {
    color: #0d6efd;
    font-weight: 600;
}

/* Alertes de notification */
.alert.position-fixed {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive pour la modal */
@media (max-width: 768px) {
    .modal-dialog.modal-lg {
        margin: 10px;
        max-width: calc(100% - 20px);
    }

    #startHourlyProductionBtn {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
    }
}

/* ===== DÉTAILS MACHINE STYLES ===== */
#machineDetailsSection {
    animation: slideInUp 0.5s ease-out;
}

#machineDetailsSection .card {
    border: 2px solid #007bff;
    box-shadow: 0 4px 20px rgba(0, 123, 255, 0.15);
}

#machineDetailsSection .progress {
    border-radius: 10px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

#machineDetailsSection .progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
}

#machineDetailsSection .table td {
    border: none;
    padding: 0.5rem 0;
}

#machineDetailsSection .card.bg-light:hover {
    background-color: #e9ecef !important;
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

#machineDetailsSection .btn-sm {
    border-radius: 6px;
    font-weight: 500;
}

#machineDetailsSection .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
}

/* Animation pour l'apparition des détails */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive pour les détails machine */
@media (max-width: 768px) {
    #machineDetailsSection .row > div {
        margin-bottom: 1rem;
    }

    #machineDetailsSection .d-flex.flex-wrap {
        flex-direction: column;
    }

    #machineDetailsSection .btn-sm {
        margin-bottom: 0.5rem;
        width: 100%;
    }
}