/* Barcode Generator Styles */
.barcode-generator-section {
    margin-bottom: 2rem;
}

.barcode-generator-section .card-header {
    background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
    border: none;
}

.barcode-generator-section .card {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.barcode-display {
    background: #f8f9fa !important;
    border: 2px dashed #dee2e6;
    transition: all 0.3s ease;
}

.barcode-display:hover {
    border-color: #0d6efd;
    background: #e7f1ff !important;
}

.barcode-display svg {
    max-width: 100%;
    height: auto;
}

.btn-group .btn-check:checked+.btn {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

.barcode-generator-section .btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
}

.barcode-generator-section .form-control-lg {
    padding: 0.75rem 1rem;
    font-size: 1.125rem;
    border-radius: 0.5rem;
}

.barcode-generator-section .btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
    border-radius: 0.5rem;
}

#barcodeInfo {
    background: rgba(13, 110, 253, 0.1);
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-top: 1rem;
}

/* Animation for barcode generation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.barcode-display svg {
    animation: fadeInUp 0.5s ease-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .barcode-generator-section .btn-group {
        flex-direction: column;
    }

    .barcode-generator-section .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 0.25rem;
    }

    .barcode-generator-section .btn-group-vertical {
        width: 100%;
    }
}

/* Scanner Barcode Visual Styles */
.recent-scans {
    max-height: 300px;
    overflow-y: auto;
}

.barcode-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 2px solid #e9ecef !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.barcode-container:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

.barcode-visual {
    background: #ffffff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    margin: 0 auto;
    max-width: 300px;
    overflow: hidden;
}

.barcode-bars {
    min-height: 60px;
    background: linear-gradient(to bottom, #ffffff 0%, #f8f9fa 100%);
    border-radius: 2px;
    padding: 5px;
}

.barcode-text {
    font-family: 'Courier New', monospace;
    font-size: 14px;
    font-weight: bold;
    color: #212529;
    letter-spacing: 1px;
    margin-top: 8px;
}

.bar {
    background: #000000 !important;
    border-radius: 0;
    transition: all 0.2s ease;
}

.barcode-bars:hover .bar {
    background: #333333 !important;
}

/* Barcode format badge */
.barcode-container small {
    background: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

/* Animation for barcode appearance */
@keyframes barcodeAppear {
    from {
        opacity: 0;
        transform: scale(0.9);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

.barcode-container {
    animation: barcodeAppear 0.5s ease-out;
}

/* Responsive barcode for scanner */
@media (max-width: 768px) {
    .barcode-visual {
        max-width: 250px;
    }

    .barcode-text {
        font-size: 12px;
    }
}