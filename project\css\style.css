/* ===== STYLES GÉNÉRAUX ===== */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
    border-bottom: none;
    font-weight: 600;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* ===== GRAPHIQUES ===== */
.chart-container {
    position: relative;
    height: 400px;
    margin: 1rem 0;
}

/* ===== TABLEAUX ===== */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    background-color: #f8f9fa;
}

.table tbody tr:hover {
    background-color: #f5f5f5;
}

/* ===== BADGES ET STATUTS ===== */
.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

.status-badge {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease;
}

.slide-in-up {
    animation: slideInUp 0.5s ease;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }

    .chart-container {
        height: 300px;
    }

    .table-responsive {
        font-size: 0.875rem;
    }
}

/* ===== UTILITAIRES ===== */
.text-primary {
    color: #007bff !important;
}

.text-success {
    color: #28a745 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-info {
    color: #17a2b8 !important;
}

.bg-primary {
    background-color: #007bff !important;
}

.bg-success {
    background-color: #28a745 !important;
}

.bg-warning {
    background-color: #ffc107 !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.bg-info {
    background-color: #17a2b8 !important;
}

/* ===== FOOTER ===== */
.footer {
    background-color: #343a40;
    color: white;
    padding: 1rem 0;
    margin-top: 2rem;
}

.footer a {
    color: #adb5bd;
    text-decoration: none;
}

.footer a:hover {
    color: white;
}

/* ===== START PRODUCTION PAR HEURE STYLES ===== */
#startHourlyProductionBtn {
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.3s ease;
}

#startHourlyProductionBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.modal-header.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

#hourlyConfigSummary {
    background: rgba(13, 110, 253, 0.05);
    border-radius: 8px;
    padding: 1rem;
}

#hourlyConfigSummary p {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

#hourlyConfigSummary span {
    color: #0d6efd;
    font-weight: 600;
}

/* Alertes de notification */
.alert.position-fixed {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive pour la modal */
@media (max-width: 768px) {
    .modal-dialog.modal-lg {
        margin: 10px;
        max-width: calc(100% - 20px);
    }

    #startHourlyProductionBtn {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
    }
}

/* ===== DÉTAILS MACHINE STYLES ===== */
#machineDetailsSection {
    animation: slideInUp 0.5s ease-out;
}

#machineDetailsSection .card {
    border: 2px solid #007bff;
    box-shadow: 0 4px 20px rgba(0, 123, 255, 0.15);
}

#machineDetailsSection .progress {
    border-radius: 10px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

#machineDetailsSection .progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
}

#machineDetailsSection .table td {
    border: none;
    padding: 0.5rem 0;
}

#machineDetailsSection .card.bg-light:hover {
    background-color: #e9ecef !important;
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

#machineDetailsSection .btn-sm {
    border-radius: 6px;
    font-weight: 500;
}

#machineDetailsSection .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
}

/* Animation pour l'apparition des détails */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive pour les détails machine */
@media (max-width: 768px) {
    #machineDetailsSection .row>div {
        margin-bottom: 1rem;
    }

    #machineDetailsSection .d-flex.flex-wrap {
        flex-direction: column;
    }

    #machineDetailsSection .btn-sm {
        margin-bottom: 0.5rem;
        width: 100%;
    }
}

/* ===== TOOLTIPS PERSONNALISÉS ===== */
.custom-chart-tooltip {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid #007bff;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 8px 32px rgba(0, 123, 255, 0.3);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 13px;
    max-width: 280px;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    backdrop-filter: blur(10px);
}

.custom-chart-tooltip.show {
    opacity: 1;
    transform: translateY(0);
}

.custom-chart-tooltip .tooltip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9ecef;
}

.custom-chart-tooltip .tooltip-header strong {
    color: #007bff;
    font-size: 15px;
    font-weight: 600;
}

.custom-chart-tooltip .badge {
    font-size: 10px;
    padding: 4px 8px;
    border-radius: 6px;
}

.custom-chart-tooltip .tooltip-body {
    margin-bottom: 12px;
}

.custom-chart-tooltip .tooltip-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    padding: 4px 0;
}

.custom-chart-tooltip .metric-label {
    color: #6c757d;
    font-weight: 500;
}

.custom-chart-tooltip .metric-value {
    font-weight: 600;
    color: #212529;
}

.custom-chart-tooltip .tooltip-footer {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    border-top: 1px solid #e9ecef;
    padding-top: 8px;
}

/* ===== INDICATEURS DE STATUT SUR GRAPHIQUE ===== */
.chart-status-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

.status-indicator {
    position: absolute;
    font-size: 16px;
    animation: statusPulse 2s infinite;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.status-indicator i {
    display: block;
    transition: transform 0.3s ease;
}

.status-indicator:hover i {
    transform: scale(1.2);
}

@keyframes statusPulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }
}

/* ===== SÉLECTION MULTIPLE ===== */
#machine-selection-info {
    position: sticky;
    top: 20px;
    z-index: 100;
    margin-bottom: 20px;
}

#machine-selection-info .alert {
    border-radius: 10px;
    border: 2px solid #17a2b8;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    animation: slideInDown 0.5s ease-out;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== BOUTONS FAVORIS ===== */
.favorite-btn {
    border: none;
    background: transparent;
    padding: 4px 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.favorite-btn:hover {
    background-color: rgba(255, 193, 7, 0.1);
    transform: scale(1.1);
}

.favorite-btn .fa-star {
    font-size: 16px;
    transition: all 0.3s ease;
}

/* ===== AMÉLIORATIONS GRAPHIQUE ===== */
#productionChart {
    transition: filter 0.3s ease;
    cursor: default;
}

#productionChart:hover {
    filter: brightness(1.05);
}

/* Conteneur du graphique avec position relative pour les overlays */
.chart-container {
    position: relative;
    overflow: visible;
}

/* ===== ANIMATIONS AVANCÉES ===== */
@keyframes chartBarHover {
    0% {
        transform: scaleY(1);
    }

    50% {
        transform: scaleY(1.05);
    }

    100% {
        transform: scaleY(1);
    }
}

/* ===== RESPONSIVE POUR NOUVELLES FONCTIONNALITÉS ===== */
@media (max-width: 768px) {
    .custom-chart-tooltip {
        max-width: 250px;
        font-size: 12px;
        padding: 12px;
    }

    .status-indicator {
        font-size: 14px;
    }

    #machine-selection-info .alert {
        font-size: 13px;
        padding: 10px;
    }
}

/* ===== NOTIFICATIONS RAPIDES ===== */
.quick-preview-notification {
    animation: slideInRight 0.3s ease-out;
    backdrop-filter: blur(10px);
    font-size: 13px;
    min-width: 280px;
}

.quick-preview-notification:hover {
    transform: translateX(-5px);
    transition: transform 0.3s ease;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== AMÉLIORATIONS POUR L'ACCESSIBILITÉ ===== */
.chart-container:focus-within {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    border-radius: 4px;
}

/* Indicateur de focus pour la navigation clavier */
.keyboard-focus-indicator {
    position: absolute;
    border: 3px solid #007bff;
    border-radius: 4px;
    pointer-events: none;
    z-index: 20;
    animation: focusPulse 1.5s infinite;
}

@keyframes focusPulse {

    0%,
    100% {
        opacity: 1;
        transform: scale(1);
    }

    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

/* ===== AMÉLIORATIONS DROPDOWN FAVORIS ===== */
#favoritesList .dropdown-item {
    transition: all 0.3s ease;
    border-radius: 6px;
    margin: 2px 4px;
}

#favoritesList .dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

#favoritesList .btn-outline-danger {
    opacity: 0;
    transition: opacity 0.3s ease;
}

#favoritesList .dropdown-item:hover .btn-outline-danger {
    opacity: 1;
}

/* ===== STYLES POUR INSTRUCTIONS D'UTILISATION ===== */
.alert-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
}

.alert-light kbd {
    background-color: #495057;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
}

/* ===== ANIMATIONS POUR BARRES DU GRAPHIQUE ===== */
@keyframes barSelection {
    0% {
        transform: scaleY(1) scaleX(1);
    }

    50% {
        transform: scaleY(1.1) scaleX(1.05);
    }

    100% {
        transform: scaleY(1) scaleX(1);
    }
}

/* ===== RESPONSIVE AVANCÉ ===== */
@media (max-width: 576px) {
    .quick-preview-notification {
        min-width: 250px;
        font-size: 12px;
        padding: 10px;
        top: 10px !important;
        right: 10px !important;
        left: 10px !important;
        width: auto !important;
    }

    .custom-chart-tooltip {
        max-width: 200px;
        font-size: 11px;
        padding: 10px;
    }

    .status-indicator {
        font-size: 12px;
    }

    .alert-light kbd {
        font-size: 10px;
        padding: 1px 4px;
    }
}