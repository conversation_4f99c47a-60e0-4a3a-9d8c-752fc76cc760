/* ===== STYLES GÉNÉRAUX ===== */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
    border-bottom: none;
    font-weight: 600;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* ===== GRAPHIQUES ===== */
.chart-container {
    position: relative;
    height: 400px;
    margin: 1rem 0;
}

/* ===== TABLEAUX ===== */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    background-color: #f8f9fa;
}

.table tbody tr:hover {
    background-color: #f5f5f5;
}

/* ===== BADGES ET STATUTS ===== */
.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

.status-badge {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from { 
        transform: translateY(20px); 
        opacity: 0; 
    }
    to { 
        transform: translateY(0); 
        opacity: 1; 
    }
}

.fade-in {
    animation: fadeIn 0.5s ease;
}

.slide-in-up {
    animation: slideInUp 0.5s ease;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .chart-container {
        height: 300px;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* ===== UTILITAIRES ===== */
.text-primary { color: #007bff !important; }
.text-success { color: #28a745 !important; }
.text-warning { color: #ffc107 !important; }
.text-danger { color: #dc3545 !important; }
.text-info { color: #17a2b8 !important; }

.bg-primary { background-color: #007bff !important; }
.bg-success { background-color: #28a745 !important; }
.bg-warning { background-color: #ffc107 !important; }
.bg-danger { background-color: #dc3545 !important; }
.bg-info { background-color: #17a2b8 !important; }

/* ===== FOOTER ===== */
.footer {
    background-color: #343a40;
    color: white;
    padding: 1rem 0;
    margin-top: 2rem;
}

.footer a {
    color: #adb5bd;
    text-decoration: none;
}

.footer a:hover {
    color: white;
}
