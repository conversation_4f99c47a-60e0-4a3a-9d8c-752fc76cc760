/* ===== STYLES GÉNÉRAUX ===== */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    color: #333;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
    border-bottom: none;
    font-weight: 600;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* ===== GRAPHIQUES ===== */
.chart-container {
    position: relative;
    height: 400px;
    margin: 1rem 0;
}

/* ===== TABLEAUX ===== */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    background-color: #f8f9fa;
}

.table tbody tr:hover {
    background-color: #f5f5f5;
}

/* ===== BADGES ET STATUTS ===== */
.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

.status-badge {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease;
}

.slide-in-up {
    animation: slideInUp 0.5s ease;
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }

    .chart-container {
        height: 300px;
    }

    .table-responsive {
        font-size: 0.875rem;
    }
}

/* ===== UTILITAIRES ===== */
.text-primary {
    color: #007bff !important;
}

.text-success {
    color: #28a745 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-info {
    color: #17a2b8 !important;
}

.bg-primary {
    background-color: #007bff !important;
}

.bg-success {
    background-color: #28a745 !important;
}

.bg-warning {
    background-color: #ffc107 !important;
}

.bg-danger {
    background-color: #dc3545 !important;
}

.bg-info {
    background-color: #17a2b8 !important;
}

/* ===== FOOTER ===== */
.footer {
    background-color: #343a40;
    color: white;
    padding: 1rem 0;
    margin-top: 2rem;
}

.footer a {
    color: #adb5bd;
    text-decoration: none;
}

.footer a:hover {
    color: white;
}

/* ===== START PRODUCTION PAR HEURE STYLES ===== */
#startHourlyProductionBtn {
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.3s ease;
}

#startHourlyProductionBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.modal-header.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

#hourlyConfigSummary {
    background: rgba(13, 110, 253, 0.05);
    border-radius: 8px;
    padding: 1rem;
}

#hourlyConfigSummary p {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

#hourlyConfigSummary span {
    color: #0d6efd;
    font-weight: 600;
}

/* Alertes de notification */
.alert.position-fixed {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive pour la modal */
@media (max-width: 768px) {
    .modal-dialog.modal-lg {
        margin: 10px;
        max-width: calc(100% - 20px);
    }

    #startHourlyProductionBtn {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
    }
}

/* ===== DÉTAILS MACHINE STYLES ===== */
#machineDetailsSection {
    animation: slideInUp 0.5s ease-out;
}

#machineDetailsSection .card {
    border: 2px solid #007bff;
    box-shadow: 0 4px 20px rgba(0, 123, 255, 0.15);
}

#machineDetailsSection .progress {
    border-radius: 10px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

#machineDetailsSection .progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
}

#machineDetailsSection .table td {
    border: none;
    padding: 0.5rem 0;
}

#machineDetailsSection .card.bg-light:hover {
    background-color: #e9ecef !important;
    transform: translateY(-2px);
    transition: all 0.3s ease;
}

#machineDetailsSection .btn-sm {
    border-radius: 6px;
    font-weight: 500;
}

#machineDetailsSection .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
}

/* Animation pour l'apparition des détails */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive pour les détails machine */
@media (max-width: 768px) {
    #machineDetailsSection .row>div {
        margin-bottom: 1rem;
    }

    #machineDetailsSection .d-flex.flex-wrap {
        flex-direction: column;
    }

    #machineDetailsSection .btn-sm {
        margin-bottom: 0.5rem;
        width: 100%;
    }
}

/* ===== TOOLTIPS PERSONNALISÉS ===== */
.custom-chart-tooltip {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 2px solid #007bff;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 8px 32px rgba(0, 123, 255, 0.3);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 13px;
    max-width: 280px;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    backdrop-filter: blur(10px);
}

.custom-chart-tooltip.show {
    opacity: 1;
    transform: translateY(0);
}

.custom-chart-tooltip .tooltip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9ecef;
}

.custom-chart-tooltip .tooltip-header strong {
    color: #007bff;
    font-size: 15px;
    font-weight: 600;
}

.custom-chart-tooltip .badge {
    font-size: 10px;
    padding: 4px 8px;
    border-radius: 6px;
}

.custom-chart-tooltip .tooltip-body {
    margin-bottom: 12px;
}

.custom-chart-tooltip .tooltip-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    padding: 4px 0;
}

.custom-chart-tooltip .metric-label {
    color: #6c757d;
    font-weight: 500;
}

.custom-chart-tooltip .metric-value {
    font-weight: 600;
    color: #212529;
}

.custom-chart-tooltip .tooltip-footer {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    border-top: 1px solid #e9ecef;
    padding-top: 8px;
}

/* ===== INDICATEURS DE STATUT SUR GRAPHIQUE ===== */
.chart-status-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

.status-indicator {
    position: absolute;
    font-size: 16px;
    animation: statusPulse 2s infinite;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.status-indicator i {
    display: block;
    transition: transform 0.3s ease;
}

.status-indicator:hover i {
    transform: scale(1.2);
}

@keyframes statusPulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }
}

/* ===== SÉLECTION MULTIPLE ===== */
#machine-selection-info {
    position: sticky;
    top: 20px;
    z-index: 100;
    margin-bottom: 20px;
}

#machine-selection-info .alert {
    border-radius: 10px;
    border: 2px solid #17a2b8;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    animation: slideInDown 0.5s ease-out;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== BOUTONS FAVORIS ===== */
.favorite-btn {
    border: none;
    background: transparent;
    padding: 4px 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.favorite-btn:hover {
    background-color: rgba(255, 193, 7, 0.1);
    transform: scale(1.1);
}

.favorite-btn .fa-star {
    font-size: 16px;
    transition: all 0.3s ease;
}

/* ===== AMÉLIORATIONS GRAPHIQUE ===== */
#productionChart {
    transition: filter 0.3s ease;
    cursor: default;
}

#productionChart:hover {
    filter: brightness(1.05);
}

/* Conteneur du graphique avec position relative pour les overlays */
.chart-container {
    position: relative;
    overflow: visible;
}

/* ===== ANIMATIONS AVANCÉES ===== */
@keyframes chartBarHover {
    0% {
        transform: scaleY(1);
    }

    50% {
        transform: scaleY(1.05);
    }

    100% {
        transform: scaleY(1);
    }
}

/* ===== RESPONSIVE POUR NOUVELLES FONCTIONNALITÉS ===== */
@media (max-width: 768px) {
    .custom-chart-tooltip {
        max-width: 250px;
        font-size: 12px;
        padding: 12px;
    }

    .status-indicator {
        font-size: 14px;
    }

    #machine-selection-info .alert {
        font-size: 13px;
        padding: 10px;
    }
}

/* ===== NOTIFICATIONS RAPIDES ===== */
.quick-preview-notification {
    animation: slideInRight 0.3s ease-out;
    backdrop-filter: blur(10px);
    font-size: 13px;
    min-width: 280px;
}

.quick-preview-notification:hover {
    transform: translateX(-5px);
    transition: transform 0.3s ease;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== AMÉLIORATIONS POUR L'ACCESSIBILITÉ ===== */
.chart-container:focus-within {
    outline: 2px solid #007bff;
    outline-offset: 2px;
    border-radius: 4px;
}

/* Indicateur de focus pour la navigation clavier */
.keyboard-focus-indicator {
    position: absolute;
    border: 3px solid #007bff;
    border-radius: 4px;
    pointer-events: none;
    z-index: 20;
    animation: focusPulse 1.5s infinite;
}

@keyframes focusPulse {

    0%,
    100% {
        opacity: 1;
        transform: scale(1);
    }

    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

/* ===== AMÉLIORATIONS DROPDOWN FAVORIS ===== */
#favoritesList .dropdown-item {
    transition: all 0.3s ease;
    border-radius: 6px;
    margin: 2px 4px;
}

#favoritesList .dropdown-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

#favoritesList .btn-outline-danger {
    opacity: 0;
    transition: opacity 0.3s ease;
}

#favoritesList .dropdown-item:hover .btn-outline-danger {
    opacity: 1;
}

/* ===== STYLES POUR INSTRUCTIONS D'UTILISATION ===== */
.alert-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
}

.alert-light kbd {
    background-color: #495057;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
}

/* ===== ANIMATIONS POUR BARRES DU GRAPHIQUE ===== */
@keyframes barSelection {
    0% {
        transform: scaleY(1) scaleX(1);
    }

    50% {
        transform: scaleY(1.1) scaleX(1.05);
    }

    100% {
        transform: scaleY(1) scaleX(1);
    }
}

/* ===== RESPONSIVE AVANCÉ ===== */
@media (max-width: 576px) {
    .quick-preview-notification {
        min-width: 250px;
        font-size: 12px;
        padding: 10px;
        top: 10px !important;
        right: 10px !important;
        left: 10px !important;
        width: auto !important;
    }

    .custom-chart-tooltip {
        max-width: 200px;
        font-size: 11px;
        padding: 10px;
    }

    .status-indicator {
        font-size: 12px;
    }

    .alert-light kbd {
        font-size: 10px;
        padding: 1px 4px;
    }
}

/* ===== EFFETS VISUELS AVANCÉS ===== */

/* Animation de ripple pour les clics */
@keyframes rippleExpand {
    0% {
        transform: scale(0);
        opacity: 1;
    }

    100% {
        transform: scale(4);
        opacity: 0;
    }
}

.chart-ripple-effect {
    animation: rippleExpand 0.6s ease-out forwards;
}

/* Indicateurs de machine améliorés */
.machine-indicator-container {
    cursor: pointer;
    transition: all 0.3s ease;
}

.machine-indicator-container:hover {
    z-index: 1000;
}

/* Animations de pulsation pour les statuts */
@keyframes pulseSuccess {

    0%,
    100% {
        color: #28a745;
        text-shadow: 0 0 5px rgba(40, 167, 69, 0.5);
    }

    50% {
        color: #34ce57;
        text-shadow: 0 0 10px rgba(40, 167, 69, 0.8);
    }
}

@keyframes pulseWarning {

    0%,
    100% {
        color: #ffc107;
        text-shadow: 0 0 5px rgba(255, 193, 7, 0.5);
    }

    50% {
        color: #ffcd39;
        text-shadow: 0 0 10px rgba(255, 193, 7, 0.8);
    }
}

@keyframes pulseDanger {

    0%,
    100% {
        color: #dc3545;
        text-shadow: 0 0 5px rgba(220, 53, 69, 0.5);
    }

    50% {
        color: #e4606d;
        text-shadow: 0 0 10px rgba(220, 53, 69, 0.8);
    }
}

.pulse-success {
    animation: pulseSuccess 2s infinite;
}

.pulse-warning {
    animation: pulseWarning 2s infinite;
}

.pulse-danger {
    animation: pulseDanger 2s infinite;
}

/* Mini barres d'efficacité */
.efficiency-mini-bar {
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.efficiency-mini-bar:hover {
    transform: scaleY(1.5);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

/* Indicateurs de tendance */
.trend-indicator {
    transition: all 0.3s ease;
}

.trend-indicator:hover {
    transform: scale(1.3);
}

/* Effets de survol pour le graphique */
#productionChart {
    transition: all 0.3s ease;
}

#productionChart.chart-active {
    filter: brightness(1.05) contrast(1.1);
    transform: scale(1.01);
}

/* Bouton de contrôle des sons */
.sound-control-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.sound-control-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
}

.sound-control-btn.sound-enabled {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.sound-control-btn.sound-disabled {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
}

/* Animations pour les interactions */
@keyframes chartInteraction {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.02);
    }

    100% {
        transform: scale(1);
    }
}

.chart-interaction-active {
    animation: chartInteraction 0.3s ease-out;
}

/* Effets de particules pour les actions importantes */
@keyframes particleFloat {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }

    100% {
        opacity: 0;
        transform: translateY(-50px) scale(0.5);
    }
}

.particle-effect {
    position: absolute;
    pointer-events: none;
    z-index: 9999;
    animation: particleFloat 1s ease-out forwards;
}

/* Amélioration des tooltips avec glassmorphism */
.custom-chart-tooltip {
    backdrop-filter: blur(20px) saturate(180%);
    background: rgba(255, 255, 255, 0.85);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Mode sombre pour les tooltips (optionnel) */
@media (prefers-color-scheme: dark) {
    .custom-chart-tooltip {
        background: rgba(30, 30, 30, 0.9);
        color: white;
        border-color: rgba(255, 255, 255, 0.2);
    }

    .custom-chart-tooltip .tooltip-header strong {
        color: #4dabf7;
    }
}

/* Responsive pour les nouveaux éléments */
@media (max-width: 768px) {
    .machine-indicator-container {
        transform: scale(0.8);
    }

    .sound-control-btn {
        width: 40px;
        height: 40px;
        bottom: 15px;
        right: 15px;
    }

    .efficiency-mini-bar {
        width: 15px;
        height: 2px;
    }
}

/* ===== ENHANCED CHART BAR STYLING ===== */

/* Chart container enhancements */
.chart-container {
    position: relative;
    overflow: visible;
}

/* Value labels on bars */
.chart-value-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

.chart-value-label {
    position: absolute;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: bold;
    font-size: 12px;
    text-align: center;
    padding: 4px 6px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.chart-value-label:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Hover effects overlay */
.chart-hover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 5;
}

.bar-hover-effect {
    position: absolute;
    background: rgba(0, 123, 255, 0.1);
    border: 2px solid rgba(0, 123, 255, 0.3);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 123, 255, 0.2);
    transition: all 0.3s ease;
    opacity: 0;
    transform: scale(1.05);
    backdrop-filter: blur(2px);
}

/* Enhanced chart canvas styling */
#productionChart {
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

#productionChart:hover {
    box-shadow: 0 6px 30px rgba(0, 0, 0, 0.15);
}

/* Chart animation enhancements */
@keyframes barGlow {
    0% {
        box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
    }

    50% {
        box-shadow: 0 0 20px rgba(0, 123, 255, 0.6);
    }

    100% {
        box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
    }
}

.chart-bar-glow {
    animation: barGlow 2s ease-in-out infinite;
}

/* Performance-based bar styling */
.bar-performance-high {
    background: linear-gradient(180deg, #28a745 0%, #20c997 100%);
    border-color: #1e7e34;
}

.bar-performance-medium {
    background: linear-gradient(180deg, #ffc107 0%, #fd7e14 100%);
    border-color: #d39e00;
}

.bar-performance-low {
    background: linear-gradient(180deg, #dc3545 0%, #e83e8c 100%);
    border-color: #bd2130;
}

/* Responsive chart enhancements */
@media (max-width: 768px) {
    .chart-value-label {
        font-size: 10px;
        padding: 2px 4px;
    }

    .bar-hover-effect {
        border-width: 1px;
        box-shadow: 0 2px 10px rgba(0, 123, 255, 0.2);
    }

    #productionChart {
        border-radius: 6px;
    }
}

@media (max-width: 480px) {
    .chart-value-label {
        font-size: 9px;
        padding: 1px 3px;
    }

    .chart-value-overlay,
    .chart-hover-overlay {
        display: none;
        /* Hide overlays on very small screens for performance */
    }
}

/* Chart loading animation */
@keyframes chartLoad {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.chart-container.loading {
    animation: chartLoad 0.8s ease-out;
}

/* Enhanced status indicators positioning */
.chart-status-overlay {
    z-index: 15;
    /* Above value labels */
}

.machine-indicator-container {
    z-index: 16;
    /* Above everything else */
}

/* Chart interaction feedback */
.chart-interaction-feedback {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 123, 255, 0.9);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 20;
}

.chart-interaction-feedback.show {
    opacity: 1;
}

/* Print styles for charts */
@media print {

    .chart-hover-overlay,
    .chart-value-overlay {
        display: none;
    }

    #productionChart {
        box-shadow: none;
        border: 1px solid #000;
    }

    .chart-value-label {
        background: white !important;
        color: black !important;
        border-color: black !important;
    }
}