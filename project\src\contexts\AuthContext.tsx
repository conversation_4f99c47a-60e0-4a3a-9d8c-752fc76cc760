import React, { createContext, useContext, useState, useEffect } from 'react';
import { User } from '../types';

interface AuthContextType {
  user: User | null;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  isAuthenticated: boolean;
  hasPermission: (permission: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Mock users for demonstration
const mockUsers: User[] = [
  {
    id: '1',
    username: 'manager',
    email: '<EMAIL>',
    role: 'manager',
    firstName: '<PERSON>',
    lastName: 'Dupont',
    isActive: true,
    createdAt: new Date(),
  },
  {
    id: '2',
    username: 'comptable',
    email: '<EMAIL>',
    role: 'accountant',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    isActive: true,
    createdAt: new Date(),
  },
  {
    id: '3',
    username: 'utilisateur',
    email: '<EMAIL>',
    role: 'user',
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    isActive: true,
    createdAt: new Date(),
  },
  {
    id: '4',
    username: 'mousstafa',
    email: '<EMAIL>',
    role: 'manager',
    firstName: 'Mousstafa',
    lastName: 'Admin',
    isActive: true,
    createdAt: new Date(),
  },
];

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    const savedUser = localStorage.getItem('aptiv_user');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
    }
  }, []);

  const login = async (username: string, password: string): Promise<boolean> => {
    // Mock authentication - in real app, this would call an API
    const foundUser = mockUsers.find(u => u.username === username);
    
    // Check for custom credentials
    if (username === 'mousstafa' && password === '9876') {
      const customUser = mockUsers.find(u => u.username === 'mousstafa');
      if (customUser) {
        setUser(customUser);
        localStorage.setItem('aptiv_user', JSON.stringify(customUser));
        return true;
      }
    }
    
    // Check for demo accounts
    if (foundUser && password === 'password') {
      setUser(foundUser);
      localStorage.setItem('aptiv_user', JSON.stringify(foundUser));
      return true;
    }
    
    return false;
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('aptiv_user');
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    const permissions = {
      manager: ['read', 'write', 'delete', 'reports', 'users'],
      accountant: ['read', 'write', 'reports'],
      user: ['read'],
    };

    return permissions[user.role]?.includes(permission) || false;
  };

  return (
    <AuthContext.Provider value={{
      user,
      login,
      logout,
      isAuthenticated: !!user,
      hasPermission,
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};