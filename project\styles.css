/* Barcode Generator Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    max-width: 600px;
    width: 100%;
    text-align: center;
}

h1 {
    color: #333;
    margin-bottom: 30px;
    font-size: 2.5rem;
    font-weight: 300;
}

.input-group {
    margin-bottom: 30px;
}

#barcodeInput {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

#barcodeInput:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.button-group {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 30px;
}

button {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

#generateButton {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

#generateButton:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.format-button {
    background: #f8f9fa;
    color: #495057;
    border: 2px solid #e9ecef;
}

.format-button:hover {
    background: #e9ecef;
    border-color: #dee2e6;
}

.format-button.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

#downloadButton {
    background: #28a745;
    color: white;
    margin-top: 20px;
    display: none;
}

#downloadButton:hover {
    background: #218838;
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3);
}

.barcode-container {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 30px;
    margin-top: 30px;
    min-height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed #dee2e6;
}

#barcode {
    max-width: 100%;
    height: auto;
}

.barcode-info {
    margin-top: 20px;
    padding: 15px;
    background: #e3f2fd;
    border-radius: 8px;
    display: none;
}

.barcode-info h3 {
    color: #1976d2;
    margin-bottom: 10px;
}

.barcode-info p {
    color: #424242;
    margin: 5px 0;
}

.error-message {
    background: #ffebee;
    color: #c62828;
    padding: 15px;
    border-radius: 8px;
    margin-top: 20px;
    display: none;
}

.scanner-section {
    margin-top: 40px;
    padding-top: 40px;
    border-top: 2px solid #e9ecef;
}

.scanner-section h2 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.8rem;
}

#cameraContainer {
    position: relative;
    max-width: 400px;
    margin: 0 auto 20px;
    border-radius: 10px;
    overflow: hidden;
    background: #000;
}

#camera {
    width: 100%;
    height: auto;
    display: block;
}

.scanner-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    height: 60%;
    border: 2px solid #ff4444;
    border-radius: 10px;
    pointer-events: none;
}

.scanner-overlay::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid rgba(255, 68, 68, 0.3);
    border-radius: 10px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

#startScanButton, #stopScanButton {
    background: #ff4444;
    color: white;
    margin: 10px;
}

#startScanButton:hover, #stopScanButton:hover {
    background: #cc3333;
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(255, 68, 68, 0.3);
}

#stopScanButton {
    display: none;
}

.scan-result {
    background: #e8f5e8;
    border: 2px solid #4caf50;
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
    display: none;
}

.scan-result h3 {
    color: #2e7d32;
    margin-bottom: 10px;
}

.scan-result p {
    color: #424242;
    word-break: break-all;
}

@media (max-width: 768px) {
    .container {
        padding: 20px;
        margin: 10px;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    .button-group {
        flex-direction: column;
        align-items: center;
    }
    
    button {
        width: 100%;
        max-width: 300px;
    }
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
