#!/usr/bin/env python3
"""
Script pour importer les données de codes-barres depuis Excel vers la base de données
Aptiv - Twisting Monitoring Tool
"""

import pandas as pd
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
import os
import json
from datetime import datetime

def create_excel_with_barcode_data():
    """Crée un fichier Excel avec toutes les données de codes-barres"""
    
    # Lire les données CSV
    csv_file = 'barcode-data-simple.csv'
    if not os.path.exists(csv_file):
        print(f"Erreur: Le fichier {csv_file} n'existe pas!")
        return
    
    df = pd.read_csv(csv_file)
    
    # Créer un nouveau classeur Excel
    wb = openpyxl.Workbook()
    
    # Supprimer la feuille par défaut
    wb.remove(wb.active)
    
    # Styles pour l'en-tête
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    # Style pour les bordures
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # === FEUILLE 1: TOUS LES PRODUITS ===
    ws_all = wb.create_sheet("Tous les Produits")
    
    # Ajouter les données
    for r in dataframe_to_rows(df, index=False, header=True):
        ws_all.append(r)
    
    # Formater l'en-tête
    for cell in ws_all[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = thin_border
    
    # Ajuster la largeur des colonnes
    column_widths = {
        'A': 25,  # Code
        'B': 12,  # Format
        'C': 35,  # Name
        'D': 15,  # Category
        'E': 50,  # Description
        'F': 10,  # Price
        'G': 8,   # Stock
        'H': 10,  # MinStock
        'I': 20,  # Supplier
        'J': 15,  # Location
        'K': 12   # Status
    }
    
    for col, width in column_widths.items():
        ws_all.column_dimensions[col].width = width
    
    # === FEUILLE 2: PRODUITS APTIV ===
    ws_aptiv = wb.create_sheet("Produits Aptiv")
    aptiv_products = df[df['Code'].str.startswith('APTIV-') & (df['Category'] != 'Test')].copy()
    
    for r in dataframe_to_rows(aptiv_products, index=False, header=True):
        ws_aptiv.append(r)
    
    # Formater l'en-tête
    for cell in ws_aptiv[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = thin_border
    
    for col, width in column_widths.items():
        ws_aptiv.column_dimensions[col].width = width
    
    # === FEUILLE 3: PRODUITS STANDARDS (EAN/UPC) ===
    ws_standard = wb.create_sheet("Produits Standards")
    standard_products = df[df['Format'].isin(['EAN13', 'EAN8', 'UPC']) & (df['Category'] != 'Test')].copy()
    
    for r in dataframe_to_rows(standard_products, index=False, header=True):
        ws_standard.append(r)
    
    # Formater l'en-tête
    for cell in ws_standard[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = thin_border
    
    for col, width in column_widths.items():
        ws_standard.column_dimensions[col].width = width
    
    # === FEUILLE 4: CODES INTERNES ===
    ws_internal = wb.create_sheet("Codes Internes")
    internal_codes = df[df['Format'] == 'CODE39'].copy()
    
    for r in dataframe_to_rows(internal_codes, index=False, header=True):
        ws_internal.append(r)
    
    # Formater l'en-tête
    for cell in ws_internal[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = thin_border
    
    for col, width in column_widths.items():
        ws_internal.column_dimensions[col].width = width
    
    # === FEUILLE 5: EMBALLAGES ===
    ws_packaging = wb.create_sheet("Emballages")
    packaging_codes = df[df['Format'] == 'ITF14'].copy()
    
    for r in dataframe_to_rows(packaging_codes, index=False, header=True):
        ws_packaging.append(r)
    
    # Formater l'en-tête
    for cell in ws_packaging[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = thin_border
    
    for col, width in column_widths.items():
        ws_packaging.column_dimensions[col].width = width
    
    # === FEUILLE 6: STOCK FAIBLE ===
    ws_low_stock = wb.create_sheet("Stock Faible")
    
    # Filtrer les produits avec stock faible
    low_stock_products = df[
        (df['Stock'].notna()) & 
        (df['MinStock'].notna()) & 
        (df['Stock'].astype(float) <= df['MinStock'].astype(float))
    ].copy()
    
    for r in dataframe_to_rows(low_stock_products, index=False, header=True):
        ws_low_stock.append(r)
    
    # Formater l'en-tête avec couleur d'alerte
    alert_fill = PatternFill(start_color="FF6B6B", end_color="FF6B6B", fill_type="solid")
    for cell in ws_low_stock[1]:
        cell.font = header_font
        cell.fill = alert_fill
        cell.alignment = header_alignment
        cell.border = thin_border
    
    for col, width in column_widths.items():
        ws_low_stock.column_dimensions[col].width = width
    
    # === FEUILLE 7: STATISTIQUES ===
    ws_stats = wb.create_sheet("Statistiques")
    
    # Créer des statistiques
    stats_data = [
        ["Statistiques des Codes-Barres Aptiv", ""],
        ["", ""],
        ["Total des codes-barres", len(df)],
        ["Produits Aptiv", len(aptiv_products)],
        ["Produits Standards", len(standard_products)],
        ["Codes internes", len(internal_codes)],
        ["Codes d'emballage", len(packaging_codes)],
        ["Produits en stock faible", len(low_stock_products)],
        ["", ""],
        ["Par Format:", ""],
        ["CODE128", len(df[df['Format'] == 'CODE128'])],
        ["CODE39", len(df[df['Format'] == 'CODE39'])],
        ["EAN13", len(df[df['Format'] == 'EAN13'])],
        ["EAN8", len(df[df['Format'] == 'EAN8'])],
        ["UPC", len(df[df['Format'] == 'UPC'])],
        ["ITF14", len(df[df['Format'] == 'ITF14'])],
        ["", ""],
        ["Par Catégorie:", ""],
    ]
    
    # Ajouter les statistiques par catégorie
    categories = df['Category'].value_counts()
    for category, count in categories.items():
        if pd.notna(category):
            stats_data.append([category, count])
    
    # Ajouter les données de statistiques
    for row in stats_data:
        ws_stats.append(row)
    
    # Formater la feuille de statistiques
    ws_stats['A1'].font = Font(bold=True, size=16, color="366092")
    ws_stats.column_dimensions['A'].width = 30
    ws_stats.column_dimensions['B'].width = 15
    
    # === FEUILLE 8: FORMATS DE CODES-BARRES ===
    ws_formats = wb.create_sheet("Formats")
    
    format_info = [
        ["Format", "Nom", "Description", "Charset", "Longueur", "Usage"],
        ["CODE128", "Code 128", "Alphanumeric, haute densité", "ASCII complet", "Variable (1-50)", "Produits internes, traçabilité"],
        ["CODE39", "Code 39", "Alphanumeric, largement utilisé", "A-Z, 0-9, -. $/+%", "Variable (1-43)", "Identification interne, maintenance"],
        ["EAN13", "EAN-13", "13 chiffres, produits de consommation", "0-9 uniquement", "13", "Produits commerciaux, retail"],
        ["EAN8", "EAN-8", "8 chiffres, petits produits", "0-9 uniquement", "8", "Petits produits, espace limité"],
        ["UPC", "UPC-A", "12 chiffres, États-Unis/Canada", "0-9 uniquement", "12", "Marché nord-américain"],
        ["ITF14", "ITF-14", "14 chiffres, emballages", "0-9 uniquement", "14", "Caisses, palettes, logistique"]
    ]
    
    for row in format_info:
        ws_formats.append(row)
    
    # Formater l'en-tête des formats
    for cell in ws_formats[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = thin_border
    
    # Ajuster les largeurs
    format_widths = {'A': 12, 'B': 15, 'C': 35, 'D': 25, 'E': 15, 'F': 30}
    for col, width in format_widths.items():
        ws_formats.column_dimensions[col].width = width
    
    # Sauvegarder le fichier
    output_file = f"../Nouveau dossier (8)/Data_twist_updated_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    wb.save(output_file)
    
    print(f"✅ Fichier Excel créé avec succès: {output_file}")
    print(f"📊 {len(df)} codes-barres ajoutés")
    print(f"📦 {len(aptiv_products)} produits Aptiv")
    print(f"🏷️ {len(internal_codes)} codes internes")
    print(f"📋 {len(low_stock_products)} produits en stock faible")

def update_existing_excel(excel_file):
    """Met à jour un fichier Excel existant avec les nouvelles données"""
    
    if not os.path.exists(excel_file):
        print(f"Erreur: Le fichier {excel_file} n'existe pas!")
        return
    
    # Lire les nouvelles données
    csv_file = 'barcode-data-simple.csv'
    if not os.path.exists(csv_file):
        print(f"Erreur: Le fichier {csv_file} n'existe pas!")
        return
    
    df_new = pd.read_csv(csv_file)
    
    # Lire le fichier Excel existant
    try:
        df_existing = pd.read_excel(excel_file)
        
        # Combiner les données (éviter les doublons basés sur le code)
        df_combined = pd.concat([df_existing, df_new]).drop_duplicates(subset=['Code'], keep='last')
        
        # Sauvegarder le fichier mis à jour
        output_file = f"../Nouveau dossier (8)/Data_twist_merged_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        df_combined.to_excel(output_file, index=False)
        
        print(f"✅ Fichier Excel mis à jour: {output_file}")
        print(f"📊 {len(df_combined)} codes-barres au total")
        print(f"➕ {len(df_new)} nouveaux codes ajoutés")
        
    except Exception as e:
        print(f"Erreur lors de la lecture du fichier Excel: {e}")
        print("Création d'un nouveau fichier à la place...")
        create_excel_with_barcode_data()

def import_twist_excel_to_database(excel_file_path):
    """Importe les données du fichier Excel Data twist.xlsx vers la base de données de codes-barres"""

    if not os.path.exists(excel_file_path):
        print(f"❌ Erreur: Le fichier {excel_file_path} n'existe pas!")
        return False

    try:
        # Lire le fichier Excel
        print(f"📖 Lecture du fichier Excel: {excel_file_path}")
        df = pd.read_excel(excel_file_path)

        # Afficher les colonnes disponibles
        print(f"📋 Colonnes trouvées: {list(df.columns)}")
        print(f"📊 Nombre de lignes: {len(df)}")

        # Lire la base de données existante
        db_file = 'barcode-database.json'
        csv_file = 'barcode-data-simple.csv'

        # Charger la base de données JSON existante
        if os.path.exists(db_file):
            with open(db_file, 'r', encoding='utf-8') as f:
                database = json.load(f)
        else:
            database = {
                "aptivProducts": [],
                "standardProducts": [],
                "upcProducts": [],
                "internalCodes": [],
                "packagingCodes": [],
                "testData": [],
                "sampleScanHistory": [],
                "formatSpecs": {}
            }

        # Charger les données CSV existantes
        if os.path.exists(csv_file):
            existing_df = pd.read_csv(csv_file)
        else:
            existing_df = pd.DataFrame(columns=['Code', 'Format', 'Name', 'Category', 'Description', 'Price', 'Stock', 'MinStock', 'Supplier', 'Location', 'Status'])

        # Traiter chaque ligne du fichier Excel Data twist
        new_records = 0
        updated_records = 0

        for index, row in df.iterrows():
            # Extraire les données spécifiques au format Data twist
            unicos = str(row.get('unicos', '')).strip() if pd.notna(row.get('unicos')) else ''
            if not unicos:
                continue

            mc = str(row.get('Mc', '')).strip() if pd.notna(row.get('Mc')) else ''
            group = str(row.get('Group', '')).strip() if pd.notna(row.get('Group')) else ''
            ordre = float(row.get('Ordre', 0)) if pd.notna(row.get('Ordre')) else 0
            reste = float(row.get('Reste', 0)) if pd.notna(row.get('Reste')) else 0
            status = str(row.get('Status', '')).strip() if pd.notna(row.get('Status')) else 'Pending'
            scanned = str(row.get('Scanned', '')).strip() if pd.notna(row.get('Scanned')) else ''
            action = str(row.get('Action', '')).strip() if pd.notna(row.get('Action')) else ''

            # Créer un nom descriptif basé sur les données
            name = f"Composant {group} - {unicos}"
            description = f"Machine: {mc}, Ordre: {ordre}, Reste: {reste}"
            if action:
                description += f", Actions: {action}"

            # Déterminer le statut
            final_status = 'Active'
            if status:
                final_status = status
            elif scanned:
                final_status = 'Scanned'
            elif reste == 0:
                final_status = 'Completed'

            # Créer l'enregistrement pour CSV
            csv_record = {
                'Code': unicos,
                'Format': 'CODE128',  # Format par défaut pour les codes internes
                'Name': name,
                'Category': group if group else 'Production',
                'Description': description,
                'Price': '',
                'Stock': int(reste) if reste > 0 else '',
                'MinStock': '',
                'Supplier': 'Aptiv Internal',
                'Location': mc if mc else '',
                'Status': final_status
            }

            # Vérifier si le code existe déjà dans le CSV
            existing_record = existing_df[existing_df['Code'] == unicos]
            if not existing_record.empty:
                # Mettre à jour l'enregistrement existant
                existing_df.loc[existing_df['Code'] == unicos] = list(csv_record.values())
                updated_records += 1
            else:
                # Ajouter un nouveau enregistrement
                new_row = pd.DataFrame([csv_record])
                existing_df = pd.concat([existing_df, new_row], ignore_index=True)
                new_records += 1

            # Ajouter à la base de données JSON
            json_record = {
                "code": unicos,
                "format": "CODE128",
                "name": name,
                "category": group if group else 'Production',
                "description": description,
                "type": "Production",
                "machine": mc,
                "order": ordre,
                "remaining": reste,
                "status": final_status,
                "lastUpdated": datetime.now().isoformat() + "Z"
            }

            if scanned:
                json_record["scanned"] = scanned
            if action:
                json_record["availableActions"] = action

            # Ajouter aux codes internes (production)
            existing_internal = next((p for p in database["internalCodes"] if p["code"] == unicos), None)
            if existing_internal:
                database["internalCodes"].remove(existing_internal)
            database["internalCodes"].append(json_record)

        # Sauvegarder le fichier CSV mis à jour
        existing_df.to_csv(csv_file, index=False, encoding='utf-8')

        # Sauvegarder la base de données JSON mise à jour
        with open(db_file, 'w', encoding='utf-8') as f:
            json.dump(database, f, indent=2, ensure_ascii=False)

        print(f"✅ Import terminé avec succès!")
        print(f"➕ {new_records} nouveaux enregistrements ajoutés")
        print(f"🔄 {updated_records} enregistrements mis à jour")
        print(f"📄 Fichier CSV mis à jour: {csv_file}")
        print(f"🗃️ Base de données JSON mise à jour: {db_file}")

        # Afficher quelques statistiques
        print(f"\n📊 Statistiques:")
        print(f"   - Machines: {len(df['Mc'].dropna().unique())}")
        print(f"   - Groupes: {len(df['Group'].dropna().unique())}")
        print(f"   - Total ordre: {df['Ordre'].sum():.0f}")
        print(f"   - Total reste: {df['Reste'].sum():.0f}")

        return True

    except Exception as e:
        print(f"❌ Erreur lors de l'import: {e}")
        import traceback
        traceback.print_exc()
        return False

def import_squib_commande_excel_to_database(excel_file_path):
    """Importe les données du fichier Excel Datat squib-Commande.xlsx vers la base de données de codes-barres"""

    if not os.path.exists(excel_file_path):
        print(f"❌ Erreur: Le fichier {excel_file_path} n'existe pas!")
        return False

    try:
        # Lire le fichier Excel
        print(f"📖 Lecture du fichier Excel: {excel_file_path}")
        df = pd.read_excel(excel_file_path)

        # Nettoyer les données - supprimer la première ligne qui contient les en-têtes
        df = df.iloc[1:].reset_index(drop=True)

        # Renommer les colonnes selon la structure observée
        df.columns = ['Empty', 'UNICO', 'APN_Cable', 'Ordre', 'Empty2', 'Qt_Box', 'Commande_Box']

        # Supprimer les lignes vides
        df = df.dropna(subset=['UNICO']).reset_index(drop=True)

        # Afficher les informations
        print(f"📋 Colonnes: UNICO, APN-Cable, Ordre, Qt/Box, Commande/Box")
        print(f"📊 Nombre de lignes valides: {len(df)}")

        # Lire la base de données existante
        db_file = 'barcode-database.json'
        csv_file = 'barcode-data-simple.csv'

        # Charger la base de données JSON existante
        if os.path.exists(db_file):
            with open(db_file, 'r', encoding='utf-8') as f:
                database = json.load(f)
        else:
            database = {
                "aptivProducts": [],
                "standardProducts": [],
                "upcProducts": [],
                "internalCodes": [],
                "packagingCodes": [],
                "testData": [],
                "sampleScanHistory": [],
                "formatSpecs": {}
            }

        # Charger les données CSV existantes
        if os.path.exists(csv_file):
            existing_df = pd.read_csv(csv_file)
        else:
            existing_df = pd.DataFrame(columns=['Code', 'Format', 'Name', 'Category', 'Description', 'Price', 'Stock', 'MinStock', 'Supplier', 'Location', 'Status'])

        # Traiter chaque ligne du fichier Excel Squib-Commande
        new_records = 0
        updated_records = 0

        for index, row in df.iterrows():
            # Extraire les données spécifiques au format Squib-Commande
            unico = str(row.get('UNICO', '')).strip() if pd.notna(row.get('UNICO')) else ''
            if not unico:
                continue

            apn_cable = str(row.get('APN_Cable', '')).strip() if pd.notna(row.get('APN_Cable')) else ''
            ordre = float(row.get('Ordre', 0)) if pd.notna(row.get('Ordre')) else 0
            qt_box = float(row.get('Qt_Box', 0)) if pd.notna(row.get('Qt_Box')) else 0
            commande_box = float(row.get('Commande_Box', 0)) if pd.notna(row.get('Commande_Box')) else 0

            # Créer un nom descriptif basé sur les données
            name = f"Squib {unico}"
            description = f"APN-Cable: {apn_cable}, Ordre: {ordre}, Qt/Box: {qt_box}, Commande/Box: {commande_box}"

            # Déterminer la catégorie basée sur le code UNICO
            category = 'Squib-Commande'
            if 'HAB' in unico:
                category = 'HAB-Squib'
            elif 'TAB' in unico:
                category = 'TAB-Squib'
            elif 'Brg' in unico:
                category = 'Brg-Squib'

            # Calculer le stock total
            total_stock = int(qt_box * commande_box) if qt_box > 0 and commande_box > 0 else int(ordre)

            # Créer l'enregistrement pour CSV
            csv_record = {
                'Code': unico,
                'Format': 'CODE128',  # Format spécifié par l'utilisateur
                'Name': name,
                'Category': category,
                'Description': description,
                'Price': '',
                'Stock': total_stock if total_stock > 0 else '',
                'MinStock': int(qt_box) if qt_box > 0 else '',
                'Supplier': 'Aptiv Squib',
                'Location': 'Commande',
                'Status': 'Active'
            }

            # Vérifier si le code existe déjà dans le CSV
            existing_record = existing_df[existing_df['Code'] == unico]
            if not existing_record.empty:
                # Mettre à jour l'enregistrement existant
                existing_df.loc[existing_df['Code'] == unico] = list(csv_record.values())
                updated_records += 1
            else:
                # Ajouter un nouveau enregistrement
                new_row = pd.DataFrame([csv_record])
                existing_df = pd.concat([existing_df, new_row], ignore_index=True)
                new_records += 1

            # Ajouter à la base de données JSON
            json_record = {
                "code": unico,
                "format": "CODE128",
                "name": name,
                "category": category,
                "description": description,
                "type": "Squib-Commande",
                "apnCable": apn_cable,
                "order": ordre,
                "quantityPerBox": qt_box,
                "commandePerBox": commande_box,
                "totalStock": total_stock,
                "status": "Active",
                "lastUpdated": datetime.now().isoformat() + "Z"
            }

            # Ajouter aux codes internes (squib-commande)
            existing_internal = next((p for p in database["internalCodes"] if p["code"] == unico), None)
            if existing_internal:
                database["internalCodes"].remove(existing_internal)
            database["internalCodes"].append(json_record)

        # Sauvegarder le fichier CSV mis à jour
        existing_df.to_csv(csv_file, index=False, encoding='utf-8')

        # Sauvegarder la base de données JSON mise à jour
        with open(db_file, 'w', encoding='utf-8') as f:
            json.dump(database, f, indent=2, ensure_ascii=False)

        print(f"✅ Import terminé avec succès!")
        print(f"➕ {new_records} nouveaux enregistrements ajoutés")
        print(f"🔄 {updated_records} enregistrements mis à jour")
        print(f"📄 Fichier CSV mis à jour: {csv_file}")
        print(f"🗃️ Base de données JSON mise à jour: {db_file}")

        # Afficher quelques statistiques
        print(f"\n📊 Statistiques:")
        print(f"   - Codes UNICO: {len(df)}")
        print(f"   - Catégories HAB: {len([x for x in df['UNICO'] if 'HAB' in str(x)])}")
        print(f"   - Catégories TAB: {len([x for x in df['UNICO'] if 'TAB' in str(x)])}")
        print(f"   - Catégories Brg: {len([x for x in df['UNICO'] if 'Brg' in str(x)])}")
        print(f"   - Total ordre: {df['Ordre'].sum():.0f}")

        return True

    except Exception as e:
        print(f"❌ Erreur lors de l'import: {e}")
        import traceback
        traceback.print_exc()
        return False

def import_excel_to_database(excel_file_path):
    """Fonction générique pour importer depuis Excel - détecte automatiquement le format"""

    if not os.path.exists(excel_file_path):
        print(f"❌ Erreur: Le fichier {excel_file_path} n'existe pas!")
        return False

    try:
        # Lire les premières lignes pour détecter le format
        df_sample = pd.read_excel(excel_file_path, nrows=5)
        columns = list(df_sample.columns)

        # Détecter le format Data twist
        if 'unicos' in columns and 'Mc' in columns and 'Group' in columns:
            print("🔍 Format détecté: Data twist (Production)")
            return import_twist_excel_to_database(excel_file_path)

        # Détecter le format Squib-Commande
        elif 'Unnamed: 1' in columns and 'Unnamed: 2' in columns:
            # Vérifier si c'est le format Squib-Commande en lisant quelques lignes
            df_check = pd.read_excel(excel_file_path, nrows=10)
            if any('UNICO' in str(val) for val in df_check.iloc[0] if pd.notna(val)):
                print("🔍 Format détecté: Squib-Commande")
                return import_squib_commande_excel_to_database(excel_file_path)

        # Format standard avec colonnes Code, Format, Name, etc.
        elif 'Code' in columns or 'Format' in columns:
            print("🔍 Format détecté: Standard barcode database")
            return import_standard_excel_to_database(excel_file_path)

        else:
            print(f"❌ Format Excel non reconnu. Colonnes trouvées: {columns}")
            print("💡 Formats supportés:")
            print("   - Data twist: unicos, Mc, Group, Ordre, Reste, Status, Scanned, Action")
            print("   - Squib-Commande: UNICO, APN-Cable, Ordre, Qt/Box, Commande/Box")
            print("   - Standard: Code, Format, Name, Category, Description, Price, Stock, etc.")
            return False

    except Exception as e:
        print(f"❌ Erreur lors de la détection du format: {e}")
        return False

def import_standard_excel_to_database(excel_file_path):
    """Importe les données d'un fichier Excel standard vers la base de données de codes-barres"""

    try:
        # Lire le fichier Excel
        print(f"📖 Lecture du fichier Excel standard: {excel_file_path}")
        df = pd.read_excel(excel_file_path)

        # Afficher les colonnes disponibles
        print(f"📋 Colonnes trouvées: {list(df.columns)}")
        print(f"📊 Nombre de lignes: {len(df)}")

        # Lire la base de données existante
        db_file = 'barcode-database.json'
        csv_file = 'barcode-data-simple.csv'

        # Charger la base de données JSON existante
        if os.path.exists(db_file):
            with open(db_file, 'r', encoding='utf-8') as f:
                database = json.load(f)
        else:
            database = {
                "aptivProducts": [],
                "standardProducts": [],
                "upcProducts": [],
                "internalCodes": [],
                "packagingCodes": [],
                "testData": [],
                "sampleScanHistory": [],
                "formatSpecs": {}
            }

        # Charger les données CSV existantes
        if os.path.exists(csv_file):
            existing_df = pd.read_csv(csv_file)
        else:
            existing_df = pd.DataFrame(columns=['Code', 'Format', 'Name', 'Category', 'Description', 'Price', 'Stock', 'MinStock', 'Supplier', 'Location', 'Status'])

        # Traiter chaque ligne du fichier Excel
        new_records = 0
        updated_records = 0

        for index, row in df.iterrows():
            # Extraire les données de base
            code = str(row.get('Code', '')).strip() if pd.notna(row.get('Code')) else ''
            if not code:
                continue

            format_type = str(row.get('Format', 'CODE128')).strip() if pd.notna(row.get('Format')) else 'CODE128'
            name = str(row.get('Name', '')).strip() if pd.notna(row.get('Name')) else ''
            category = str(row.get('Category', '')).strip() if pd.notna(row.get('Category')) else ''
            description = str(row.get('Description', '')).strip() if pd.notna(row.get('Description')) else ''

            # Données numériques avec gestion des valeurs manquantes
            price = float(row.get('Price', 0)) if pd.notna(row.get('Price')) and str(row.get('Price')).strip() != '' else None
            stock = int(float(row.get('Stock', 0))) if pd.notna(row.get('Stock')) and str(row.get('Stock')).strip() != '' else None
            min_stock = int(float(row.get('MinStock', 0))) if pd.notna(row.get('MinStock')) and str(row.get('MinStock')).strip() != '' else None

            supplier = str(row.get('Supplier', '')).strip() if pd.notna(row.get('Supplier')) else ''
            location = str(row.get('Location', '')).strip() if pd.notna(row.get('Location')) else ''
            status = str(row.get('Status', 'Active')).strip() if pd.notna(row.get('Status')) else 'Active'

            # Créer l'enregistrement pour CSV
            csv_record = {
                'Code': code,
                'Format': format_type,
                'Name': name,
                'Category': category,
                'Description': description,
                'Price': price if price is not None else '',
                'Stock': stock if stock is not None else '',
                'MinStock': min_stock if min_stock is not None else '',
                'Supplier': supplier,
                'Location': location,
                'Status': status
            }

            # Vérifier si le code existe déjà dans le CSV
            existing_record = existing_df[existing_df['Code'] == code]
            if not existing_record.empty:
                # Mettre à jour l'enregistrement existant
                existing_df.loc[existing_df['Code'] == code] = list(csv_record.values())
                updated_records += 1
            else:
                # Ajouter un nouveau enregistrement
                new_row = pd.DataFrame([csv_record])
                existing_df = pd.concat([existing_df, new_row], ignore_index=True)
                new_records += 1

            # Ajouter à la base de données JSON selon le type
            json_record = {
                "code": code,
                "format": format_type,
                "name": name,
                "category": category,
                "description": description,
                "lastUpdated": datetime.now().isoformat() + "Z"
            }

            # Ajouter les champs spécifiques selon le type de produit
            if price is not None:
                json_record["price"] = price
            if stock is not None:
                json_record["stock"] = stock
            if min_stock is not None:
                json_record["minStock"] = min_stock
            if supplier:
                json_record["supplier"] = supplier
            if location:
                json_record["location"] = location

            # Déterminer dans quelle section ajouter le produit
            if code.startswith('APTIV-'):
                # Produit Aptiv interne
                existing_aptiv = next((p for p in database["aptivProducts"] if p["code"] == code), None)
                if existing_aptiv:
                    database["aptivProducts"].remove(existing_aptiv)
                database["aptivProducts"].append(json_record)

            elif format_type == 'EAN13' and len(code) == 13:
                # Produit EAN13 standard
                json_record["brand"] = "Aptiv"
                json_record["country"] = "France"
                existing_standard = next((p for p in database["standardProducts"] if p["code"] == code), None)
                if existing_standard:
                    database["standardProducts"].remove(existing_standard)
                database["standardProducts"].append(json_record)

            elif format_type == 'UPC' and len(code) == 12:
                # Produit UPC
                json_record["brand"] = "Aptiv"
                json_record["region"] = "North America"
                existing_upc = next((p for p in database["upcProducts"] if p["code"] == code), None)
                if existing_upc:
                    database["upcProducts"].remove(existing_upc)
                database["upcProducts"].append(json_record)

            elif format_type == 'CODE39':
                # Code interne
                json_record["type"] = category if category else "Internal"
                existing_internal = next((p for p in database["internalCodes"] if p["code"] == code), None)
                if existing_internal:
                    database["internalCodes"].remove(existing_internal)
                database["internalCodes"].append(json_record)

            elif format_type == 'ITF14':
                # Code d'emballage
                json_record["type"] = "Packaging"
                existing_packaging = next((p for p in database["packagingCodes"] if p["code"] == code), None)
                if existing_packaging:
                    database["packagingCodes"].remove(existing_packaging)
                database["packagingCodes"].append(json_record)

            elif category == 'Test':
                # Données de test
                existing_test = next((p for p in database["testData"] if p["code"] == code), None)
                if existing_test:
                    database["testData"].remove(existing_test)
                database["testData"].append(json_record)

        # Sauvegarder le fichier CSV mis à jour
        existing_df.to_csv(csv_file, index=False, encoding='utf-8')

        # Sauvegarder la base de données JSON mise à jour
        with open(db_file, 'w', encoding='utf-8') as f:
            json.dump(database, f, indent=2, ensure_ascii=False)

        print(f"✅ Import terminé avec succès!")
        print(f"➕ {new_records} nouveaux enregistrements ajoutés")
        print(f"🔄 {updated_records} enregistrements mis à jour")
        print(f"📄 Fichier CSV mis à jour: {csv_file}")
        print(f"🗃️ Base de données JSON mise à jour: {db_file}")

        return True

    except Exception as e:
        print(f"❌ Erreur lors de l'import: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Script d'import de données de codes-barres Aptiv")
    print("=" * 50)

    # Liste des fichiers Excel à traiter
    excel_files = [
        "../../Data twist.xlsx",
        "../../Datat squib-Commande.xlsx"
    ]

    success_count = 0

    for excel_file in excel_files:
        print(f"\n📁 Vérification du fichier: {excel_file}")

        if os.path.exists(excel_file):
            print(f"✅ Fichier trouvé: {excel_file}")

            # Importer les données depuis Excel
            if import_excel_to_database(excel_file):
                print(f"✅ Import réussi pour: {excel_file}")
                success_count += 1
            else:
                print(f"❌ Échec de l'import pour: {excel_file}")
        else:
            print(f"❌ Fichier non trouvé: {excel_file}")

    print(f"\n📊 Résumé: {success_count}/{len(excel_files)} fichiers importés avec succès")

    if success_count == 0:
        print("📁 Fichiers Excel disponibles dans le répertoire parent:")
        parent_dir = "../.."
        if os.path.exists(parent_dir):
            for file in os.listdir(parent_dir):
                if file.endswith(('.xlsx', '.xls')):
                    print(f"   - {file}")

    print("\n🎯 Options disponibles:")
    print("1. import_excel_to_database('chemin/vers/fichier.xlsx') - Importer depuis Excel")
    print("2. import_squib_commande_excel_to_database('fichier.xlsx') - Importer Squib-Commande")
    print("3. import_twist_excel_to_database('fichier.xlsx') - Importer Data twist")
    print("4. create_excel_with_barcode_data() - Créer un fichier Excel depuis la DB")
    print("5. update_existing_excel('fichier.xlsx') - Mettre à jour un Excel existant")
