#!/usr/bin/env python3
"""
Script pour importer les données de codes-barres dans Excel
Aptiv - Twisting Monitoring Tool
"""

import pandas as pd
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
import os
from datetime import datetime

def create_excel_with_barcode_data():
    """Crée un fichier Excel avec toutes les données de codes-barres"""
    
    # Lire les données CSV
    csv_file = 'barcode-data-simple.csv'
    if not os.path.exists(csv_file):
        print(f"Erreur: Le fichier {csv_file} n'existe pas!")
        return
    
    df = pd.read_csv(csv_file)
    
    # Créer un nouveau classeur Excel
    wb = openpyxl.Workbook()
    
    # Supprimer la feuille par défaut
    wb.remove(wb.active)
    
    # Styles pour l'en-tête
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    # Style pour les bordures
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # === FEUILLE 1: TOUS LES PRODUITS ===
    ws_all = wb.create_sheet("Tous les Produits")
    
    # Ajouter les données
    for r in dataframe_to_rows(df, index=False, header=True):
        ws_all.append(r)
    
    # Formater l'en-tête
    for cell in ws_all[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = thin_border
    
    # Ajuster la largeur des colonnes
    column_widths = {
        'A': 25,  # Code
        'B': 12,  # Format
        'C': 35,  # Name
        'D': 15,  # Category
        'E': 50,  # Description
        'F': 10,  # Price
        'G': 8,   # Stock
        'H': 10,  # MinStock
        'I': 20,  # Supplier
        'J': 15,  # Location
        'K': 12   # Status
    }
    
    for col, width in column_widths.items():
        ws_all.column_dimensions[col].width = width
    
    # === FEUILLE 2: PRODUITS APTIV ===
    ws_aptiv = wb.create_sheet("Produits Aptiv")
    aptiv_products = df[df['Code'].str.startswith('APTIV-') & (df['Category'] != 'Test')].copy()
    
    for r in dataframe_to_rows(aptiv_products, index=False, header=True):
        ws_aptiv.append(r)
    
    # Formater l'en-tête
    for cell in ws_aptiv[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = thin_border
    
    for col, width in column_widths.items():
        ws_aptiv.column_dimensions[col].width = width
    
    # === FEUILLE 3: PRODUITS STANDARDS (EAN/UPC) ===
    ws_standard = wb.create_sheet("Produits Standards")
    standard_products = df[df['Format'].isin(['EAN13', 'EAN8', 'UPC']) & (df['Category'] != 'Test')].copy()
    
    for r in dataframe_to_rows(standard_products, index=False, header=True):
        ws_standard.append(r)
    
    # Formater l'en-tête
    for cell in ws_standard[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = thin_border
    
    for col, width in column_widths.items():
        ws_standard.column_dimensions[col].width = width
    
    # === FEUILLE 4: CODES INTERNES ===
    ws_internal = wb.create_sheet("Codes Internes")
    internal_codes = df[df['Format'] == 'CODE39'].copy()
    
    for r in dataframe_to_rows(internal_codes, index=False, header=True):
        ws_internal.append(r)
    
    # Formater l'en-tête
    for cell in ws_internal[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = thin_border
    
    for col, width in column_widths.items():
        ws_internal.column_dimensions[col].width = width
    
    # === FEUILLE 5: EMBALLAGES ===
    ws_packaging = wb.create_sheet("Emballages")
    packaging_codes = df[df['Format'] == 'ITF14'].copy()
    
    for r in dataframe_to_rows(packaging_codes, index=False, header=True):
        ws_packaging.append(r)
    
    # Formater l'en-tête
    for cell in ws_packaging[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = thin_border
    
    for col, width in column_widths.items():
        ws_packaging.column_dimensions[col].width = width
    
    # === FEUILLE 6: STOCK FAIBLE ===
    ws_low_stock = wb.create_sheet("Stock Faible")
    
    # Filtrer les produits avec stock faible
    low_stock_products = df[
        (df['Stock'].notna()) & 
        (df['MinStock'].notna()) & 
        (df['Stock'].astype(float) <= df['MinStock'].astype(float))
    ].copy()
    
    for r in dataframe_to_rows(low_stock_products, index=False, header=True):
        ws_low_stock.append(r)
    
    # Formater l'en-tête avec couleur d'alerte
    alert_fill = PatternFill(start_color="FF6B6B", end_color="FF6B6B", fill_type="solid")
    for cell in ws_low_stock[1]:
        cell.font = header_font
        cell.fill = alert_fill
        cell.alignment = header_alignment
        cell.border = thin_border
    
    for col, width in column_widths.items():
        ws_low_stock.column_dimensions[col].width = width
    
    # === FEUILLE 7: STATISTIQUES ===
    ws_stats = wb.create_sheet("Statistiques")
    
    # Créer des statistiques
    stats_data = [
        ["Statistiques des Codes-Barres Aptiv", ""],
        ["", ""],
        ["Total des codes-barres", len(df)],
        ["Produits Aptiv", len(aptiv_products)],
        ["Produits Standards", len(standard_products)],
        ["Codes internes", len(internal_codes)],
        ["Codes d'emballage", len(packaging_codes)],
        ["Produits en stock faible", len(low_stock_products)],
        ["", ""],
        ["Par Format:", ""],
        ["CODE128", len(df[df['Format'] == 'CODE128'])],
        ["CODE39", len(df[df['Format'] == 'CODE39'])],
        ["EAN13", len(df[df['Format'] == 'EAN13'])],
        ["EAN8", len(df[df['Format'] == 'EAN8'])],
        ["UPC", len(df[df['Format'] == 'UPC'])],
        ["ITF14", len(df[df['Format'] == 'ITF14'])],
        ["", ""],
        ["Par Catégorie:", ""],
    ]
    
    # Ajouter les statistiques par catégorie
    categories = df['Category'].value_counts()
    for category, count in categories.items():
        if pd.notna(category):
            stats_data.append([category, count])
    
    # Ajouter les données de statistiques
    for row in stats_data:
        ws_stats.append(row)
    
    # Formater la feuille de statistiques
    ws_stats['A1'].font = Font(bold=True, size=16, color="366092")
    ws_stats.column_dimensions['A'].width = 30
    ws_stats.column_dimensions['B'].width = 15
    
    # === FEUILLE 8: FORMATS DE CODES-BARRES ===
    ws_formats = wb.create_sheet("Formats")
    
    format_info = [
        ["Format", "Nom", "Description", "Charset", "Longueur", "Usage"],
        ["CODE128", "Code 128", "Alphanumeric, haute densité", "ASCII complet", "Variable (1-50)", "Produits internes, traçabilité"],
        ["CODE39", "Code 39", "Alphanumeric, largement utilisé", "A-Z, 0-9, -. $/+%", "Variable (1-43)", "Identification interne, maintenance"],
        ["EAN13", "EAN-13", "13 chiffres, produits de consommation", "0-9 uniquement", "13", "Produits commerciaux, retail"],
        ["EAN8", "EAN-8", "8 chiffres, petits produits", "0-9 uniquement", "8", "Petits produits, espace limité"],
        ["UPC", "UPC-A", "12 chiffres, États-Unis/Canada", "0-9 uniquement", "12", "Marché nord-américain"],
        ["ITF14", "ITF-14", "14 chiffres, emballages", "0-9 uniquement", "14", "Caisses, palettes, logistique"]
    ]
    
    for row in format_info:
        ws_formats.append(row)
    
    # Formater l'en-tête des formats
    for cell in ws_formats[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = thin_border
    
    # Ajuster les largeurs
    format_widths = {'A': 12, 'B': 15, 'C': 35, 'D': 25, 'E': 15, 'F': 30}
    for col, width in format_widths.items():
        ws_formats.column_dimensions[col].width = width
    
    # Sauvegarder le fichier
    output_file = f"../Nouveau dossier (8)/Data_twist_updated_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    wb.save(output_file)
    
    print(f"✅ Fichier Excel créé avec succès: {output_file}")
    print(f"📊 {len(df)} codes-barres ajoutés")
    print(f"📦 {len(aptiv_products)} produits Aptiv")
    print(f"🏷️ {len(internal_codes)} codes internes")
    print(f"📋 {len(low_stock_products)} produits en stock faible")

def update_existing_excel(excel_file):
    """Met à jour un fichier Excel existant avec les nouvelles données"""
    
    if not os.path.exists(excel_file):
        print(f"Erreur: Le fichier {excel_file} n'existe pas!")
        return
    
    # Lire les nouvelles données
    csv_file = 'barcode-data-simple.csv'
    if not os.path.exists(csv_file):
        print(f"Erreur: Le fichier {csv_file} n'existe pas!")
        return
    
    df_new = pd.read_csv(csv_file)
    
    # Lire le fichier Excel existant
    try:
        df_existing = pd.read_excel(excel_file)
        
        # Combiner les données (éviter les doublons basés sur le code)
        df_combined = pd.concat([df_existing, df_new]).drop_duplicates(subset=['Code'], keep='last')
        
        # Sauvegarder le fichier mis à jour
        output_file = f"../Nouveau dossier (8)/Data_twist_merged_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        df_combined.to_excel(output_file, index=False)
        
        print(f"✅ Fichier Excel mis à jour: {output_file}")
        print(f"📊 {len(df_combined)} codes-barres au total")
        print(f"➕ {len(df_new)} nouveaux codes ajoutés")
        
    except Exception as e:
        print(f"Erreur lors de la lecture du fichier Excel: {e}")
        print("Création d'un nouveau fichier à la place...")
        create_excel_with_barcode_data()

if __name__ == "__main__":
    print("🔧 Script d'import de données de codes-barres Aptiv")
    print("=" * 50)
    
    # Vérifier si le répertoire de destination existe
    dest_dir = "../Nouveau dossier (8)"
    if not os.path.exists(dest_dir):
        os.makedirs(dest_dir)
        print(f"📁 Répertoire créé: {dest_dir}")
    
    # Créer un nouveau fichier Excel avec toutes les données
    create_excel_with_barcode_data()
    
    print("\n🎯 Options disponibles:")
    print("1. Le fichier Excel a été créé avec 8 feuilles:")
    print("   - Tous les Produits")
    print("   - Produits Aptiv")
    print("   - Produits Standards")
    print("   - Codes Internes")
    print("   - Emballages")
    print("   - Stock Faible")
    print("   - Statistiques")
    print("   - Formats")
    print("\n2. Pour mettre à jour un fichier existant, utilisez:")
    print("   update_existing_excel('chemin/vers/fichier.xlsx')")
