// Barcode Generator and Scanner
class BarcodeManager {
    constructor() {
        this.currentFormat = 'CODE128';
        this.scanner = null;
        this.isScanning = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.createFormatButtons();
        this.setupCamera();
    }

    setupEventListeners() {
        const generateButton = document.getElementById('generateButton');
        const barcodeInput = document.getElementById('barcodeInput');
        const downloadButton = document.getElementById('downloadButton');
        const startScanButton = document.getElementById('startScanButton');
        const stopScanButton = document.getElementById('stopScanButton');

        generateButton?.addEventListener('click', () => this.generateBarcode());
        barcodeInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.generateBarcode();
        });
        downloadButton?.addEventListener('click', () => this.downloadBarcode());
        startScanButton?.addEventListener('click', () => this.startScanning());
        stopScanButton?.addEventListener('click', () => this.stopScanning());

        // Auto-generate on input change
        barcodeInput?.addEventListener('input', () => {
            if (barcodeInput.value.trim()) {
                this.generateBarcode();
            }
        });
    }

    createFormatButtons() {
        const buttonGroup = document.querySelector('.button-group');
        if (!buttonGroup) return;

        const formats = [
            { code: 'CODE128', name: 'Code 128' },
            { code: 'CODE39', name: 'Code 39' },
            { code: 'EAN13', name: 'EAN-13' },
            { code: 'EAN8', name: 'EAN-8' },
            { code: 'UPC', name: 'UPC-A' },
            { code: 'ITF14', name: 'ITF-14' }
        ];

        formats.forEach(format => {
            const button = document.createElement('button');
            button.className = `format-button ${format.code === this.currentFormat ? 'active' : ''}`;
            button.textContent = format.name;
            button.addEventListener('click', () => this.setFormat(format.code, button));
            buttonGroup.appendChild(button);
        });
    }

    setFormat(format, button) {
        this.currentFormat = format;

        // Update active button
        document.querySelectorAll('.format-button').forEach(btn => {
            btn.classList.remove('active');
        });
        button.classList.add('active');

        // Regenerate barcode if input has value
        const input = document.getElementById('barcodeInput');
        if (input?.value.trim()) {
            this.generateBarcode();
        }
    }

    generateBarcode() {
        const input = document.getElementById('barcodeInput');
        const barcodeElement = document.getElementById('barcode');
        const errorElement = document.querySelector('.error-message');
        const infoElement = document.querySelector('.barcode-info');
        const downloadButton = document.getElementById('downloadButton');

        if (!input || !barcodeElement) return;

        const text = input.value.trim();
        if (!text) {
            this.showError('Veuillez entrer un texte ou un numéro');
            return;
        }

        try {
            // Clear previous content
            barcodeElement.innerHTML = '';
            this.hideError();

            // Validate input based on format
            if (!this.validateInput(text, this.currentFormat)) {
                this.showError(`Format invalide pour ${this.currentFormat}`);
                return;
            }

            // Generate barcode
            JsBarcode(barcodeElement, text, {
                format: this.currentFormat,
                width: 2,
                height: 100,
                displayValue: true,
                fontSize: 14,
                margin: 10,
                background: '#ffffff',
                lineColor: '#000000'
            });

            // Show barcode info
            this.showBarcodeInfo(text, this.currentFormat);

            // Show download button
            if (downloadButton) {
                downloadButton.style.display = 'inline-block';
            }

        } catch (error) {
            this.showError('Erreur lors de la génération du code-barres: ' + error.message);
        }
    }

    validateInput(text, format) {
        switch (format) {
            case 'EAN13':
                return /^\d{12,13}$/.test(text);
            case 'EAN8':
                return /^\d{7,8}$/.test(text);
            case 'UPC':
                return /^\d{11,12}$/.test(text);
            case 'CODE39':
                return /^[A-Z0-9\-. $/+%]*$/.test(text);
            case 'ITF14':
                return /^\d{13,14}$/.test(text);
            case 'CODE128':
            default:
                return text.length > 0;
        }
    }

    showBarcodeInfo(text, format) {
        const infoElement = document.querySelector('.barcode-info');
        if (!infoElement) return;

        infoElement.innerHTML = `
            <h3>Informations du Code-Barres</h3>
            <p><strong>Texte:</strong> ${text}</p>
            <p><strong>Format:</strong> ${format}</p>
            <p><strong>Longueur:</strong> ${text.length} caractères</p>
            <p><strong>Généré le:</strong> ${new Date().toLocaleString('fr-FR')}</p>
        `;
        infoElement.style.display = 'block';
    }

    showError(message) {
        const errorElement = document.querySelector('.error-message');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
    }

    hideError() {
        const errorElement = document.querySelector('.error-message');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }

    downloadBarcode() {
        const barcodeElement = document.getElementById('barcode');
        if (!barcodeElement) return;

        try {
            // Create canvas from SVG
            const svg = barcodeElement.outerHTML;
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = function () {
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);

                // Download
                const link = document.createElement('a');
                link.download = `barcode_${Date.now()}.png`;
                link.href = canvas.toDataURL();
                link.click();
            };

            img.src = 'data:image/svg+xml;base64,' + btoa(svg);
        } catch (error) {
            this.showError('Erreur lors du téléchargement: ' + error.message);
        }
    }

    setupCamera() {
        // This is a placeholder for camera setup
        // In a real implementation, you would use libraries like QuaggaJS or ZXing
        console.log('Camera setup for barcode scanning');
    }

    async startScanning() {
        const startButton = document.getElementById('startScanButton');
        const stopButton = document.getElementById('stopScanButton');
        const cameraContainer = document.getElementById('cameraContainer');

        if (!startButton || !stopButton) return;

        try {
            startButton.style.display = 'none';
            stopButton.style.display = 'inline-block';
            this.isScanning = true;

            // Simulate camera access (replace with actual camera implementation)
            this.simulateScanning();

        } catch (error) {
            this.showError('Erreur d\'accès à la caméra: ' + error.message);
            this.stopScanning();
        }
    }

    stopScanning() {
        const startButton = document.getElementById('startScanButton');
        const stopButton = document.getElementById('stopScanButton');

        if (startButton) startButton.style.display = 'inline-block';
        if (stopButton) stopButton.style.display = 'none';

        this.isScanning = false;

        if (this.scanner) {
            this.scanner.stop();
            this.scanner = null;
        }
    }

    simulateScanning() {
        // This simulates barcode detection
        // Replace with actual barcode scanning library
        setTimeout(() => {
            if (this.isScanning) {
                const simulatedResult = '1234567890123';
                this.onBarcodeDetected(simulatedResult);
            }
        }, 3000);
    }

    onBarcodeDetected(result) {
        const scanResult = document.querySelector('.scan-result');
        const barcodeInput = document.getElementById('barcodeInput');

        if (scanResult) {
            scanResult.innerHTML = `
                <h3>Code-Barres Détecté!</h3>
                <p><strong>Résultat:</strong> ${result}</p>
                <p><strong>Détecté le:</strong> ${new Date().toLocaleString('fr-FR')}</p>
            `;
            scanResult.style.display = 'block';
        }

        if (barcodeInput) {
            barcodeInput.value = result;
            this.generateBarcode();
        }

        this.stopScanning();
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.barcodeManager = new BarcodeManager();
});

// Comprehensive barcode data for Aptiv system
const barcodeDatabase = {
    aptivProducts: [
        { code: "APTIV-CBL-HT-001", format: "CODE128", name: "Câble Haute Tension 16AWG", category: "Câbles", stock: 2450 },
        { code: "APTIV-CON-IP67-004", format: "CODE128", name: "Connecteur Étanche IP67", category: "Connecteurs", stock: 1250 },
        { code: "APTIV-FCX-ECU-007", format: "CODE128", name: "Faisceau de Câbles ECU", category: "Faisceaux", stock: 875 },
        { code: "APTIV-SEN-TEMP-012", format: "CODE128", name: "Capteur de Température", category: "Capteurs", stock: 650 },
        { code: "APTIV-REL-PWR-008", format: "CODE128", name: "Relais de Puissance 40A", category: "Relais", stock: 320 },
        { code: "APTIV-FUS-MINI-025", format: "CODE128", name: "Fusible Mini 25A", category: "Fusibles", stock: 5000 },
        { code: "APTIV-LED-IND-003", format: "CODE128", name: "LED Indicateur Rouge", category: "Indicateurs", stock: 2800 },
        { code: "APTIV-SW-PUSH-019", format: "CODE128", name: "Bouton Poussoir Étanche", category: "Commutateurs", stock: 450 }
    ],
    standardProducts: [
        { code: "3760123456789", format: "EAN13", name: "Kit de Câblage Automobile Premium", price: 89.99 },
        { code: "3760234567890", format: "EAN13", name: "Connecteur Multi-Pin 24 Voies", price: 34.50 },
        { code: "3760345678901", format: "EAN13", name: "Capteur de Position Intelligent", price: 67.80 },
        { code: "3760456789012", format: "EAN13", name: "Module de Contrôle ECU", price: 245.00 },
        { code: "3760567890123", format: "EAN13", name: "Faisceau de Câbles Hybride", price: 156.75 }
    ],
    internalCodes: [
        { code: "LOT-2024-001", format: "CODE39", type: "Lot Number", description: "Lot de production Janvier 2024" },
        { code: "QC-PASS-2024-A", format: "CODE39", type: "Quality Control", description: "Contrôle qualité réussi - Série A" },
        { code: "SHIP-FR-001", format: "CODE39", type: "Shipping", description: "Expédition France - Lot 001" },
        { code: "MAINT-EQ-012", format: "CODE39", type: "Maintenance", description: "Équipement de maintenance 012" },
        { code: "RMA-2024-0001", format: "CODE39", type: "Return", description: "Retour client - Défaut mineur" }
    ],
    testData: [
        { code: "TEST-BASIC-001", format: "CODE128", description: "Code de test basique" },
        { code: "1234567890123", format: "EAN13", description: "EAN-13 de test standard" },
        { code: "12345678", format: "EAN8", description: "EAN-8 de test compact" },
        { code: "HELLO WORLD", format: "CODE39", description: "Texte simple Code 39" },
        { code: "123456789012", format: "UPC", description: "UPC-A de test" },
        { code: "APTIV-TEST-999", format: "CODE128", description: "Code de test Aptiv spécifique" }
    ]
};

// Get all sample barcodes
function getAllSampleBarcodes() {
    const allBarcodes = [];
    Object.values(barcodeDatabase).forEach(category => {
        if (Array.isArray(category)) {
            allBarcodes.push(...category);
        }
    });
    return allBarcodes;
}

// Load sample data with product information
function loadSampleData() {
    const input = document.getElementById('barcodeInput');
    if (input) {
        const allSamples = getAllSampleBarcodes();
        const randomSample = allSamples[Math.floor(Math.random() * allSamples.length)];

        input.value = randomSample.code;

        // Set the appropriate format
        const manager = window.barcodeManager;
        if (manager && randomSample.format) {
            manager.currentFormat = randomSample.format;

            // Update format button
            document.querySelectorAll('.format-button').forEach(btn => {
                btn.classList.remove('active');
                if (btn.textContent.includes(getFormatDisplayName(randomSample.format))) {
                    btn.classList.add('active');
                }
            });
        }

        // Trigger generation
        const event = new Event('input');
        input.dispatchEvent(event);

        // Show product info if available
        showProductInfo(randomSample);
    }
}

// Get format display name
function getFormatDisplayName(format) {
    const formatNames = {
        'CODE128': 'Code 128',
        'CODE39': 'Code 39',
        'EAN13': 'EAN-13',
        'EAN8': 'EAN-8',
        'UPC': 'UPC-A',
        'ITF14': 'ITF-14'
    };
    return formatNames[format] || format;
}

// Show product information
function showProductInfo(product) {
    const infoElement = document.querySelector('.barcode-info');
    if (!infoElement || !product) return;

    let infoHTML = `
        <h3>Informations du Produit</h3>
        <p><strong>Code:</strong> ${product.code}</p>
        <p><strong>Format:</strong> ${product.format}</p>
    `;

    if (product.name) {
        infoHTML += `<p><strong>Nom:</strong> ${product.name}</p>`;
    }
    if (product.category) {
        infoHTML += `<p><strong>Catégorie:</strong> ${product.category}</p>`;
    }
    if (product.price) {
        infoHTML += `<p><strong>Prix:</strong> ${product.price}€</p>`;
    }
    if (product.stock) {
        infoHTML += `<p><strong>Stock:</strong> ${product.stock} unités</p>`;
    }
    if (product.description) {
        infoHTML += `<p><strong>Description:</strong> ${product.description}</p>`;
    }
    if (product.type) {
        infoHTML += `<p><strong>Type:</strong> ${product.type}</p>`;
    }

    infoHTML += `<p><strong>Généré le:</strong> ${new Date().toLocaleString('fr-FR')}</p>`;

    infoElement.innerHTML = infoHTML;
    infoElement.style.display = 'block';
}

// Search product by barcode
function searchProductByBarcode(code) {
    const allProducts = getAllSampleBarcodes();
    return allProducts.find(product => product.code === code);
}

// Load specific category data
function loadCategoryData(category) {
    const input = document.getElementById('barcodeInput');
    if (input && barcodeDatabase[category]) {
        const categoryData = barcodeDatabase[category];
        const randomItem = categoryData[Math.floor(Math.random() * categoryData.length)];

        input.value = randomItem.code;

        // Set format and trigger generation
        const manager = window.barcodeManager;
        if (manager && randomItem.format) {
            manager.currentFormat = randomItem.format;
        }

        const event = new Event('input');
        input.dispatchEvent(event);

        showProductInfo(randomItem);
    }
}
