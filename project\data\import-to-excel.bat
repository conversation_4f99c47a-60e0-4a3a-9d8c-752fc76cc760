@echo off
echo ========================================
echo  Import de donnees codes-barres Aptiv
echo ========================================
echo.

REM Verifier si Python est installe
python --version >nul 2>&1
if errorlevel 1 (
    echo ERREUR: Python n'est pas installe ou pas dans le PATH
    echo Veuillez installer Python depuis https://python.org
    pause
    exit /b 1
)

echo Python detecte. Installation des dependances...
echo.

REM Installer les dependances necessaires
pip install pandas openpyxl

if errorlevel 1 (
    echo ERREUR: Impossible d'installer les dependances
    pause
    exit /b 1
)

echo.
echo Execution du script d'import...
echo.

REM Executer le script Python
python excel-import-script.py

if errorlevel 1 (
    echo ERREUR: Echec de l'execution du script
    pause
    exit /b 1
)

echo.
echo ========================================
echo  Import termine avec succes!
echo ========================================
echo.
echo Le fichier Excel a ete cree dans le dossier parent.
echo Vous pouvez maintenant ouvrir le fichier Excel.
echo.
pause
