{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,QAAQ,CAAA;AACvB,OAAO,EAAE,MAAM,IAAI,CAAA;AACnB,OAAO,EAAE,QAAQ,EAAE,MAAM,UAAU,CAAA;AAEnC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAA;AAExB,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;AACvC,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;AAC/B,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;AAC/B,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAA;AACzB,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,CAAA;AACrC,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;AAC/B,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;AAC/B,MAAM,YAAY,GAAG,MAAM,CAAC,cAAc,CAAC,CAAA;AAC3C,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;AACnC,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,CAAA;AACvC,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;AACnC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;AACnC,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;AAC3B,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;AAC/B,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,CAAA;AACrC,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;AACnC,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;AACjC,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;AAC7B,MAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;AAC/B,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;AACnC,MAAM,YAAY,GAAG,MAAM,CAAC,cAAc,CAAC,CAAA;AAC3C,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,CAAA;AAcnC,MAAM,OAAO,UAAW,SAAQ,QAI/B;IACC,CAAC,QAAQ,CAAC,GAAY,KAAK,CAAC;IAC5B,CAAC,GAAG,CAAC,CAAU;IACf,CAAC,KAAK,CAAC,CAAS;IAChB,CAAC,SAAS,CAAC,CAAS;IACpB,CAAC,QAAQ,CAAC,GAAY,KAAK,CAAC;IAC5B,CAAC,KAAK,CAAC,CAAS;IAChB,CAAC,OAAO,CAAC,CAAS;IAClB,CAAC,UAAU,CAAC,CAAS;IAErB,YAAY,IAAY,EAAE,GAAsB;QAC9C,GAAG,GAAG,GAAG,IAAI,EAAE,CAAA;QACf,KAAK,CAAC,GAAG,CAAC,CAAA;QAEV,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;QACpB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA;QAErB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,MAAM,IAAI,SAAS,CAAC,uBAAuB,CAAC,CAAA;QAC9C,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAA;QACtB,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAA;QAC3D,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAA;QAClB,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,QAAQ,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI,CAAA;QAClD,IAAI,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAA;QACtB,IAAI,CAAC,KAAK,CAAC,GAAG,OAAO,GAAG,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAA;QAChE,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAA;QAC3B,IAAI,CAAC,UAAU,CAAC;YACd,OAAO,GAAG,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAA;QAE3D,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC;YAClC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAA;QACf,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,KAAK,CAAC,EAAE,CAAA;QACf,CAAC;IACH,CAAC;IAED,IAAI,EAAE;QACJ,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;IAClB,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC,CAAA;IACpB,CAAC;IAED,YAAY;IACZ,KAAK;QACH,MAAM,IAAI,SAAS,CAAC,2BAA2B,CAAC,CAAA;IAClD,CAAC;IAED,YAAY;IACZ,GAAG;QACD,MAAM,IAAI,SAAS,CAAC,2BAA2B,CAAC,CAAA;IAClD,CAAC;IAED,CAAC,KAAK,CAAC;QACL,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAA;IAC9D,CAAC;IAED,CAAC,OAAO,CAAC,CAAC,EAAiC,EAAE,EAAW;QACtD,IAAI,EAAE,EAAE,CAAC;YACP,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAA;QACpB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAA;YACd,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAY,CAAC,CAAA;YAC/B,IAAI,CAAC,KAAK,CAAC,EAAE,CAAA;QACf,CAAC;IACH,CAAC;IAED,CAAC,QAAQ,CAAC;QACR,OAAO,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IACrE,CAAC;IAED,CAAC,KAAK,CAAC;QACL,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpB,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAA;YACrB,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAA;YAC5B,qBAAqB;YACrB,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACrB,OAAO,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;YAC5D,CAAC;YACD,oBAAoB;YACpB,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAW,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CACnE,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CACzB,CAAA;QACH,CAAC;IACH,CAAC;IAED,CAAC,OAAO,CAAC,CAAC,EAAiC,EAAE,EAAW,EAAE,GAAY;QACpE,IAAI,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAA;QACtB,IAAI,EAAE,EAAE,CAAC;YACP,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAA;QACpB,CAAC;aAAM,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,EAAY,EAAE,GAAa,CAAC,EAAE,CAAC;YAC3D,IAAI,CAAC,KAAK,CAAC,EAAE,CAAA;QACf,CAAC;IACH,CAAC;IAED,CAAC,MAAM,CAAC;QACN,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC;YACtD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;YACpB,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAA;YACrB,EAAE,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAChB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CACjD,CAAA;QACH,CAAC;IACH,CAAC;IAED,CAAC,QAAQ,CAAC,CAAC,EAAyB;QAClC,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAA;QACrB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;QACd,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;IACxB,CAAC;IAED,CAAC,YAAY,CAAC,CAAC,EAAU,EAAE,GAAW;QACpC,IAAI,GAAG,GAAG,KAAK,CAAA;QACf,wBAAwB;QACxB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAA;QACnB,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC;YACX,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;QAChE,CAAC;QAED,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACnC,GAAG,GAAG,KAAK,CAAA;YACX,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;YACd,KAAK,CAAC,GAAG,EAAE,CAAA;QACb,CAAC;QAED,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,IAAI,CACF,EAAS,EACT,GAAG,IAA6B;QAEhC,QAAQ,EAAE,EAAE,CAAC;YACX,KAAK,WAAW,CAAC;YACjB,KAAK,QAAQ;gBACX,OAAO,KAAK,CAAA;YAEd,KAAK,OAAO;gBACV,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC;oBAClC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAA;gBACf,CAAC;gBACD,OAAO,KAAK,CAAA;YAEd,KAAK,OAAO;gBACV,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACnB,OAAO,KAAK,CAAA;gBACd,CAAC;gBACD,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAA;gBACrB,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,CAAA;YAEhC;gBACE,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,CAAA;QAClC,CAAC;IACH,CAAC;CACF;AAED,MAAM,OAAO,cAAe,SAAQ,UAAU;IAC5C,CAAC,KAAK,CAAC;QACL,IAAI,KAAK,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC;YACH,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,CAAA;YAClD,KAAK,GAAG,KAAK,CAAA;QACf,CAAC;gBAAS,CAAC;YACT,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;YAChB,CAAC;QACH,CAAC;IACH,CAAC;IAED,CAAC,KAAK,CAAC;QACL,IAAI,KAAK,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACpB,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAA;gBACrB,GAAG,CAAC;oBACF,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAA;oBAC5B,qBAAqB;oBACrB,MAAM,EAAE,GACN,GAAG,CAAC,MAAM,KAAK,CAAC;wBACd,CAAC,CAAC,CAAC;wBACH,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAW,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;oBAChE,oBAAoB;oBACpB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC;wBACjC,MAAK;oBACP,CAAC;gBACH,CAAC,QAAQ,IAAI,EAAC;gBACd,IAAI,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAA;YACxB,CAAC;YACD,KAAK,GAAG,KAAK,CAAA;QACf,CAAC;gBAAS,CAAC;YACT,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;YAChB,CAAC;QACH,CAAC;IACH,CAAC;IAED,CAAC,MAAM,CAAC;QACN,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC;YACtD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;YACpB,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAA;YACrB,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;YAChB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QACpB,CAAC;IACH,CAAC;CACF;AAWD,MAAM,OAAO,WAAY,SAAQ,EAAE;IACjC,QAAQ,GAAU,KAAK,CAAA;IACvB,QAAQ,GAAY,IAAI,CAAC;IACzB,CAAC,QAAQ,CAAC,GAAY,KAAK,CAAC;IAC5B,CAAC,QAAQ,CAAC,GAAY,KAAK,CAAC;IAC5B,CAAC,MAAM,CAAC,GAAY,KAAK,CAAC;IAC1B,CAAC,MAAM,CAAC,GAAa,EAAE,CAAC;IACxB,CAAC,UAAU,CAAC,GAAY,KAAK,CAAC;IAC9B,CAAC,KAAK,CAAC,CAAS;IAChB,CAAC,KAAK,CAAC,CAAS;IAChB,CAAC,UAAU,CAAC,CAAU;IACtB,CAAC,GAAG,CAAC,CAAU;IACf,CAAC,YAAY,CAAC,CAAU;IACxB,CAAC,MAAM,CAAC,CAAS;IACjB,CAAC,SAAS,CAAC,GAAY,KAAK,CAAC;IAC7B,CAAC,IAAI,CAAC,CAAS;IAEf,YAAY,IAAY,EAAE,GAAuB;QAC/C,GAAG,GAAG,GAAG,IAAI,EAAE,CAAA;QACf,KAAK,CAAC,GAAG,CAAC,CAAA;QACV,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAA;QAClB,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAA;QAC3D,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAA;QACvD,IAAI,CAAC,IAAI,CAAC,GAAG,OAAO,GAAG,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAA;QAClE,IAAI,CAAC,UAAU,CAAC;YACd,OAAO,GAAG,CAAC,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAA;QAE3D,yDAAyD;QACzD,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAA;QACzD,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,KAAK,KAAK,SAAS,CAAA;QAC5C,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAA;QAEhE,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE,CAAC;YAC5B,IAAI,CAAC,KAAK,CAAC,EAAE,CAAA;QACf,CAAC;IACH,CAAC;IAED,IAAI,CAAC,EAAU,EAAE,GAAG,IAAW;QAC7B,IAAI,EAAE,KAAK,OAAO,EAAE,CAAC;YACnB,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACnB,OAAO,KAAK,CAAA;YACd,CAAC;YACD,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAA;QACvB,CAAC;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,CAAA;IAChC,CAAC;IAED,IAAI,EAAE;QACJ,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;IAClB,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC,CAAA;IACpB,CAAC;IAED,CAAC,QAAQ,CAAC,CAAC,EAAyB;QAClC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;QACd,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAA;QACrB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;IACxB,CAAC;IAED,CAAC,KAAK,CAAC;QACL,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CACzD,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CACtB,CAAA;IACH,CAAC;IAED,CAAC,OAAO,CAAC,CAAC,EAAiC,EAAE,EAAW;QACtD,IACE,IAAI,CAAC,YAAY,CAAC;YAClB,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI;YACrB,EAAE;YACF,EAAE,CAAC,IAAI,KAAK,QAAQ,EACpB,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA;YAClB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAA;QACf,CAAC;aAAM,IAAI,EAAE,EAAE,CAAC;YACd,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAA;QACpB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAA;YACd,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;YACrB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACpB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;YAChB,CAAC;QACH,CAAC;IACH,CAAC;IAID,GAAG,CAAC,GAAqB,EAAE,GAAoB;QAC7C,IAAI,GAAG,EAAE,CAAC;YACR,YAAY;YACZ,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;QACtB,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAA;QAEnB,uDAAuD;QACvD,IACE,CAAC,IAAI,CAAC,QAAQ,CAAC;YACf,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM;YACpB,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,EAC7B,CAAC;YACD,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;QACzB,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAID,KAAK,CAAC,GAAoB,EAAE,GAAoB;QAC9C,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC5B,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;QAC7B,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAA;YACpD,OAAO,KAAK,CAAA;QACd,CAAC;QAED,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC;YACrE,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACtB,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAA;YACvB,OAAO,KAAK,CAAA;QACd,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAA;QACrB,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAA;QACjB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,CAAC,MAAM,CAAC,CAAC,GAAW;QAClB,EAAE,CAAC,KAAK,CACN,IAAI,CAAC,GAAG,CAAW,EACnB,GAAG,EACH,CAAC,EACD,GAAG,CAAC,MAAM,EACV,IAAI,CAAC,IAAI,CAAC,EACV,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CACnC,CAAA;IACH,CAAC;IAED,CAAC,QAAQ,CAAC,CAAC,EAAiC,EAAE,EAAW;QACvD,IAAI,EAAE,EAAE,CAAC;YACP,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAA;QACpB,CAAC;aAAM,CAAC;YACN,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,SAAS,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE,CAAC;gBACvD,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAA;YAClB,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC;gBACxB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;YAChB,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAA;gBAEtB,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;oBACrC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAA;oBACtB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;oBACd,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBACrB,CAAC;qBAAM,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC5B,IAAI,CAAC,UAAU,CAAC,GAAG,KAAK,CAAA;oBACxB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBACpB,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,CAAC,MAAM,CAAC;QACN,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBACjB,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;YACzB,CAAC;QACH,CAAC;aAAM,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAY,CAAC,CAAA;QAC5C,CAAC;aAAM,CAAC;YACN,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAA;YAC1B,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAA;YACjB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAW,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,CAAW,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAClE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CACvB,CAAA;QACH,CAAC;IACH,CAAC;IAED,CAAC,MAAM,CAAC;QACN,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC;YACtD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;YACpB,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAA;YACrB,EAAE,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAChB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CACjD,CAAA;QACH,CAAC;IACH,CAAC;CACF;AAED,MAAM,OAAO,eAAgB,SAAQ,WAAW;IAC9C,CAAC,KAAK,CAAC;QACL,IAAI,EAAE,CAAA;QACN,8DAA8D;QAC9D,gEAAgE;QAChE,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC;YAChD,IAAI,CAAC;gBACH,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;YAC1D,CAAC;YAAC,OAAO,EAAE,EAAE,CAAC;gBACZ,IAAK,EAA4B,EAAE,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACrD,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,CAAA;oBAClB,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,CAAA;gBACtB,CAAC;qBAAM,CAAC;oBACN,MAAM,EAAE,CAAA;gBACV,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;QAC1D,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;IACzB,CAAC;IAED,CAAC,MAAM,CAAC;QACN,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC;YACtD,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;YACpB,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAA;YACrB,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;YAChB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QACpB,CAAC;IACH,CAAC;IAED,CAAC,MAAM,CAAC,CAAC,GAAW;QAClB,mDAAmD;QACnD,IAAI,KAAK,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC;YACH,IAAI,CAAC,QAAQ,CAAC,CACZ,IAAI,EACJ,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAW,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAClE,CAAA;YACD,KAAK,GAAG,KAAK,CAAA;QACf,CAAC;gBAAS,CAAC;YACT,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,CAAC;oBACH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAA;gBAChB,CAAC;gBAAC,MAAM,CAAC;oBACP,WAAW;gBACb,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;CACF", "sourcesContent": ["import EE from 'events'\nimport fs from 'fs'\nimport { Minipass } from 'minipass'\n\nconst writev = fs.writev\n\nconst _autoClose = Symbol('_autoClose')\nconst _close = Symbol('_close')\nconst _ended = Symbol('_ended')\nconst _fd = Symbol('_fd')\nconst _finished = Symbol('_finished')\nconst _flags = Symbol('_flags')\nconst _flush = Symbol('_flush')\nconst _handleChunk = Symbol('_handleChunk')\nconst _makeBuf = Symbol('_makeBuf')\nconst _mode = Symbol('_mode')\nconst _needDrain = Symbol('_needDrain')\nconst _onerror = Symbol('_onerror')\nconst _onopen = Symbol('_onopen')\nconst _onread = Symbol('_onread')\nconst _onwrite = Symbol('_onwrite')\nconst _open = Symbol('_open')\nconst _path = Symbol('_path')\nconst _pos = Symbol('_pos')\nconst _queue = Symbol('_queue')\nconst _read = Symbol('_read')\nconst _readSize = Symbol('_readSize')\nconst _reading = Symbol('_reading')\nconst _remain = Symbol('_remain')\nconst _size = Symbol('_size')\nconst _write = Symbol('_write')\nconst _writing = Symbol('_writing')\nconst _defaultFlag = Symbol('_defaultFlag')\nconst _errored = Symbol('_errored')\n\nexport type ReadStreamOptions =\n  Minipass.Options<Minipass.ContiguousData> & {\n    fd?: number\n    readSize?: number\n    size?: number\n    autoClose?: boolean\n  }\n\nexport type ReadStreamEvents = Minipass.Events<Minipass.ContiguousData> & {\n  open: [fd: number]\n}\n\nexport class ReadStream extends Minipass<\n  Minipass.ContiguousData,\n  Buffer,\n  ReadStreamEvents\n> {\n  [_errored]: boolean = false;\n  [_fd]?: number;\n  [_path]: string;\n  [_readSize]: number;\n  [_reading]: boolean = false;\n  [_size]: number;\n  [_remain]: number;\n  [_autoClose]: boolean\n\n  constructor(path: string, opt: ReadStreamOptions) {\n    opt = opt || {}\n    super(opt)\n\n    this.readable = true\n    this.writable = false\n\n    if (typeof path !== 'string') {\n      throw new TypeError('path must be a string')\n    }\n\n    this[_errored] = false\n    this[_fd] = typeof opt.fd === 'number' ? opt.fd : undefined\n    this[_path] = path\n    this[_readSize] = opt.readSize || 16 * 1024 * 1024\n    this[_reading] = false\n    this[_size] = typeof opt.size === 'number' ? opt.size : Infinity\n    this[_remain] = this[_size]\n    this[_autoClose] =\n      typeof opt.autoClose === 'boolean' ? opt.autoClose : true\n\n    if (typeof this[_fd] === 'number') {\n      this[_read]()\n    } else {\n      this[_open]()\n    }\n  }\n\n  get fd() {\n    return this[_fd]\n  }\n\n  get path() {\n    return this[_path]\n  }\n\n  //@ts-ignore\n  write() {\n    throw new TypeError('this is a readable stream')\n  }\n\n  //@ts-ignore\n  end() {\n    throw new TypeError('this is a readable stream')\n  }\n\n  [_open]() {\n    fs.open(this[_path], 'r', (er, fd) => this[_onopen](er, fd))\n  }\n\n  [_onopen](er?: NodeJS.ErrnoException | null, fd?: number) {\n    if (er) {\n      this[_onerror](er)\n    } else {\n      this[_fd] = fd\n      this.emit('open', fd as number)\n      this[_read]()\n    }\n  }\n\n  [_makeBuf]() {\n    return Buffer.allocUnsafe(Math.min(this[_readSize], this[_remain]))\n  }\n\n  [_read]() {\n    if (!this[_reading]) {\n      this[_reading] = true\n      const buf = this[_makeBuf]()\n      /* c8 ignore start */\n      if (buf.length === 0) {\n        return process.nextTick(() => this[_onread](null, 0, buf))\n      }\n      /* c8 ignore stop */\n      fs.read(this[_fd] as number, buf, 0, buf.length, null, (er, br, b) =>\n        this[_onread](er, br, b),\n      )\n    }\n  }\n\n  [_onread](er?: NodeJS.ErrnoException | null, br?: number, buf?: Buffer) {\n    this[_reading] = false\n    if (er) {\n      this[_onerror](er)\n    } else if (this[_handleChunk](br as number, buf as Buffer)) {\n      this[_read]()\n    }\n  }\n\n  [_close]() {\n    if (this[_autoClose] && typeof this[_fd] === 'number') {\n      const fd = this[_fd]\n      this[_fd] = undefined\n      fs.close(fd, er =>\n        er ? this.emit('error', er) : this.emit('close'),\n      )\n    }\n  }\n\n  [_onerror](er: NodeJS.ErrnoException) {\n    this[_reading] = true\n    this[_close]()\n    this.emit('error', er)\n  }\n\n  [_handleChunk](br: number, buf: Buffer) {\n    let ret = false\n    // no effect if infinite\n    this[_remain] -= br\n    if (br > 0) {\n      ret = super.write(br < buf.length ? buf.subarray(0, br) : buf)\n    }\n\n    if (br === 0 || this[_remain] <= 0) {\n      ret = false\n      this[_close]()\n      super.end()\n    }\n\n    return ret\n  }\n\n  emit<Event extends keyof ReadStreamEvents>(\n    ev: Event,\n    ...args: ReadStreamEvents[Event]\n  ): boolean {\n    switch (ev) {\n      case 'prefinish':\n      case 'finish':\n        return false\n\n      case 'drain':\n        if (typeof this[_fd] === 'number') {\n          this[_read]()\n        }\n        return false\n\n      case 'error':\n        if (this[_errored]) {\n          return false\n        }\n        this[_errored] = true\n        return super.emit(ev, ...args)\n\n      default:\n        return super.emit(ev, ...args)\n    }\n  }\n}\n\nexport class ReadStreamSync extends ReadStream {\n  [_open]() {\n    let threw = true\n    try {\n      this[_onopen](null, fs.openSync(this[_path], 'r'))\n      threw = false\n    } finally {\n      if (threw) {\n        this[_close]()\n      }\n    }\n  }\n\n  [_read]() {\n    let threw = true\n    try {\n      if (!this[_reading]) {\n        this[_reading] = true\n        do {\n          const buf = this[_makeBuf]()\n          /* c8 ignore start */\n          const br =\n            buf.length === 0\n              ? 0\n              : fs.readSync(this[_fd] as number, buf, 0, buf.length, null)\n          /* c8 ignore stop */\n          if (!this[_handleChunk](br, buf)) {\n            break\n          }\n        } while (true)\n        this[_reading] = false\n      }\n      threw = false\n    } finally {\n      if (threw) {\n        this[_close]()\n      }\n    }\n  }\n\n  [_close]() {\n    if (this[_autoClose] && typeof this[_fd] === 'number') {\n      const fd = this[_fd]\n      this[_fd] = undefined\n      fs.closeSync(fd)\n      this.emit('close')\n    }\n  }\n}\n\nexport type WriteStreamOptions = {\n  fd?: number\n  autoClose?: boolean\n  mode?: number\n  captureRejections?: boolean\n  start?: number\n  flags?: string\n}\n\nexport class WriteStream extends EE {\n  readable: false = false\n  writable: boolean = true;\n  [_errored]: boolean = false;\n  [_writing]: boolean = false;\n  [_ended]: boolean = false;\n  [_queue]: Buffer[] = [];\n  [_needDrain]: boolean = false;\n  [_path]: string;\n  [_mode]: number;\n  [_autoClose]: boolean;\n  [_fd]?: number;\n  [_defaultFlag]: boolean;\n  [_flags]: string;\n  [_finished]: boolean = false;\n  [_pos]?: number\n\n  constructor(path: string, opt: WriteStreamOptions) {\n    opt = opt || {}\n    super(opt)\n    this[_path] = path\n    this[_fd] = typeof opt.fd === 'number' ? opt.fd : undefined\n    this[_mode] = opt.mode === undefined ? 0o666 : opt.mode\n    this[_pos] = typeof opt.start === 'number' ? opt.start : undefined\n    this[_autoClose] =\n      typeof opt.autoClose === 'boolean' ? opt.autoClose : true\n\n    // truncating makes no sense when writing into the middle\n    const defaultFlag = this[_pos] !== undefined ? 'r+' : 'w'\n    this[_defaultFlag] = opt.flags === undefined\n    this[_flags] = opt.flags === undefined ? defaultFlag : opt.flags\n\n    if (this[_fd] === undefined) {\n      this[_open]()\n    }\n  }\n\n  emit(ev: string, ...args: any[]) {\n    if (ev === 'error') {\n      if (this[_errored]) {\n        return false\n      }\n      this[_errored] = true\n    }\n    return super.emit(ev, ...args)\n  }\n\n  get fd() {\n    return this[_fd]\n  }\n\n  get path() {\n    return this[_path]\n  }\n\n  [_onerror](er: NodeJS.ErrnoException) {\n    this[_close]()\n    this[_writing] = true\n    this.emit('error', er)\n  }\n\n  [_open]() {\n    fs.open(this[_path], this[_flags], this[_mode], (er, fd) =>\n      this[_onopen](er, fd),\n    )\n  }\n\n  [_onopen](er?: null | NodeJS.ErrnoException, fd?: number) {\n    if (\n      this[_defaultFlag] &&\n      this[_flags] === 'r+' &&\n      er &&\n      er.code === 'ENOENT'\n    ) {\n      this[_flags] = 'w'\n      this[_open]()\n    } else if (er) {\n      this[_onerror](er)\n    } else {\n      this[_fd] = fd\n      this.emit('open', fd)\n      if (!this[_writing]) {\n        this[_flush]()\n      }\n    }\n  }\n\n  end(buf: string, enc?: BufferEncoding): this\n  end(buf?: Buffer, enc?: undefined): this\n  end(buf?: Buffer | string, enc?: BufferEncoding): this {\n    if (buf) {\n      //@ts-ignore\n      this.write(buf, enc)\n    }\n\n    this[_ended] = true\n\n    // synthetic after-write logic, where drain/finish live\n    if (\n      !this[_writing] &&\n      !this[_queue].length &&\n      typeof this[_fd] === 'number'\n    ) {\n      this[_onwrite](null, 0)\n    }\n    return this\n  }\n\n  write(buf: string, enc?: BufferEncoding): boolean\n  write(buf: Buffer, enc?: undefined): boolean\n  write(buf: Buffer | string, enc?: BufferEncoding): boolean {\n    if (typeof buf === 'string') {\n      buf = Buffer.from(buf, enc)\n    }\n\n    if (this[_ended]) {\n      this.emit('error', new Error('write() after end()'))\n      return false\n    }\n\n    if (this[_fd] === undefined || this[_writing] || this[_queue].length) {\n      this[_queue].push(buf)\n      this[_needDrain] = true\n      return false\n    }\n\n    this[_writing] = true\n    this[_write](buf)\n    return true\n  }\n\n  [_write](buf: Buffer) {\n    fs.write(\n      this[_fd] as number,\n      buf,\n      0,\n      buf.length,\n      this[_pos],\n      (er, bw) => this[_onwrite](er, bw),\n    )\n  }\n\n  [_onwrite](er?: null | NodeJS.ErrnoException, bw?: number) {\n    if (er) {\n      this[_onerror](er)\n    } else {\n      if (this[_pos] !== undefined && typeof bw === 'number') {\n        this[_pos] += bw\n      }\n      if (this[_queue].length) {\n        this[_flush]()\n      } else {\n        this[_writing] = false\n\n        if (this[_ended] && !this[_finished]) {\n          this[_finished] = true\n          this[_close]()\n          this.emit('finish')\n        } else if (this[_needDrain]) {\n          this[_needDrain] = false\n          this.emit('drain')\n        }\n      }\n    }\n  }\n\n  [_flush]() {\n    if (this[_queue].length === 0) {\n      if (this[_ended]) {\n        this[_onwrite](null, 0)\n      }\n    } else if (this[_queue].length === 1) {\n      this[_write](this[_queue].pop() as Buffer)\n    } else {\n      const iovec = this[_queue]\n      this[_queue] = []\n      writev(this[_fd] as number, iovec, this[_pos] as number, (er, bw) =>\n        this[_onwrite](er, bw),\n      )\n    }\n  }\n\n  [_close]() {\n    if (this[_autoClose] && typeof this[_fd] === 'number') {\n      const fd = this[_fd]\n      this[_fd] = undefined\n      fs.close(fd, er =>\n        er ? this.emit('error', er) : this.emit('close'),\n      )\n    }\n  }\n}\n\nexport class WriteStreamSync extends WriteStream {\n  [_open](): void {\n    let fd\n    // only wrap in a try{} block if we know we'll retry, to avoid\n    // the rethrow obscuring the error's source frame in most cases.\n    if (this[_defaultFlag] && this[_flags] === 'r+') {\n      try {\n        fd = fs.openSync(this[_path], this[_flags], this[_mode])\n      } catch (er) {\n        if ((er as NodeJS.ErrnoException)?.code === 'ENOENT') {\n          this[_flags] = 'w'\n          return this[_open]()\n        } else {\n          throw er\n        }\n      }\n    } else {\n      fd = fs.openSync(this[_path], this[_flags], this[_mode])\n    }\n\n    this[_onopen](null, fd)\n  }\n\n  [_close]() {\n    if (this[_autoClose] && typeof this[_fd] === 'number') {\n      const fd = this[_fd]\n      this[_fd] = undefined\n      fs.closeSync(fd)\n      this.emit('close')\n    }\n  }\n\n  [_write](buf: Buffer) {\n    // throw the original, but try to close if it fails\n    let threw = true\n    try {\n      this[_onwrite](\n        null,\n        fs.writeSync(this[_fd] as number, buf, 0, buf.length, this[_pos]),\n      )\n      threw = false\n    } finally {\n      if (threw) {\n        try {\n          this[_close]()\n        } catch {\n          // ok error\n        }\n      }\n    }\n  }\n}\n"]}