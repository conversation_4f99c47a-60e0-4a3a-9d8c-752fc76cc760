(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))i(r);new MutationObserver(r=>{for(const a of r)if(a.type==="childList")for(const n of a.addedNodes)n.tagName==="LINK"&&n.rel==="modulepreload"&&i(n)}).observe(document,{childList:!0,subtree:!0});function e(r){const a={};return r.integrity&&(a.integrity=r.integrity),r.referrerPolicy&&(a.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?a.credentials="include":r.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function i(r){if(r.ep)return;r.ep=!0;const a=e(r);fetch(r.href,a)}})();const c={colors:{success:"#20c997",warning:"#ffc107",danger:"#dc3545",primary:"#4e73df",secondary:"#6c757d"},productionTarget:1e3,endShiftTarget:1200},s={yearElement:document.getElementById("current-year"),productionCanvas:document.getElementById("productionChart"),hourlyProductionCanvas:document.getElementById("hourlyProductionChart"),machineInfoBody:document.getElementById("machineInfoBody")};document.addEventListener("DOMContentLoaded",function(){C()});function C(){s.yearElement&&(s.yearElement.textContent=new Date().getFullYear()),M(),s.productionCanvas&&E(),s.hourlyProductionCanvas&&u("today"),s.machineInfoBody&&m(),initEventListeners()}function M(){[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')).forEach(t=>{new bootstrap.Tooltip(t)})}function E(){try{const o=s.productionCanvas.getContext("2d"),t=["MC-10 CXC","MC-11 CXC","MC-12 CXC","MC-13 CXC","MC-14 CXC","MC-15 CXC","MC-16 CXC","MC-17 CXC"],e=[950,1100,800,1200,650,900,1e3,1300],i=e.map(r=>{const a=r/c.productionTarget*100;return a>=100?c.colors.success:a>=80?c.colors.warning:c.colors.danger});return new Chart(o,{type:"bar",data:{labels:t,datasets:[{label:"Production",data:e,backgroundColor:i,borderColor:i.map(r=>r.replace("0.6","1")),borderWidth:1,barPercentage:.7,categoryPercentage:.8}]},options:v()})}catch(o){return console.error("Erreur lors de l'initialisation du graphique de production:",o),null}}function v(){return{responsive:!0,maintainAspectRatio:!1,scales:{y:{beginAtZero:!0,title:{display:!0,text:"Quantité Produite"},grid:{drawBorder:!1},ticks:{callback:o=>o.toLocaleString()}},x:{grid:{display:!1}}},plugins:{legend:{position:"bottom",labels:{usePointStyle:!0,padding:20}},tooltip:{callbacks:{label:function(o){let t=o.dataset.label||"";return t&&(t+=": "),o.parsed.y!==null&&(t+=o.parsed.y),t}}},animation:{duration:1e3,easing:"easeInOutQuart"}}}}function B(){const o=document.getElementById("startProductionBtn"),t=new bootstrap.Modal(document.getElementById("startProductionModal")),e=document.getElementById("machineSelectModal"),i=document.getElementById("confirmStartProduction");o&&o.addEventListener("click",function(){t.show()}),e&&document.querySelectorAll('#machineSelect option[value^="T"]').forEach(a=>{if(a.value){const n=document.createElement("option");n.value=a.value,n.textContent=a.textContent,e.appendChild(n)}}),i&&i.addEventListener("click",function(){const r=e?e.value:"",a=document.getElementById("orderNumber")?document.getElementById("orderNumber").value:"",n=document.getElementById("quantity")?document.getElementById("quantity").value:"";if(!r||!a||!n){alert("Please fill in all fields");return}console.log(`Starting production for ${r}, Order: ${a}, Quantity: ${n}`);const d=e?e.options[e.selectedIndex].text:r;alert(`Production started for ${d}
Order: ${a}
Quantity: ${n}`),document.getElementById("startProductionForm")&&document.getElementById("startProductionForm").reset(),t.hide()})}B();const I=[{id:"MC-10",modele:"CXC-2000",annee:2020,statut:"En service",derniereMaintenance:"2025-06-15",prochaineMaintenance:"2025-08-15",heuresFonctionnement:2450},{id:"MC-11",modele:"CXC-2000",annee:2020,statut:"En service",derniereMaintenance:"2025-06-20",prochaineMaintenance:"2025-08-20",heuresFonctionnement:2380},{id:"MC-12",modele:"CXC-2000",annee:2021,statut:"Maintenance",derniereMaintenance:"2025-06-10",prochaineMaintenance:"2025-08-10",heuresFonctionnement:1980},{id:"MC-13",modele:"CXC-3000",annee:2021,statut:"En service",derniereMaintenance:"2025-06-25",prochaineMaintenance:"2025-08-25",heuresFonctionnement:2150},{id:"MC-14",modele:"CXC-3000",annee:2022,statut:"En service",derniereMaintenance:"2025-07-01",prochaineMaintenance:"2025-09-01",heuresFonctionnement:1750},{id:"MC-15",modele:"CXC-4000",annee:2022,statut:"Hors service",derniereMaintenance:"2025-05-30",prochaineMaintenance:"2025-07-30",heuresFonctionnement:1650},{id:"MC-16",modele:"CXC-4000",annee:2023,statut:"En service",derniereMaintenance:"2025-07-05",prochaineMaintenance:"2025-09-05",heuresFonctionnement:1200},{id:"MC-17",modele:"CXC-4000",annee:2023,statut:"En service",derniereMaintenance:"2025-07-10",prochaineMaintenance:"2025-09-10",heuresFonctionnement:980}];function p(o){const t={year:"numeric",month:"2-digit",day:"2-digit"};return new Date(o).toLocaleDateString("fr-FR",t)}function L(o){switch(o.toLowerCase()){case"en service":return"badge bg-success";case"maintenance":return"badge bg-warning text-dark";case"hors service":return"badge bg-danger";default:return"badge bg-secondary"}}function m(){const o=document.getElementById("machineInfoBody");o.innerHTML="",I.forEach(t=>{const e=document.createElement("tr");t.statut==="Hors service"?e.classList.add("table-danger"):t.statut==="Maintenance"&&e.classList.add("table-warning"),e.innerHTML=`
                    <td><strong>${t.id}</strong></td>
                    <td>${t.modele}</td>
                    <td>${t.annee}</td>
                    <td><span class="${L(t.statut)}">${t.statut}</span></td>
                    <td>${p(t.derniereMaintenance)}</td>
                    <td>${p(t.prochaineMaintenance)}</td>
                    <td>${t.heuresFonctionnement.toLocaleString()} h</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" title="Voir les détails">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-warning me-1" title="Planifier une maintenance">
                            <i class="fas fa-tools"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" title="Historique">
                            <i class="fas fa-history"></i>
                        </button>
                    </td>
                `,o.appendChild(e)})}const $={today:{labels:Array.from({length:24},(o,t)=>`${t}:00`),actual:Array.from({length:24},()=>Math.floor(Math.random()*100)+50),target:Array(24).fill(80)},yesterday:{labels:Array.from({length:24},(o,t)=>`${t}:00`),actual:Array.from({length:24},()=>Math.floor(Math.random()*90)+40),target:Array(24).fill(80)},week:{labels:["Lun","Mar","Mer","Jeu","Ven","Sam","Dim"],actual:[550,620,580,600,650,700,450],target:Array(7).fill(600)}};let l;function u(o="today"){const t=document.getElementById("hourlyProductionChart").getContext("2d"),e=$[o];l&&l.destroy(),l=new Chart(t,{type:"bar",data:{labels:e.labels,datasets:[{label:"Production Réelle",data:e.actual,backgroundColor:"rgba(78, 115, 223, 0.8)",borderColor:"rgba(78, 115, 223, 1)",borderWidth:1,borderRadius:4,yAxisID:"y",barPercentage:.8,categoryPercentage:.8},{label:"Objectif",data:e.target,type:"line",borderColor:"rgba(220, 53, 69, 0.8)",borderWidth:2,borderDash:[5,5],fill:!1,pointRadius:0,yAxisID:"y"}]},options:{responsive:!0,maintainAspectRatio:!1,interaction:{mode:"index",intersect:!1},scales:{y:{beginAtZero:!0,type:"linear",display:!0,position:"left",title:{display:!0,text:"Quantité produite"},grid:{drawOnChartArea:!0,drawBorder:!1}},x:{grid:{display:!1},title:{display:!0,text:o==="week"?"Jours de la Semaine":"Heures de la Journée"}}},plugins:{legend:{display:!0,position:"top",labels:{usePointStyle:!0,boxWidth:6}},tooltip:{mode:"index",intersect:!1,callbacks:{label:function(i){return`${i.dataset.label}: ${i.parsed.y} unités`}}}},animation:{duration:1e3,easing:"easeInOutQuart"}}})}document.querySelectorAll("[data-period]").forEach(o=>{o.addEventListener("click",function(){document.querySelectorAll("[data-period]").forEach(t=>{t.classList.remove("active")}),this.classList.add("active"),u(this.dataset.period)})});document.addEventListener("DOMContentLoaded",function(){m(),u("today"),[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map(function(n){return new bootstrap.Tooltip(n)});const t=document.getElementById("dashboard-link"),e=document.getElementById("reports-link"),i=document.querySelector(".dashboard"),r=document.getElementById("reportModal");t.addEventListener("click",function(n){n.preventDefault(),i.style.display="block",new bootstrap.Modal(r).hide()}),e.addEventListener("click",function(n){n.preventDefault(),i.style.display="none",new bootstrap.Modal(r).show()}),P(),document.getElementById("generateReportBtn").addEventListener("click",function(){const n=document.getElementById("reportType").value,d=document.getElementById("reportFormat").value,g=document.getElementById("reportStartDate").value,f=document.getElementById("reportEndDate").value,y=Array.from(document.getElementById("reportMachines").selectedOptions).map(b=>b.value),h=document.getElementById("includeCharts").checked;alert(`Rapport généré:
Type: ${n}
Format: ${d}
Date de début: ${g}
Date de fin: ${f}
Machines: ${y.join(", ")}
Inclure les graphiques: ${h}`)})});async function P(){try{const e=(await(await fetch("js/data.json")).json()).data,i={output:e.reduce((n,d)=>n+(d.output||0),0)/e.length,uptime:e.reduce((n,d)=>n+(d.uptime||0),0)/e.length,unplanned_dt:e.reduce((n,d)=>n+(d.unplanned_dt||0),0)/e.length,planned_dt:e.reduce((n,d)=>n+(d.planned_dt||0),0)/e.length,setup_time:e.reduce((n,d)=>n+(d.setup_time||0),0)/e.length,pmh:e.reduce((n,d)=>n+(d.pmh||0),0)/e.length,ftq:e.reduce((n,d)=>n+(d.ftq||0),0)/e.length,throughput:e.reduce((n,d)=>n+(d.throughput||0),0)/e.length,avg_target_setup:e.reduce((n,d)=>n+(d.avg_target_setup||0),0)/e.length,compliance_setup:e.reduce((n,d)=>n+(d.compliance_setup||0),0)/e.length},r=document.querySelector(".table-bordered tbody"),a=`
                    <tr>
                        <td><strong>Averages</strong></td>
                        <td><span class="status-badge bg-info"></span> ${i.output.toFixed(2)}</td>
                        <td><span class="status-badge bg-info"></span> ${i.uptime.toFixed(2)}%</td>
                        <td><span class="status-badge bg-info"></span> ${i.unplanned_dt.toFixed(2)}%</td>
                        <td><span class="status-badge bg-info"></span> ${i.planned_dt.toFixed(2)}%</td>
                        <td><span class="status-badge bg-info"></span> ${i.setup_time.toFixed(2)}%</td>
                        <td>00:00:00</td>
                        <td>${i.pmh.toFixed(2)}</td>
                        <td>${i.ftq.toFixed(2)}%</td>
                        <td>${i.throughput.toFixed(2)}</td>
                        <td>00:00:00</td>
                        <td>${i.compliance_setup.toFixed(2)}%</td>
                    </tr>
                `;r.innerHTML+=a}catch(o){console.error("Error loading or processing data:",o)}}
