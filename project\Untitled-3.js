// your code goes here
document.getElementById("generateButton").addEventListener("click", function () {
    const input = document.getElementById("barcodeInput").value;
    const barcodeElement = document.getElementById("barcode");

    if (input.trim() === "") {
        alert("Veuillez entrer un texte ou un numéro !");
        return;
    }

    // Générer le code-barres avec JsBarcode
    JsBarcode(barcodeElement, input, {
        format: "CODE128",
        lineColor: "#000",
        width: 2,
        height: 50,
        displayValue: true,
    });
});
