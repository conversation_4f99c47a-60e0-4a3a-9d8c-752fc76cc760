# Informations de Shift Ajoutées aux Détails des Machines

## 🎯 Fonctionnalités Ajoutées

✅ **Informations d'équipe détaillées** dans les détails de machine  
✅ **Horaires de shift** avec badges colorés  
✅ **Opérateur assigné** pour chaque machine  
✅ **Prochaine relève** avec temps restant  
✅ **Performances par équipe** (Jour/Après-midi/Nuit)  
✅ **Historique des équipes** sur 7 jours  

## 📊 Nouvelles Informations Affichées

### **Quand vous sélectionnez une machine (ex: T-10 CXC):**

#### **Section Équipe Actuelle:**
```
Équipe actuelle: [Jour] 06:00 - 14:00
Opérateur: <PERSON>cha<PERSON> équipe: Après-midi dans 3 heures
```

#### **Section Performances par Équipe:**
```
┌─────────────────────────────────────────────────┐
│ Équipe Jour    │ Équipe Après-midi │ Équipe Nuit │
│     95%        │       88%         │     82%     │
│ 06:00 - 14:00  │  14:00 - 22:00    │ 22:00 - 06:00│
└─────────────────────────────────────────────────┘
```

#### **Informations Temps Réel:**
```
Équipe actuelle: Jour | Prochaine relève dans: 3 heures
```

## 🕐 Gestion des Shifts

### **3 Équipes Définies:**
- **Équipe Jour**: 06:00 - 14:00 (Badge jaune)
- **Équipe Après-midi**: 14:00 - 22:00 (Badge bleu)
- **Équipe Nuit**: 22:00 - 06:00 (Badge bleu)

### **Calcul Automatique:**
- **Équipe actuelle**: Déterminée selon l'heure système
- **Prochaine relève**: Calculée automatiquement
- **Temps restant**: Affiché en heures

### **Opérateurs Assignés:**
- **T-10 CXC**: Jean Dupont
- **T-11 CXC**: Marie Martin
- **T-12 CXC**: Pierre Durand
- **T-13 CXC**: Sophie Leroy
- **T-14 CXC**: Michel Bernard
- **T-15 CXC**: Anne Moreau
- **T-16 CXC**: Laurent Petit
- **T-17 CXC**: Isabelle Roux

## 📈 Performances par Équipe

### **Données de Performance (Exemple T-10 CXC):**
- **Équipe Jour**: 95% d'efficacité
- **Équipe Après-midi**: 88% d'efficacité
- **Équipe Nuit**: 82% d'efficacité

### **Codes Couleur:**
- **Vert**: Performance ≥ 100%
- **Jaune**: Performance ≥ 80%
- **Rouge**: Performance < 80%

## 📋 Historique des Équipes

### **Bouton "Historique équipes":**
Affiche un tableau des 7 derniers jours avec:
- **Date**: Jour de la semaine + date
- **Performance par équipe**: Jour, Après-midi, Nuit
- **Performance moyenne**: Moyenne des 3 équipes
- **Badges colorés**: Selon le niveau de performance

### **Exemple d'Historique:**
```
┌─────────┬─────────┬─────────────┬─────────┬─────────────┐
│ Date    │ Jour    │ Après-midi  │ Nuit    │ Moyenne     │
├─────────┼─────────┼─────────────┼─────────┼─────────────┤
│ Lun 15  │ [95%]   │ [88%]       │ [82%]   │ 88%         │
│ Mar 16  │ [92%]   │ [85%]       │ [79%]   │ 85%         │
│ Mer 17  │ [98%]   │ [91%]       │ [84%]   │ 91%         │
└─────────┴─────────┴─────────────┴─────────┴─────────────┘
```

## 🎮 Comment Utiliser

### **Voir les Informations de Shift:**
1. **Sélectionner** une machine (ex: T-10 CXC) dans le filtre
2. **Consulter** la section "Équipe actuelle" avec badge coloré
3. **Voir** l'opérateur assigné et les horaires
4. **Observer** les performances par équipe

### **Consulter l'Historique:**
1. **Cliquer** sur "Historique équipes" dans les détails
2. **Consulter** le tableau des 7 derniers jours
3. **Analyser** les performances par équipe
4. **Fermer** avec le bouton "Fermer l'historique"

### **Informations Temps Réel:**
- **Équipe actuelle**: Mise à jour selon l'heure système
- **Temps restant**: Calculé dynamiquement
- **Prochaine équipe**: Rotation automatique

## 🔧 Fonctionnalités Techniques

### **Calcul Automatique de l'Équipe:**
```javascript
function getCurrentShift() {
  const currentHour = new Date().getHours();
  if (currentHour >= 6 && currentHour < 14) return 'Jour';
  if (currentHour >= 14 && currentHour < 22) return 'Après-midi';
  return 'Nuit';
}
```

### **Calcul du Temps Restant:**
```javascript
function getTimeToNextShift(currentShift) {
  // Calcule les heures restantes jusqu'à la prochaine relève
  // Retourne "X heures" ou "1 heure"
}
```

### **Données de Performance:**
- **Simulées**: Basées sur des données réalistes
- **Variables**: Légère variation quotidienne
- **Cohérentes**: Performance Jour > Après-midi > Nuit

## 🎨 Interface Utilisateur

### **Badges d'Équipe:**
- **Équipe Jour**: Badge jaune (`bg-warning`)
- **Équipe Après-midi/Nuit**: Badge bleu (`bg-info`)

### **Cartes de Performance:**
- **3 cartes**: Une par équipe
- **Fond gris clair**: `bg-light`
- **Centré**: Performance en pourcentage
- **Horaires**: Affichés sous la performance

### **Tableau d'Historique:**
- **Responsive**: S'adapte à la taille d'écran
- **Badges colorés**: Performance par équipe
- **Compact**: Table `table-sm`

## 📁 Modifications Apportées

### **`js/script.js`:**
- `allMachineData.details` - Ajout opérateur, horaires
- `getShiftTime()` - Horaires par équipe
- `getNextShift()` - Prochaine équipe
- `getTimeToNextShift()` - Temps restant
- `getCurrentShift()` - Équipe actuelle
- `shiftPerformanceData` - Performances par équipe
- `showShiftHistory()` - Affichage historique
- `generateShiftHistory()` - Génération données historiques

### **Affichage des Détails:**
- Section équipe avec badge coloré
- Opérateur assigné
- Prochaine relève
- Cartes de performance par équipe
- Bouton historique

## 🎉 Résultat Final

**Les détails de machine incluent maintenant toutes les informations de shift!**

✅ **Équipe actuelle** avec badge coloré et horaires  
✅ **Opérateur assigné** pour chaque machine  
✅ **Prochaine relève** avec temps restant calculé  
✅ **Performances par équipe** (3 cartes)  
✅ **Historique 7 jours** avec tableau détaillé  
✅ **Mise à jour temps réel** selon l'heure système  

### **Exemple Complet pour T-10 CXC:**
```
Détails de la machine: T-10 CXC
┌─────────────────────────────────────────────────┐
│ Production: 950  │ Objectif: 1000  │ Efficacité: 95% │
│                                                 │
│ Équipe actuelle: [Jour] 06:00 - 14:00          │
│ Opérateur: Jean Dupont                          │
│ Prochaine équipe: Après-midi dans 3 heures     │
│                                                 │
│ ┌─────────┬─────────────┬─────────┐             │
│ │ Jour    │ Après-midi  │ Nuit    │             │
│ │ 95%     │ 88%         │ 82%     │             │
│ │06-14h   │ 14-22h      │ 22-06h  │             │
│ └─────────┴─────────────┴─────────┘             │
│                                                 │
│ Équipe actuelle: Jour | Prochaine relève: 3h   │
│                                                 │
│ [Voir toutes] [Actualiser] [Historique équipes]│
└─────────────────────────────────────────────────┘
```

**Toutes les informations de shift sont maintenant disponibles dans les détails des machines!** 🕐✅
