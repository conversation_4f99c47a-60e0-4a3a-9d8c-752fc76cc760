.machine-card.highlight {
    border: 2px solid #0dcaf0;
    box-shadow: 0 0 15px rgba(13, 202, 240, 0.5);
    transition: all 0.3s ease-in-out;
}

@import "tailwindcss";

/* Loading animations */
@keyframes loading-bar {
    0% {
        transform: translateX(-100%);
    }

    50% {
        transform: translateX(0%);
    }

    100% {
        transform: translateX(100%);
    }
}

@keyframes fade-in {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.animate-fade-in {
    animation: fade-in 0.3s ease-out;
}

:root {
    /* Background colors */
    --background: #0f172a;
    --foreground: #f8fafc;
    --background-secondary: #1e293b;
    --background-tertiary: #334155;
    --background-card: #1e293b;

    /* APTIV Orange palette */
    --aptiv-orange: #f97316;
    --aptiv-orange-dark: #ea580c;
    --aptiv-orange-light: #fb923c;
    --aptiv-orange-50: #fff7ed;
    --aptiv-orange-100: #ffedd5;
    --aptiv-orange-200: #fed7aa;
    --aptiv-orange-300: #fdba74;
    --aptiv-orange-400: #fb923c;
    --aptiv-orange-500: #f97316;
    --aptiv-orange-600: #ea580c;
    --aptiv-orange-700: #c2410c;
    --aptiv-orange-800: #9a3412;
    --aptiv-orange-900: #7c2d12;

    /* Dark theme colors */
    --dark-50: #f8fafc;
    --dark-100: #f1f5f9;
    --dark-200: #e2e8f0;
    --dark-300: #cbd5e1;
    --dark-400: #94a3b8;
    --dark-500: #64748b;
    --dark-600: #475569;
    --dark-700: #334155;
    --dark-800: #1e293b;
    --dark-900: #0f172a;
    --dark-950: #020617;

    /* Status colors */
    --status-running: #10b981;
    --status-stopped: #ef4444;
    --status-warning: #f59e0b;
    --status-maintenance: #6b7280;
    --status-idle: #3b82f6;
    --status-error: #dc2626;
    --status-success: #059669;

    /* Semantic colors */
    --semantic-info: #0ea5e9;
    --semantic-success: #10b981;
    --semantic-warning: #f59e0b;
    --semantic-error: #ef4444;
    --semantic-critical: #dc2626;

    /* Text colors */
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;
    --text-muted: #64748b;

    /* Border colors */
    --border-primary: #334155;
    --border-secondary: #475569;
    --border-accent: rgba(249, 115, 22, 0.2);

    /* Shadow colors */
    --shadow-machine: 0 4px 6px -1px rgba(234, 88, 12, 0.3), 0 2px 4px -1px rgba(234, 88, 12, 0.2);
    --shadow-machine-lg: 0 10px 15px -3px rgba(234, 88, 12, 0.3), 0 4px 6px -2px rgba(234, 88, 12, 0.2);
    --shadow-glow: 0 0 20px rgba(249, 115, 22, 0.3);
}

/* Remove @theme as it's not standard CSS */

body {
    background: linear-gradient(135deg, var(--dark-900) 0%, var(--dark-800) 25%, var(--dark-900) 50%, var(--dark-800) 75%, var(--dark-900) 100%);
    background-attachment: fixed;
    background-size: 400% 400%;
    animation: gradientShift 20s ease infinite;
    color: var(--foreground);
    font-family: 'Inter', system-ui, sans-serif;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    position: relative;
}

/* Subtle animated background gradient */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

/* Industrial grid pattern overlay */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(249, 115, 22, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(249, 115, 22, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
    pointer-events: none;
    z-index: -1;
}

/* Enhanced background utility classes */
.bg-solid-dark {
    background: linear-gradient(135deg, var(--dark-900) 0%, var(--dark-800) 100%) !important;
}

.bg-solid-card {
    background: linear-gradient(135deg, var(--dark-800) 0%, var(--dark-700) 100%) !important;
    border: 1px solid rgba(249, 115, 22, 0.1);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.bg-solid-darker {
    background: linear-gradient(135deg, var(--dark-700) 0%, var(--dark-600) 100%) !important;
}

.bg-solid-aptiv {
    background: linear-gradient(135deg, var(--aptiv-orange) 0%, var(--aptiv-orange-dark) 100%) !important;
}

/* New enhanced background classes */
.bg-industrial-card {
    background: linear-gradient(135deg,
            rgba(30, 41, 59, 0.95) 0%,
            rgba(51, 65, 85, 0.95) 50%,
            rgba(30, 41, 59, 0.95) 100%);
    border: 1px solid rgba(249, 115, 22, 0.2);
    backdrop-filter: blur(20px);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.bg-dashboard-section {
    background: linear-gradient(135deg,
            rgba(15, 23, 42, 0.8) 0%,
            rgba(30, 41, 59, 0.8) 100%);
    border: 1px solid rgba(249, 115, 22, 0.15);
    backdrop-filter: blur(15px);
}

.bg-machine-card {
    background: linear-gradient(135deg,
            rgba(30, 41, 59, 0.9) 0%,
            rgba(51, 65, 85, 0.9) 100%);
    border: 2px solid transparent;
    background-clip: padding-box;
    position: relative;
}

.bg-machine-card::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(135deg,
            rgba(249, 115, 22, 0.3) 0%,
            rgba(234, 88, 12, 0.3) 100%);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    z-index: -1;
}

/* Override Tailwind transparency classes with solid colors */
.bg-dark-900 {
    background-color: var(--dark-900) !important;
}

.bg-dark-800 {
    background-color: var(--dark-800) !important;
}

.bg-dark-700 {
    background-color: var(--dark-700) !important;
}

.bg-dark-600 {
    background-color: var(--dark-600) !important;
}

/* Remove any transparency from overlays */
.bg-black\/50,
.bg-black\/60,
.bg-black\/70,
.bg-black\/80,
.bg-black\/90 {
    background-color: rgba(0, 0, 0, 0.9) !important;
}

/* Custom scrollbar for industrial look */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--dark-800);
}

::-webkit-scrollbar-thumb {
    background: var(--aptiv-orange);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--aptiv-orange-dark);
}

/* Machine status indicator animations */
@keyframes pulse-status {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.8;
    }
}

.status-indicator {
    animation: pulse-status 2s ease-in-out infinite;
}

/* Industrial button styles - solid backgrounds */
.btn-industrial {
    background: var(--aptiv-orange);
    border: 2px solid var(--aptiv-orange);
    transition: all 0.3s ease;
    color: white;
}

.btn-industrial:hover {
    background: var(--aptiv-orange-dark);
    border-color: var(--aptiv-orange-dark);
    box-shadow: 0 4px 12px rgba(234, 88, 12, 0.4);
    transform: translateY(-1px);
}

/* Machine card shadows */
.shadow-machine {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

.shadow-machine-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

.shadow-machine-xl {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* Status color utilities */
.text-status-running {
    color: var(--status-running);
}

.text-status-stopped {
    color: var(--status-stopped);
}

.text-status-warning {
    color: var(--status-warning);
}

.text-status-maintenance {
    color: var(--status-maintenance);
}

.text-status-idle {
    color: var(--status-idle);
}

.bg-status-running {
    background-color: var(--status-running);
}

.bg-status-stopped {
    background-color: var(--status-stopped);
}

.bg-status-warning {
    background-color: var(--status-warning);
}

.bg-status-maintenance {
    background-color: var(--status-maintenance);
}

.bg-status-idle {
    background-color: var(--status-idle);
}

/* APTIV color utilities */
.text-aptiv-400 {
    color: var(--aptiv-orange-light);
}

.text-aptiv-500 {
    color: var(--aptiv-orange);
}

.text-aptiv-600 {
    color: var(--aptiv-orange-dark);
}

.bg-aptiv-400 {
    background-color: var(--aptiv-orange-light);
}

.bg-aptiv-500 {
    background-color: var(--aptiv-orange);
}

.bg-aptiv-600 {
    background-color: var(--aptiv-orange-dark);
}

.border-aptiv-400 {
    border-color: var(--aptiv-orange-light);
}

.border-aptiv-500 {
    border-color: var(--aptiv-orange);
}

.border-aptiv-600 {
    border-color: var(--aptiv-orange-dark);
}

/* Remove transparency from all elements */
* {
    backdrop-filter: none !important;
}

/* Ensure modal overlays are solid */
.fixed.inset-0 {
    background-color: rgba(0, 0, 0, 0.9) !important;
}

/* Remove backdrop blur effects */
.backdrop-blur,
.backdrop-blur-sm,
.backdrop-blur-md,
.backdrop-blur-lg,
.backdrop-blur-xl {
    backdrop-filter: none !important;
    background-color: var(--dark-800) !important;
}

/* Print styles for production cards */
@media print {
    @page {
        size: A4 landscape;
        margin: 0.5in;
    }

    body {
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    .print-hidden {
        display: none !important;
    }

    .print-shadow-none {
        box-shadow: none !important;
    }

    .print-rounded-none {
        border-radius: 0 !important;
    }

    .print-mb-4 {
        margin-bottom: 1rem !important;
    }

    table {
        page-break-inside: avoid;
    }

    .production-card {
        page-break-inside: avoid;
    }
}

.over-hid {
    overflow: hidden;
}

/* Enhanced login page background styles */
.login-background {
    background:
        linear-gradient(135deg,
            rgba(15, 23, 42, 0.9) 0%,
            rgba(30, 41, 59, 0.8) 25%,
            rgba(15, 23, 42, 0.9) 50%,
            rgba(30, 41, 59, 0.8) 75%,
            rgba(15, 23, 42, 0.9) 100%),
        url('/uhk.png');
    background-size: 400% 400%, cover;
    background-position: 0% 50%, center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    position: relative;
    animation: loginGradientShift 15s ease infinite;
}

@keyframes loginGradientShift {
    0% {
        background-position: 0% 50%, center;
    }

    50% {
        background-position: 100% 50%, center;
    }

    100% {
        background-position: 0% 50%, center;
    }
}

.login-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(249, 115, 22, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(249, 115, 22, 0.1) 0%, transparent 50%);
    z-index: 1;
}

.login-background::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        linear-gradient(rgba(249, 115, 22, 0.02) 1px, transparent 1px),
        linear-gradient(90deg, rgba(249, 115, 22, 0.02) 1px, transparent 1px);
    background-size: 60px 60px;
    z-index: 1;
}

.login-content {
    position: relative;
    z-index: 2;
    backdrop-filter: blur(5px);
    background: rgba(15, 23, 42, 0.3);
    border-radius: 16px;
    border: 1px solid rgba(249, 115, 22, 0.2);
    padding: 2rem;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}









.operator-action-button {
    background: var(--aptiv-orange);
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    color: white;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.operator-action-button:hover {
    background: var(--aptiv-orange-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.4);
}

.operator-action-button:active {
    transform: translateY(0);
}

.operator-action-button.secondary {
    background: var(--dark-600);
    color: var(--foreground);
}

.operator-action-button.secondary:hover {
    background: var(--dark-500);
}

/* Easy-to-use UI Enhancements */
.quick-action-toolbar {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    z-index: 1000;
}

.quick-action-btn {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: var(--aptiv-orange);
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quick-action-btn:hover {
    background: var(--aptiv-orange-dark);
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(249, 115, 22, 0.4);
}

.quick-action-btn.emergency {
    background: var(--status-stopped);
    animation: pulse-emergency 2s ease-in-out infinite;
}

@keyframes pulse-emergency {

    0%,
    100% {
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    }

    50% {
        box-shadow: 0 6px 20px rgba(239, 68, 68, 0.6);
    }
}

/* Tooltip System */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: rgba(0, 0, 0, 0.9);
    color: white;
    text-align: center;
    border-radius: 6px;
    padding: 8px 12px;
    position: absolute;
    z-index: 1001;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 14px;
    line-height: 1.4;
}

.tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.9) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Enhanced Card Design */
.easy-card {
    background: linear-gradient(135deg,
            rgba(30, 41, 59, 0.95) 0%,
            rgba(51, 65, 85, 0.95) 100%);
    border: 2px solid transparent;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    backdrop-filter: blur(10px);
    box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.easy-card::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 12px;
    padding: 2px;
    background: linear-gradient(135deg,
            rgba(249, 115, 22, 0.2) 0%,
            rgba(234, 88, 12, 0.2) 100%);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: -1;
}

.easy-card:hover {
    transform: translateY(-4px);
    box-shadow:
        0 12px 32px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(249, 115, 22, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.easy-card:hover::before {
    opacity: 1;
}

.easy-card.active {
    background: linear-gradient(135deg,
            rgba(249, 115, 22, 0.1) 0%,
            rgba(30, 41, 59, 0.95) 25%,
            rgba(51, 65, 85, 0.95) 75%,
            rgba(234, 88, 12, 0.1) 100%);
    box-shadow:
        0 8px 25px rgba(249, 115, 22, 0.2),
        0 0 0 1px rgba(249, 115, 22, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.easy-card.active::before {
    opacity: 1;
}

/* Additional Background Utility Classes */
.bg-header-gradient {
    background: linear-gradient(135deg,
            rgba(15, 23, 42, 0.95) 0%,
            rgba(30, 41, 59, 0.95) 100%);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(249, 115, 22, 0.2);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.bg-sidebar-gradient {
    background: linear-gradient(180deg,
            rgba(15, 23, 42, 0.98) 0%,
            rgba(30, 41, 59, 0.98) 100%);
    backdrop-filter: blur(15px);
    border-right: 1px solid rgba(249, 115, 22, 0.15);
}

.bg-modal-backdrop {
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(8px);
}

.bg-status-card {
    background: linear-gradient(135deg,
            rgba(30, 41, 59, 0.9) 0%,
            rgba(51, 65, 85, 0.9) 100%);
    border: 1px solid rgba(249, 115, 22, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.bg-status-card:hover {
    border-color: rgba(249, 115, 22, 0.3);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.bg-production-card {
    background: linear-gradient(135deg,
            rgba(30, 41, 59, 0.92) 0%,
            rgba(51, 65, 85, 0.92) 50%,
            rgba(30, 41, 59, 0.92) 100%);
    border: 1px solid rgba(249, 115, 22, 0.12);
    backdrop-filter: blur(12px);
    position: relative;
}

.bg-production-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg,
            transparent 0%,
            rgba(249, 115, 22, 0.5) 50%,
            transparent 100%);
}

/* Animated background for active elements */
.bg-animated-active {
    background: linear-gradient(135deg,
            rgba(249, 115, 22, 0.1) 0%,
            rgba(30, 41, 59, 0.9) 25%,
            rgba(51, 65, 85, 0.9) 75%,
            rgba(234, 88, 12, 0.1) 100%);
    background-size: 200% 200%;
    animation: activeGlow 3s ease infinite;
}

@keyframes activeGlow {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

/* Large Touch-Friendly Buttons */
.touch-button {
    min-height: 48px;
    min-width: 120px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.touch-button.primary {
    background: var(--aptiv-orange);
    color: rgb(255, 255, 255);
}

.touch-button.primary:hover {
    background: var(--aptiv-orange-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.4);
}

.touch-button.success {
    background: var(--status-running);
    color: white;
}

.touch-button.success:hover {
    background: #059669;
    transform: translateY(-2px);
}

.touch-button.danger {
    background: var(--status-stopped);
    color: white;
}

.touch-button.danger:hover {
    background: #dc2626;
    transform: translateY(-2px);
}

.touch-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Status Indicators with Labels */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.status-badge.running {
    background: rgba(16, 185, 129, 0.2);
    color: var(--status-running);
    border: 1px solid var(--status-running);
}

.status-badge.stopped {
    background: rgba(239, 68, 68, 0.2);
    color: var(--status-stopped);
    border: 1px solid var(--status-stopped);
}

.status-badge.warning {
    background: rgba(245, 158, 11, 0.2);
    color: var(--status-warning);
    border: 1px solid var(--status-warning);
}

/* Progress Indicators */
.progress-ring {
    width: 60px;
    height: 60px;
    position: relative;
}

.progress-ring svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.progress-ring circle {
    fill: none;
    stroke-width: 4;
    stroke-linecap: round;
}

.progress-ring .background {
    stroke: var(--dark-600);
}

.progress-ring .progress {
    stroke: var(--aptiv-orange);
    stroke-dasharray: 157;
    stroke-dashoffset: 157;
    transition: stroke-dashoffset 0.5s ease;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    font-weight: 600;
    color: white;
}

/* Keyboard Navigation Hints */
.keyboard-hint {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-family: monospace;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.easy-card:hover .keyboard-hint {
    opacity: 1;
}

/* Styles pour le Tableau de Bord Simple */
.tableau-de-bord-simple {
    transition: all 0.3s ease;
}

.tableau-de-bord-simple:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 20px rgba(249, 115, 22, 0.1);
}

/* Animation des métriques */
.metrique-animation {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animation de la barre de progression */
.progress-bar-animation {
    transition: width 1.5s ease-in-out;
}

/* Indicateur de statut animé */
.status-pulse {
    animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

.opacity {
    opacity: 0;
}

.margin-t {
    margin-top: 2px;
}