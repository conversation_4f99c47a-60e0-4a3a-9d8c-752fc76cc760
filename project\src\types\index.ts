export interface User {
  id: string;
  username: string;
  email: string;
  role: 'manager' | 'accountant' | 'user';
  firstName: string;
  lastName: string;
  isActive: boolean;
  createdAt: Date;
}

export interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  contactPerson: string;
  balance: number;
  creditLimit: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Supplier {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  contactPerson: string;
  balance: number;
  paymentTerms: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Product {
  id: string;
  name: string;
  sku: string;
  description: string;
  category: string;
  unitPrice: number;
  stockQuantity: number;
  minStockLevel: number;
  supplierId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Transaction {
  id: string;
  type: 'sale' | 'purchase' | 'payment' | 'receipt';
  customerId?: string;
  supplierId?: string;
  amount: number;
  description: string;
  date: Date;
  status: 'pending' | 'completed' | 'cancelled';
}

export interface Report {
  id: string;
  name: string;
  type: 'inventory' | 'sales' | 'financial' | 'customer';
  filters: Record<string, any>;
  generatedAt: Date;
  generatedBy: string;
}