// Configuration globale
const CONFIG = {
  colors: {
    success: '#20c997',
    warning: '#ffc107',
    danger: '#dc3545',
    primary: '#4e73df',
    secondary: '#6c757d'
  },
  productionTarget: 1000,
  endShiftTarget: 1200
};

// Éléments DOM fréquemment utilisés
const DOM = {
  yearElement: document.getElementById('current-year'),
  productionCanvas: document.getElementById('productionChart'),
  hourlyProductionCanvas: document.getElementById('hourlyProductionChart'),
  machineInfoBody: document.getElementById('machineInfoBody')
};

// Initialisation de l'application
document.addEventListener('DOMContentLoaded', function () {
  initApp();
});

function initApp() {
  // Initialiser l'année dans le footer
  if (DOM.yearElement) {
    DOM.yearElement.textContent = new Date().getFullYear();
  }

  // Initialiser les tooltips
  initTooltips();

  // Initialiser les graphiques
  if (DOM.productionCanvas) {
    initProductionChart();
  }

  if (DOM.hourlyProductionCanvas) {
    initHourlyProductionChart('today');
  }

  // Initialiser la table des machines
  if (DOM.machineInfoBody) {
    displayMachines();
  }

  // Initialiser les écouteurs d'événements
  initEventListeners();
}

function initTooltips() {
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.forEach(tooltipTriggerEl => {
    new bootstrap.Tooltip(tooltipTriggerEl);
  });
}

function initEventListeners() {
  // Ajouter ici les écouteurs d'événements généraux
  console.log('Event listeners initialized');
}

// Données de production par machine (globales pour le filtrage)
const allMachineData = {
  labels: ['T10 ', 'T11 ', 'T12 ', 'T13 ', 'T14 ', 'T15 ', 'T16 ', 'T17 '],
  production: [950, 1100, 800, 1200, 650, 900, 1000, 1300],
  details: {
    'T10 ': {
      production: 950, target: 1000, efficiency: 95, status: 'En service',
      shift: getCurrentShift(), operator: 'Jean Dupont', shiftStart: '06:00', shiftEnd: '14:00'
    },
    'T-11 ': {
      production: 1100, target: 1000, efficiency: 110, status: 'En service',
      shift: getCurrentShift(), operator: 'Marie Martin', shiftStart: '06:00', shiftEnd: '14:00'
    },
    'T-12 ': {
      production: 800, target: 1000, efficiency: 80, status: 'Maintenance',
      shift: 'Nuit', operator: 'Pierre Durand', shiftStart: '22:00', shiftEnd: '06:00'
    },
    'T-13 ': {
      production: 1200, target: 1000, efficiency: 120, status: 'En service',
      shift: getCurrentShift(), operator: 'Sophie Leroy', shiftStart: '06:00', shiftEnd: '14:00'
    },
    'T-14 ': {
      production: 650, target: 1000, efficiency: 65, status: 'En service',
      shift: 'Nuit', operator: 'Michel Bernard', shiftStart: '22:00', shiftEnd: '06:00'
    },
    'T-15 ': {
      production: 900, target: 1000, efficiency: 90, status: 'En service',
      shift: getCurrentShift(), operator: 'Anne Moreau', shiftStart: '06:00', shiftEnd: '14:00'
    },
    'T16 ': {
      production: 1000, target: 1000, efficiency: 100, status: 'En service',
      shift: 'Nuit', operator: 'Laurent Petit', shiftStart: '22:00', shiftEnd: '06:00'
    },
    'T17 ': {
      production: 1300, target: 1000, efficiency: 130, status: 'En service',
      shift: getCurrentShift(), operator: 'Isabelle Roux', shiftStart: '06:00', shiftEnd: '14:00'
    }
  }
};

let productionChart = null;

function getCurrentShift() {
  const now = new Date();
  const currentHour = now.getHours();

  if (currentHour >= 6 && currentHour < 14) {
    return 'Jour';
  } else if (currentHour >= 14 && currentHour < 22) {
    return 'Après-midi';
  } else {
    return 'Nuit';
  }
}

function initProductionChart(selectedMachine = null) {
  try {
    const productionCtx = DOM.productionCanvas.getContext('2d');

    // Détruire le graphique existant s'il existe
    if (productionChart) {
      productionChart.destroy();
    }

    let machineLabels, productionData;

    if (selectedMachine && selectedMachine !== 'all') {
      // Afficher seulement la machine sélectionnée
      machineLabels = [selectedMachine];
      const machineIndex = allMachineData.labels.indexOf(selectedMachine);
      productionData = [allMachineData.production[machineIndex]];
    } else {
      // Afficher toutes les machines
      machineLabels = allMachineData.labels;
      productionData = allMachineData.production;
    }

    // Calcul des couleurs en fonction du pourcentage par rapport à la cible
    const backgroundColors = productionData.map(quantity => {
      const percentage = (quantity / CONFIG.productionTarget) * 100;
      if (percentage >= 100) return CONFIG.colors.success;  // Vert si objectif atteint
      if (percentage >= 80) return CONFIG.colors.warning;   // Jaune si proche de l'objectif
      return CONFIG.colors.danger;                          // Rouge si en dessous de 80%
    });

    // Création du graphique
    productionChart = new Chart(productionCtx, {
      type: 'bar',
      data: {
        labels: machineLabels,
        datasets: [{
          label: 'Production',
          data: productionData,
          backgroundColor: backgroundColors,
          borderColor: backgroundColors.map(color => color.replace('0.6', '1')),
          borderWidth: 1,
          barPercentage: 0.7,
          categoryPercentage: 0.8
        }]
      },
      options: {
        ...getChartOptions(),
        onClick: (event, elements) => {
          if (elements.length > 0) {
            const elementIndex = elements[0].index;
            const clickedMachine = machineLabels[elementIndex];
            showMachineDetails(clickedMachine);
          }
        }
      }
    });

    // Afficher les détails si une machine spécifique est sélectionnée
    if (selectedMachine && selectedMachine !== 'all') {
      showMachineDetails(selectedMachine);
    }

    return productionChart;
  } catch (error) {
    console.error('Erreur lors de l\'initialisation du graphique de production:', error);
    return null;
  }
}

function getChartOptions() {
  return {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        title: { display: true, text: 'Quantité Produite' },
        grid: { drawBorder: false },
        ticks: { callback: value => value.toLocaleString() }
      },
      x: { grid: { display: false } }
    },
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          padding: 20
        }
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              label += context.parsed.y;
            }
            return label;
          }
        }
      },
      animation: {
        duration: 1000,
        easing: 'easeInOutQuart'
      }
    }
  };
}

// ===== FONCTIONNALITÉ START PRODUCTION PAR HEURE =====

// Variables globales pour la production par heure
let hourlyProductionSessions = [];
let currentHourlySession = null;

// Initialisation de la fonctionnalité Start Production par Heure
function initHourlyProductionControls() {
  const startBtn = document.getElementById('startHourlyProductionBtn');
  const confirmBtn = document.getElementById('confirmStartHourlyProduction');

  if (startBtn) {
    startBtn.addEventListener('click', function () {
      // Pré-remplir l'heure actuelle
      const now = new Date();
      const currentTime = now.toTimeString().slice(0, 5);
      document.getElementById('hourlyStartTime').value = currentTime;

      // Pré-sélectionner l'équipe actuelle
      const currentShift = getCurrentShift();
      document.getElementById('hourlyShiftSelect').value = currentShift;

      updateHourlySummary();
    });
  }

  if (confirmBtn) {
    confirmBtn.addEventListener('click', startHourlyProduction);
  }

  // Écouteurs pour mise à jour du résumé en temps réel
  const formInputs = [
    'hourlyMachineSelect', 'hourlyShiftSelect', 'hourlyTargetQuantity',
    'hourlyDuration', 'hourlyStartTime'
  ];

  formInputs.forEach(inputId => {
    const input = document.getElementById(inputId);
    if (input) {
      input.addEventListener('change', updateHourlySummary);
      input.addEventListener('input', updateHourlySummary);
    }
  });

  // Charger les sessions existantes
  loadHourlyProductionSessions();
}

// Fonction pour démarrer une session de production par heure
function startHourlyProduction() {
  const form = document.getElementById('startHourlyProductionForm');

  // Validation des champs
  const machine = document.getElementById('hourlyMachineSelect').value;
  const shift = document.getElementById('hourlyShiftSelect').value;
  const orderNumber = document.getElementById('hourlyOrderNumber').value;
  const targetQuantity = parseInt(document.getElementById('hourlyTargetQuantity').value);
  const startTime = document.getElementById('hourlyStartTime').value;
  const duration = parseInt(document.getElementById('hourlyDuration').value);
  const operator = document.getElementById('hourlyOperator').value;
  const notes = document.getElementById('hourlyNotes').value;

  if (!machine || !shift || !orderNumber || !targetQuantity || !startTime || !duration || !operator) {
    showAlert('Veuillez remplir tous les champs obligatoires', 'warning');
    return;
  }

  // Créer une nouvelle session
  const session = {
    id: generateSessionId(),
    machine: machine,
    shift: shift,
    orderNumber: orderNumber,
    targetQuantity: targetQuantity,
    startTime: startTime,
    duration: duration,
    operator: operator,
    notes: notes,
    startDate: new Date().toISOString(),
    status: 'active',
    actualProduction: [],
    totalProduced: 0
  };

  // Ajouter la session
  hourlyProductionSessions.push(session);
  currentHourlySession = session;

  // Sauvegarder dans localStorage
  saveHourlyProductionSessions();

  // Démarrer le suivi de production
  startProductionTracking(session);

  // Fermer la modal
  const modal = bootstrap.Modal.getInstance(document.getElementById('startHourlyProductionModal'));
  modal.hide();

  // Réinitialiser le formulaire
  form.reset();

  // Afficher confirmation
  showAlert(`Production démarrée pour ${machine} - Ordre: ${orderNumber}`, 'success');

  // Mettre à jour l'affichage
  updateHourlyProductionDisplay();
}

// Fonction pour générer un ID de session unique
function generateSessionId() {
  return 'session_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
}

// Fonction pour démarrer le suivi de production
function startProductionTracking(session) {
  // Simuler la production en temps réel
  const trackingInterval = setInterval(() => {
    if (session.status !== 'active') {
      clearInterval(trackingInterval);
      return;
    }

    // Simuler une production aléatoire
    const currentHour = new Date().getHours();
    const randomProduction = Math.floor(Math.random() * (session.targetQuantity * 0.3)) +
      Math.floor(session.targetQuantity * 0.7);

    // Ajouter à la production actuelle
    session.actualProduction.push({
      hour: currentHour,
      quantity: randomProduction,
      timestamp: new Date().toISOString()
    });

    session.totalProduced += randomProduction;

    // Sauvegarder
    saveHourlyProductionSessions();

    // Mettre à jour l'affichage
    updateHourlyProductionDisplay();

    // Vérifier si la session est terminée
    const elapsedHours = (Date.now() - new Date(session.startDate).getTime()) / (1000 * 60 * 60);
    if (elapsedHours >= session.duration) {
      session.status = 'completed';
      clearInterval(trackingInterval);
      showAlert(`Session terminée pour ${session.machine} - Total produit: ${session.totalProduced}`, 'info');
    }

  }, 60000); // Mise à jour chaque minute (pour la démo, en production ce serait plus fréquent)
}

// Fonction pour mettre à jour le résumé de configuration
function updateHourlySummary() {
  const machine = document.getElementById('hourlyMachineSelect').value;
  const shift = document.getElementById('hourlyShiftSelect').value;
  const targetQuantity = document.getElementById('hourlyTargetQuantity').value;
  const duration = document.getElementById('hourlyDuration').value;

  // Mettre à jour les éléments du résumé
  document.getElementById('summaryMachine').textContent = machine || '-';
  document.getElementById('summaryShift').textContent = shift || '-';
  document.getElementById('summaryTarget').textContent = targetQuantity || '-';
  document.getElementById('summaryDuration').textContent = duration ? `${duration} heure(s)` : '-';

  // Calculer la production totale prévue
  if (targetQuantity && duration) {
    const totalExpected = parseInt(targetQuantity) * parseInt(duration);
    document.getElementById('summaryTotal').textContent = totalExpected.toLocaleString();
  } else {
    document.getElementById('summaryTotal').textContent = '-';
  }
}

// Fonction pour sauvegarder les sessions dans localStorage
function saveHourlyProductionSessions() {
  try {
    localStorage.setItem('hourlyProductionSessions', JSON.stringify(hourlyProductionSessions));
  } catch (error) {
    console.error('Erreur lors de la sauvegarde des sessions:', error);
  }
}

// Fonction pour charger les sessions depuis localStorage
function loadHourlyProductionSessions() {
  try {
    const saved = localStorage.getItem('hourlyProductionSessions');
    if (saved) {
      hourlyProductionSessions = JSON.parse(saved);

      // Reprendre les sessions actives
      hourlyProductionSessions.forEach(session => {
        if (session.status === 'active') {
          startProductionTracking(session);
        }
      });
    }
  } catch (error) {
    console.error('Erreur lors du chargement des sessions:', error);
    hourlyProductionSessions = [];
  }
}

// Fonction pour mettre à jour l'affichage de la production par heure
function updateHourlyProductionDisplay() {
  // Cette fonction peut être étendue pour mettre à jour le graphique
  // avec les données de production en temps réel
  if (currentHourlySession && currentHourlySession.actualProduction.length > 0) {
    console.log('Session active:', currentHourlySession.machine,
      'Total produit:', currentHourlySession.totalProduced);
  }
}

// Fonction pour afficher des alertes
function showAlert(message, type = 'info') {
  // Créer une alerte Bootstrap
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
  alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';

  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
  `;

  document.body.appendChild(alertDiv);

  // Supprimer automatiquement après 5 secondes
  setTimeout(() => {
    if (alertDiv.parentElement) {
      alertDiv.remove();
    }
  }, 5000);
}

// Ajouter l'initialisation à la fonction initApp
function initEventListeners() {
  // Initialiser les contrôles de production par heure
  initHourlyProductionControls();

  console.log('Event listeners initialized');
}
