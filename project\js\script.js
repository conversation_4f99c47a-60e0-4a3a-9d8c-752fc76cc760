
// Configuration globale
const CONFIG = {
  colors: {
    success: '#20c997',
    warning: '#ffc107',
    danger: '#dc3545',
    primary: '#4e73df',
    secondary: '#6c757d'
  },
  productionTarget: 1000,
  endShiftTarget: 1200
};

// Éléments DOM fréquemment utilisés
const DOM = {
  yearElement: document.getElementById('current-year'),
  productionCanvas: document.getElementById('productionChart'),
  hourlyProductionCanvas: document.getElementById('hourlyProductionChart'),
  machineInfoBody: document.getElementById('machineInfoBody')
};

// Initialisation de l'application
document.addEventListener('DOMContentLoaded', function () {
  initApp();
});

function initApp() {
  // Initialiser l'année dans le footer
  if (DOM.yearElement) {
    DOM.yearElement.textContent = new Date().getFullYear();
  }

  // Initialiser les tooltips
  initTooltips();

  // Initialiser les graphiques
  if (DOM.productionCanvas) {
    initProductionChart();
  }

  if (DOM.hourlyProductionCanvas) {
    initHourlyProductionChart('today');
  }

  // Initialiser la table des machines
  if (DOM.machineInfoBody) {
    displayMachines();
  }

  // Initialiser le scanner de code-barres
  initBarcodeScanner();

  // Initialiser les écouteurs d'événements
  initEventListeners();
}

function initTooltips() {
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.forEach(tooltipTriggerEl => {
    new bootstrap.Tooltip(tooltipTriggerEl);
  });
}

// Données de production par machine (globales pour le filtrage)
const allMachineData = {
  labels: ['T10 ', 'T11 ', 'T12 ', 'T13 ', 'T14 ', 'T15 ', 'T16 ', 'T17 '],
  production: [950, 1100, 800, 1200, 650, 900, 1000, 1300],
  details: {
    'T10 ': {
      production: 950, target: 1000, efficiency: 95, status: 'En service',
      shift: getCurrentShift(), operator: 'Jean Dupont', shiftStart: '06:00', shiftEnd: '14:00'
    },
    'T-11 ': {
      production: 1100, target: 1000, efficiency: 110, status: 'En service',
      shift: getCurrentShift(), operator: 'Marie Martin', shiftStart: '06:00', shiftEnd: '14:00'
    },
    'T-12 ': {
      production: 800, target: 1000, efficiency: 80, status: 'Maintenance',
      shift: 'Nuit', operator: 'Pierre Durand', shiftStart: '22:00', shiftEnd: '06:00'
    },
    'T-13 ': {
      production: 1200, target: 1000, efficiency: 120, status: 'En service',
      shift: getCurrentShift(), operator: 'Sophie Leroy', shiftStart: '06:00', shiftEnd: '14:00'
    },
    'T-14 ': {
      production: 650, target: 1000, efficiency: 65, status: 'En service',
      shift: 'Nuit', operator: 'Michel Bernard', shiftStart: '22:00', shiftEnd: '06:00'
    },
    'T-15 ': {
      production: 900, target: 1000, efficiency: 90, status: 'En service',
      shift: getCurrentShift(), operator: 'Anne Moreau', shiftStart: '06:00', shiftEnd: '14:00'
    },
    'T16 ': {
      production: 1000, target: 1000, efficiency: 100, status: 'En service',
      shift: 'Nuit', operator: 'Laurent Petit', shiftStart: '22:00', shiftEnd: '06:00'
    },
    'T17 ': {
      production: 1300, target: 1000, efficiency: 130, status: 'En service',
      shift: getCurrentShift(), operator: 'Isabelle Roux', shiftStart: '06:00', shiftEnd: '14:00'
    }
  }
};

let productionChart = null;

function initProductionChart(selectedMachine = null) {
  try {
    const productionCtx = DOM.productionCanvas.getContext('2d');

    // Détruire le graphique existant s'il existe
    if (productionChart) {
      productionChart.destroy();
    }

    let machineLabels, productionData;

    if (selectedMachine && selectedMachine !== 'all') {
      // Afficher seulement la machine sélectionnée
      machineLabels = [selectedMachine];
      const machineIndex = allMachineData.labels.indexOf(selectedMachine);
      productionData = [allMachineData.production[machineIndex]];
    } else {
      // Afficher toutes les machines
      machineLabels = allMachineData.labels;
      productionData = allMachineData.production;
    }

    // Calcul des couleurs en fonction du pourcentage par rapport à la cible
    const backgroundColors = productionData.map(quantity => {
      const percentage = (quantity / CONFIG.productionTarget) * 100;
      if (percentage >= 100) return CONFIG.colors.success;  // Vert si objectif atteint
      if (percentage >= 80) return CONFIG.colors.warning;   // Jaune si proche de l'objectif
      return CONFIG.colors.danger;                          // Rouge si en dessous de 80%
    });

    // Création du graphique
    productionChart = new Chart(productionCtx, {
      type: 'bar',
      data: {
        labels: machineLabels,
        datasets: [{
          label: 'Production',
          data: productionData,
          backgroundColor: backgroundColors,
          borderColor: backgroundColors.map(color => color.replace('0.6', '1')),
          borderWidth: 1,
          barPercentage: 0.7,
          categoryPercentage: 0.8
        }]
      },
      options: {
        ...getChartOptions(),
        onClick: (event, elements) => {
          if (elements.length > 0) {
            const elementIndex = elements[0].index;
            const clickedMachine = machineLabels[elementIndex];
            showMachineDetails(clickedMachine);
          }
        }
      }
    });

    // Afficher les détails si une machine spécifique est sélectionnée
    if (selectedMachine && selectedMachine !== 'all') {
      showMachineDetails(selectedMachine);
    }

    return productionChart;
  } catch (error) {
    console.error('Erreur lors de l\'initialisation du graphique de production:', error);
    return null;
  }
}

function getChartOptions() {
  return {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        title: { display: true, text: 'Quantité Produite' },
        grid: { drawBorder: false },
        ticks: { callback: value => value.toLocaleString() }
      },
      x: { grid: { display: false } }
    },
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          padding: 20
        }
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              label += context.parsed.y;
            }
            return label;
          }
        }
      },
      animation: {
        duration: 1000,
        easing: 'easeInOutQuart'
      }
    }
  };
}

// Fonction pour afficher les détails d'une machine spécifique
function showMachineDetails(machineName) {
  const machineInfo = allMachineData.details[machineName];
  if (!machineInfo) return;

  // Créer ou mettre à jour la section des détails
  let detailsSection = document.getElementById('machineDetailsSection');
  if (!detailsSection) {
    detailsSection = document.createElement('div');
    detailsSection.id = 'machineDetailsSection';
    detailsSection.className = 'mt-4';

    // Insérer après le graphique
    const chartContainer = document.querySelector('#productionChart').closest('.card');
    chartContainer.parentNode.insertBefore(detailsSection, chartContainer.nextSibling);
  }

  detailsSection.innerHTML = `
    <div class="card">
      <div class="card-header bg-info text-white">
        <h5 class="mb-0">
          <i class="fas fa-cog me-2"></i>
          Détails de la machine: ${machineName}
        </h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-3">
            <div class="text-center">
              <h3 class="text-primary">${machineInfo.production}</h3>
              <p class="text-muted mb-0">Production Actuelle</p>
            </div>
          </div>
          <div class="col-md-3">
            <div class="text-center">
              <h3 class="text-secondary">${machineInfo.target}</h3>
              <p class="text-muted mb-0">Objectif</p>
            </div>
          </div>
          <div class="col-md-3">
            <div class="text-center">
              <h3 class="${machineInfo.efficiency >= 100 ? 'text-success' : machineInfo.efficiency >= 80 ? 'text-warning' : 'text-danger'}">${machineInfo.efficiency}%</h3>
              <p class="text-muted mb-0">Efficacité</p>
            </div>
          </div>
          <div class="col-md-3">
            <div class="text-center">
              <span class="badge ${getStatusClass(machineInfo.status)} fs-6">${machineInfo.status}</span>
              <p class="text-muted mb-0 mt-1">Statut</p>
            </div>
          </div>
        </div>

        <hr>

        <div class="row">
          <div class="col-md-6">
            <p><strong>SIFT:</strong>
              <span class="badge ${machineInfo.shift === 'Jour' ? 'bg-warning' : 'bg-info'}">${machineInfo.shift}</span>
              <small class="text-muted ms-2">${getShiftTime(machineInfo.shift)}</small>
            </p>
            <p><strong>Écart par rapport à l'objectif:</strong>
              <span class="${machineInfo.production >= machineInfo.target ? 'text-success' : 'text-danger'}">
                ${machineInfo.production - machineInfo.target > 0 ? '+' : ''}${machineInfo.production - machineInfo.target}
              </span>
            </p>
            <p><strong>Opérateur:</strong>
              <span class="text-primary">${machineInfo.operator || 'Non assigné'}</span>
            </p>
            <p><strong>Prochaine équipe:</strong>
              <span class="text-muted">${getNextShift(machineInfo.shift)} dans ${getTimeToNextShift(machineInfo.shift)}</span>
            </p>
          </div>
          <div class="col-md-6">
            <div class="progress mb-2">
              <div class="progress-bar ${machineInfo.efficiency >= 100 ? 'bg-success' : machineInfo.efficiency >= 80 ? 'bg-warning' : 'bg-danger'}"
                   role="progressbar"
                   style="width: ${Math.min(machineInfo.efficiency, 100)}%"
                   aria-valuenow="${machineInfo.efficiency}"
                   aria-valuemin="0"
                   aria-valuemax="100">
                ${machineInfo.efficiency}%
              </div>
            </div>
            <small class="text-muted">Progression vers l'objectif</small>
          </div>
        </div>

        <!-- Informations détaillées sur les shifts -->
        <div class="mt-4">
          <h6><i class="fas fa-clock me-2"></i>Informations des équipes</h6>
          <div class="row">
            <div class="col-md-4">
              <div class="card bg-light">
                <div class="card-body text-center py-2">
                  <small class="text-muted">Équipe Jour</small>
                  <div class="fw-bold">${getShiftPerformance(machineName, 'Jour')}%</div>
                  <small class="text-muted">06:00 - 14:00</small>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="card bg-light">
                <div class="card-body text-center py-2">
                  <small class="text-muted">Équipe Après-midi</small>
                  <div class="fw-bold">${getShiftPerformance(machineName, 'Après-midi')}%</div>
                  <small class="text-muted">14:00 - 22:00</small>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="card bg-light">
                <div class="card-body text-center py-2">
                  <small class="text-muted">Équipe Nuit</small>
                  <div class="fw-bold">${getShiftPerformance(machineName, 'Nuit')}%</div>
                  <small class="text-muted">22:00 - 06:00</small>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-3">
            <small class="text-muted">
              <i class="fas fa-info-circle me-1"></i>
              Équipe actuelle: <strong>${getCurrentShift()}</strong> |
              Prochaine relève dans: <strong>${getTimeToNextShift(getCurrentShift())}</strong>
            </small>
          </div>
        </div>

        <div class="mt-3">
          <button class="btn btn-primary btn-sm me-2" onclick="filterByMachine('all')">
            <i class="fas fa-list me-1"></i> Voir toutes les machines
          </button>
          <button class="btn btn-outline-info btn-sm me-2" onclick="refreshMachineData('${machineName}')">
            <i class="fas fa-sync-alt me-1"></i> Actualiser
          </button>
          <button class="btn btn-outline-secondary btn-sm" onclick="showShiftHistory('${machineName}')">
            <i class="fas fa-history me-1"></i> Historique équipes
          </button>
        </div>
      </div>
    </div>
  `;
}

// Fonction pour filtrer par machine
function filterByMachine(machineName) {
  initProductionChart(machineName);

  if (machineName === 'all') {
    // Supprimer la section des détails si elle existe
    const detailsSection = document.getElementById('machineDetailsSection');
    if (detailsSection) {
      detailsSection.remove();
    }
  }
}

// Fonctions utilitaires pour les shifts
function getShiftTime(shift) {
  const shiftTimes = {
    'Jour': '06:00 - 14:00',
    'Nuit': '22:00 - 06:00',
    'Après-midi': '14:00 - 22:00'
  };
  return shiftTimes[shift] || '06:00 - 14:00';
}

function getNextShift(currentShift) {
  const shiftSequence = {
    'Jour': 'Après-midi',
    'Après-midi': 'Nuit',
    'Nuit': 'Jour'
  };
  return shiftSequence[currentShift] || 'Après-midi';
}

function getTimeToNextShift(currentShift) {
  const now = new Date();
  const currentHour = now.getHours();

  let hoursToNext;
  switch (currentShift) {
    case 'Jour':
      hoursToNext = currentHour < 14 ? 14 - currentHour : 24 - currentHour + 14;
      break;
    case 'Après-midi':
      hoursToNext = currentHour < 22 ? 22 - currentHour : 24 - currentHour + 22;
      break;
    case 'Nuit':
      hoursToNext = currentHour < 6 ? 6 - currentHour : 24 - currentHour + 6;
      break;
    default:
      hoursToNext = 2;
  }

  return hoursToNext === 1 ? '1 heure' : `${hoursToNext} heures`;
}

function getCurrentShift() {
  const now = new Date();
  const currentHour = now.getHours();

  if (currentHour >= 6 && currentHour < 14) {
    return 'Jour';
  } else if (currentHour >= 14 && currentHour < 22) {
    return 'Après-midi';
  } else {
    return 'Nuit';
  }
}

// Données de performance par shift (simulées)
const shiftPerformanceData = {
  'T10 ': { 'Jour': 95, 'Après-midi': 88, 'Nuit': 82 },
  'T11 ': { 'Jour': 110, 'Après-midi': 105, 'Nuit': 98 },
  'T12 ': { 'Jour': 80, 'Après-midi': 75, 'Nuit': 70 },
  'T13 ': { 'Jour': 120, 'Après-midi': 115, 'Nuit': 108 },
  'T14 ': { 'Jour': 65, 'Après-midi': 70, 'Nuit': 60 },
  'T15 ': { 'Jour': 90, 'Après-midi': 85, 'Nuit': 88 },
  'T16 ': { 'Jour': 100, 'Après-midi': 95, 'Nuit': 92 },
  'T17 ': { 'Jour': 130, 'Après-midi': 125, 'Nuit': 120 }
};

function getShiftPerformance(machineName, shift) {
  return shiftPerformanceData[machineName]?.[shift] || 85;
}

function showShiftHistory(machineName) {
  // Créer ou mettre à jour la section d'historique des shifts
  let historySection = document.getElementById('shiftHistorySection');
  if (!historySection) {
    historySection = document.createElement('div');
    historySection.id = 'shiftHistorySection';
    historySection.className = 'mt-4';

    // Insérer après la section des détails de machine
    const detailsSection = document.getElementById('machineDetailsSection');
    detailsSection.parentNode.insertBefore(historySection, detailsSection.nextSibling);
  }

  // Générer des données d'historique simulées
  const historyData = generateShiftHistory(machineName);

  historySection.innerHTML = `
    <div class="card">
      <div class="card-header bg-secondary text-white">
        <h6 class="mb-0">
          <i class="fas fa-history me-2"></i>
          Historique des équipes - ${machineName} (7 derniers jours)
        </h6>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-sm">
            <thead>
              <tr>
                <th>Date</th>
                <th>Équipe Jour</th>
                <th>Équipe Après-midi</th>
                <th>Équipe Nuit</th>
                <th>Performance Moyenne</th>
              </tr>
            </thead>
            <tbody>
              ${historyData.map(day => `
                <tr>
                  <td>${day.date}</td>
                  <td><span class="badge ${getPerformanceBadgeClass(day.jour)}">${day.jour}%</span></td>
                  <td><span class="badge ${getPerformanceBadgeClass(day.apresmidi)}">${day.apresmidi}%</span></td>
                  <td><span class="badge ${getPerformanceBadgeClass(day.nuit)}">${day.nuit}%</span></td>
                  <td><strong>${day.moyenne}%</strong></td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>

        <div class="mt-3">
          <button class="btn btn-outline-secondary btn-sm" onclick="closeShiftHistory()">
            <i class="fas fa-times me-1"></i> Fermer l'historique
          </button>
        </div>
      </div>
    </div>
  `;
}

function generateShiftHistory(machineName) {
  const history = [];
  const basePerformance = shiftPerformanceData[machineName] || { 'Jour': 85, 'Après-midi': 80, 'Nuit': 75 };

  for (let i = 6; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);

    const jour = Math.max(50, basePerformance.Jour + Math.floor(Math.random() * 20) - 10);
    const apresmidi = Math.max(50, basePerformance['Après-midi'] + Math.floor(Math.random() * 20) - 10);
    const nuit = Math.max(50, basePerformance.Nuit + Math.floor(Math.random() * 20) - 10);
    const moyenne = Math.round((jour + apresmidi + nuit) / 3);

    history.push({
      date: date.toLocaleDateString('fr-FR', { weekday: 'short', day: '2-digit', month: '2-digit' }),
      jour,
      apresmidi,
      nuit,
      moyenne
    });
  }

  return history;
}

function getPerformanceBadgeClass(performance) {
  if (performance >= 100) return 'bg-success';
  if (performance >= 80) return 'bg-warning';
  return 'bg-danger';
}

function closeShiftHistory() {
  const historySection = document.getElementById('shiftHistorySection');
  if (historySection) {
    historySection.remove();
  }
}

// Fonction pour actualiser les données d'une machine
function refreshMachineData(machineName) {
  // Simuler une actualisation des données
  const machineInfo = allMachineData.details[machineName];
  if (machineInfo) {
    // Simuler de nouvelles données (dans un vrai système, ceci viendrait d'une API)
    const variation = Math.floor(Math.random() * 100) - 50; // Variation de -50 à +50
    const newProduction = Math.max(0, machineInfo.production + variation);

    // Mettre à jour les données
    allMachineData.details[machineName].production = newProduction;
    allMachineData.details[machineName].efficiency = Math.round((newProduction / machineInfo.target) * 100);

    // Mettre à jour l'array de production principal
    const machineIndex = allMachineData.labels.indexOf(machineName);
    if (machineIndex !== -1) {
      allMachineData.production[machineIndex] = newProduction;
    }

    // Rafraîchir l'affichage
    initProductionChart(machineName);

    // Afficher un message de confirmation
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show mt-3';
    alertDiv.innerHTML = `
      <i class="fas fa-check me-2"></i>
      Données de ${machineName} actualisées: ${newProduction} unités produites
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const detailsSection = document.getElementById('machineDetailsSection');
    if (detailsSection) {
      detailsSection.appendChild(alertDiv);

      // Supprimer l'alerte après 3 secondes
      setTimeout(() => {
        if (alertDiv.parentElement) {
          alertDiv.remove();
        }
      }, 3000);
    }
  }
}

// Start Production Functionality
function initProductionControls() {
  const startProductionBtn = document.getElementById('startProductionBtn');
  const startProductionModal = new bootstrap.Modal(document.getElementById('startProductionModal'));
  const machineSelectModal = document.getElementById('machineSelectModal');
  const confirmStartBtn = document.getElementById('confirmStartProduction');

  if (startProductionBtn) {
    startProductionBtn.addEventListener('click', function () {
      startProductionModal.show();
    });
  }

  // Populate machine dropdown in modal if it exists
  if (machineSelectModal) {
    const machineOptions = document.querySelectorAll('#machineSelect option[value^="T"]');
    machineOptions.forEach(option => {
      if (option.value) {
        const newOption = document.createElement('option');
        newOption.value = option.value;
        newOption.textContent = option.textContent;
        machineSelectModal.appendChild(newOption);
      }
    });
  }

  // Handle form submission
  if (confirmStartBtn) {
    confirmStartBtn.addEventListener('click', function () {
      const machine = machineSelectModal ? machineSelectModal.value : '';
      const orderNumber = document.getElementById('orderNumber') ? document.getElementById('orderNumber').value : '';
      const quantity = document.getElementById('quantity') ? document.getElementById('quantity').value : '';

      if (!machine || !orderNumber || !quantity) {
        alert('Please fill in all fields');
        return;
      }

      // Here you would typically make an API call to start production
      console.log(`Starting production for ${machine}, Order: ${orderNumber}, Quantity: ${quantity}`);

      // Show success message
      const machineName = machineSelectModal ? machineSelectModal.options[machineSelectModal.selectedIndex].text : machine;
      alert(`Production started for ${machineName}\nOrder: ${orderNumber}\nQuantity: ${quantity}`);

      // Reset form and close modal
      if (document.getElementById('startProductionForm')) {
        document.getElementById('startProductionForm').reset();
      }
      startProductionModal.hide();
    });
  }
}

// Initialisation des contrôles de production
initProductionControls();

// Données des machines (à remplacer par un appel API dans un environnement de production)
const machinesData = [
  { id: 'T10', modele: 'TWIST', annee: 2020, statut: 'En service', derniereMaintenance: '2025-06-15', prochaineMaintenance: '2025-08-15', heuresFonctionnement: 2450 },
  { id: 'T11', modele: 'TWIST', annee: 2020, statut: 'En service', derniereMaintenance: '2025-06-20', prochaineMaintenance: '2025-08-20', heuresFonctionnement: 2380 },
  { id: 'T12', modele: 'TWIST', annee: 2021, statut: 'Maintenance', derniereMaintenance: '2025-06-10', prochaineMaintenance: '2025-08-10', heuresFonctionnement: 1980 },
  { id: 'T13', modele: 'TWIST', annee: 2021, statut: 'En service', derniereMaintenance: '2025-06-25', prochaineMaintenance: '2025-0825', heuresFonctionnement: 2150 },
  { id: 'T14', modele: 'TWIST', annee: 2022, statut: 'En service', derniereMaintenance: '2025-07-01', prochaineMaintenance: '2025-09-01', heuresFonctionnement: 1750 },
  { id: 'T15', modele: 'TWIST', annee: 2022, statut: 'Hors service', derniereMaintenance: '2025-05-30', prochaineMaintenance: '2025-07-30', heuresFonctionnement: 1650 },
  { id: 'T16', modele: 'TWIST', annee: 2023, statut: 'En service', derniereMaintenance: '2025-07-05', prochaineMaintenance: '2025-09-05', heuresFonctionnement: 1200 },
  { id: 'T17', modele: 'TWIST', annee: 2023, statut: 'En service', derniereMaintenance: '2025-07-10', prochaineMaintenance: '2025-09-10', heuresFonctionnement: 980 }

];

// Fonction pour formater la date
function formatDate(dateString) {
  const options = { year: 'numeric', month: '2-digit', day: '2-digit' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
}

// Fonction pour obtenir la classe CSS en fonction du statut
function getStatusClass(statut) {
  switch (statut.toLowerCase()) {
    case 'en service':
      return 'badge bg-success';
    case 'maintenance':
      return 'badge bg-warning text-dark';
    case 'hors service':
      return 'badge bg-danger';
    default:
      return 'badge bg-secondary';
  }
}

// Fonction pour afficher les machines
function displayMachines() {
  const tbody = document.getElementById('machineInfoBody');
  tbody.innerHTML = ''; // Vider le tableau

  machinesData.forEach(machine => {
    const tr = document.createElement('tr');

    // Définir la classe de la ligne en fonction du statut
    if (machine.statut === 'Hors service') {
      tr.classList.add('table-danger');
    } else if (machine.statut === 'Maintenance') {
      tr.classList.add('table-warning');
    }

    tr.innerHTML = `
                    <td><strong>${machine.id}</strong></td>
                    <td>${machine.modele}</td>
                    <td>${machine.annee}</td>
                    <td><span class="${getStatusClass(machine.statut)}">${machine.statut}</span></td>
                    <td>${formatDate(machine.derniereMaintenance)}</td>
                    <td>${formatDate(machine.prochaineMaintenance)}</td>
                    <td>${machine.heuresFonctionnement.toLocaleString()} h</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" title="Voir les détails">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-warning me-1" title="Planifier une maintenance">
                            <i class="fas fa-tools"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" title="Historique">
                            <i class="fas fa-history"></i>
                        </button>
                    </td>
                `;
    tbody.appendChild(tr);
  });
}

// Données de production par heure (exemple)
const hourlyProductionData = {
  today: {
    labels: Array.from({ length: 24 }, (_, i) => `${i}:00`),
    actual: Array.from({ length: 24 }, () => Math.floor(Math.random() * 100) + 50),
    target: Array(24).fill(80)
  },
  yesterday: {
    labels: Array.from({ length: 24 }, (_, i) => `${i}:00`),
    actual: Array.from({ length: 24 }, () => Math.floor(Math.random() * 90) + 40),
    target: Array(24).fill(80)
  },
  week: {
    labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
    actual: [550, 620, 580, 600, 650, 700, 450],
    target: Array(7).fill(600)
  }
};

// Initialisation du graphique de production par heure
let hourlyProductionChart;

function initHourlyProductionChart(period = 'today') {
  const ctx = document.getElementById('hourlyProductionChart').getContext('2d');
  const data = hourlyProductionData[period];

  // Détruire le graphique existant s'il existe
  if (hourlyProductionChart) {
    hourlyProductionChart.destroy();
  }

  // Créer un nouveau graphique
  hourlyProductionChart = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: data.labels,
      datasets: [
        {
          label: 'Production Réelle',
          data: data.actual,
          backgroundColor: 'rgba(78, 115, 223, 0.8)',
          borderColor: 'rgba(78, 115, 223, 1)',
          borderWidth: 1,
          borderRadius: 4,
          yAxisID: 'y',
          barPercentage: 0.8,
          categoryPercentage: 0.8
        },
        {
          label: 'Objectif',
          data: data.target,
          type: 'line',
          borderColor: 'rgba(220, 53, 69, 0.8)',
          borderWidth: 2,
          borderDash: [5, 5],
          fill: false,
          pointRadius: 0,
          yAxisID: 'y'
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        mode: 'index',
        intersect: false
      },
      scales: {
        y: {
          beginAtZero: true,
          type: 'linear',
          display: true,
          position: 'left',
          title: {
            display: true,
            text: 'Quantité produite'
          },
          grid: {
            drawOnChartArea: true,
            drawBorder: false
          }
        },
        x: {
          grid: {
            display: false
          },
          title: {
            display: true,
            text: period === 'week' ? 'Jours de la Semaine' : 'Heures de la Journée'
          }
        }
      },
      plugins: {
        legend: {
          display: true,
          position: 'top',
          labels: {
            usePointStyle: true,
            boxWidth: 6
          }
        },
        tooltip: {
          mode: 'index',
          intersect: false,
          callbacks: {
            label: function (context) {
              return `${context.dataset.label}: ${context.parsed.y} unités`;
            }
          }
        }
      },
      animation: {
        duration: 1000,
        easing: 'easeInOutQuart'
      }
    }
  });
}

// Gestion des boutons de période
document.querySelectorAll('[data-period]').forEach(button => {
  button.addEventListener('click', function () {
    // Mettre à jour l'état actif des boutons
    document.querySelectorAll('[data-period]').forEach(btn => {
      btn.classList.remove('active');
    });
    this.classList.add('active');

    // Mettre à jour le graphique avec la période sélectionnée
    initHourlyProductionChart(this.dataset.period);
  });
});

// Appeler les fonctions d'initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function () {
  displayMachines();
  initHourlyProductionChart('today');

  // Initialiser les tooltips
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });

  // Activer les liens de navigation
  const dashboardLink = document.getElementById('dashboard-link');
  const reportsLink = document.getElementById('reports-link');
  const dashboardSection = document.querySelector('.dashboard');
  const reportsSection = document.getElementById('reportModal');

  dashboardLink.addEventListener('click', function (e) {
    e.preventDefault();
    dashboardSection.style.display = 'block';
    new bootstrap.Modal(reportsSection).hide();
  });

  reportsLink.addEventListener('click', function (e) {
    e.preventDefault();
    dashboardSection.style.display = 'none';
    new bootstrap.Modal(reportsSection).show();
  });

  // Charger les données et mettre à jour le tableau
  loadAndDisplayData();

  // Activer le bouton de génération de rapport
  const generateReportBtn = document.getElementById('generateReportBtn');
  generateReportBtn.addEventListener('click', function () {
    const reportType = document.getElementById('reportType').value;
    const reportFormat = document.getElementById('reportFormat').value;
    const startDate = document.getElementById('reportStartDate').value;
    const endDate = document.getElementById('reportEndDate').value;
    const machines = Array.from(document.getElementById('reportMachines').selectedOptions).map(option => option.value);
    const includeCharts = document.getElementById('includeCharts').checked;

    alert(`Rapport généré:\nType: ${reportType}\nFormat: ${reportFormat}\nDate de début: ${startDate}\nDate de fin: ${endDate}\nMachines: ${machines.join(', ')}\nInclure les graphiques: ${includeCharts}`);
  });
});

// Charger les données et mettre à jour le tableau
async function loadAndDisplayData() {
  try {
    const response = await fetch('js/data.json');
    const result = await response.json();
    const data = result.data;

    // Calculer les moyennes
    const averages = {
      output: data.reduce((sum, item) => sum + (item.output || 0), 0) / data.length,
      uptime: data.reduce((sum, item) => sum + (item.uptime || 0), 0) / data.length,
      unplanned_dt: data.reduce((sum, item) => sum + (item.unplanned_dt || 0), 0) / data.length,
      planned_dt: data.reduce((sum, item) => sum + (item.planned_dt || 0), 0) / data.length,
      setup_time: data.reduce((sum, item) => sum + (item.setup_time || 0), 0) / data.length,
      pmh: data.reduce((sum, item) => sum + (item.pmh || 0), 0) / data.length,
      ftq: data.reduce((sum, item) => sum + (item.ftq || 0), 0) / data.length,
      throughput: data.reduce((sum, item) => sum + (item.throughput || 0), 0) / data.length,
      avg_target_setup: data.reduce((sum, item) => sum + (item.avg_target_setup || 0), 0) / data.length,
      compliance_setup: data.reduce((sum, item) => sum + (item.compliance_setup || 0), 0) / data.length,
    };

    // Mettre à jour le tableau
    const tableBody = document.querySelector('.table-bordered tbody');
    const newRow = `
                    <tr>
                        <td><strong>Averages</strong></td>
                        <td><span class="status-badge bg-info"></span> ${averages.output.toFixed(2)}</td>
                        <td><span class="status-badge bg-info"></span> ${averages.uptime.toFixed(2)}%</td>
                        <td><span class="status-badge bg-info"></span> ${averages.unplanned_dt.toFixed(2)}%</td>
                        <td><span class="status-badge bg-info"></span> ${averages.planned_dt.toFixed(2)}%</td>
                        <td><span class="status-badge bg-info"></span> ${averages.setup_time.toFixed(2)}%</td>
                        <td>00:00:00</td>
                        <td>${averages.pmh.toFixed(2)}</td>
                        <td>${averages.ftq.toFixed(2)}%</td>
                        <td>${averages.throughput.toFixed(2)}</td>
                        <td>00:00:00</td>
                        <td>${averages.compliance_setup.toFixed(2)}%</td>
                    </tr>
                `;
    tableBody.innerHTML += newRow;

  } catch (error) {
    console.error('Error loading or processing data:', error);
  }
}

// ===== BARCODE SCANNER FUNCTIONS =====

function initBarcodeScanner() {
  const scannerInput = document.getElementById('scannerInput');
  if (scannerInput) {
    // Auto-focus on scanner input
    scannerInput.focus();

    // Handle Enter key press
    scannerInput.addEventListener('keypress', function (e) {
      if (e.key === 'Enter') {
        searchBarcode();
      }
    });

    // Auto-search after a short delay when typing stops
    let searchTimeout;
    scannerInput.addEventListener('input', function () {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        if (scannerInput.value.length > 3) {
          searchBarcode();
        }
      }, 500);
    });
  }

  // Load recent scans on page load
  displayRecentScans();
}

async function searchBarcode() {
  const scannerInput = document.getElementById('scannerInput');
  const resultDiv = document.getElementById('scannerResult');

  if (!scannerInput || !resultDiv) return;

  const code = scannerInput.value.trim();
  if (!code) {
    resultDiv.style.display = 'none';
    return;
  }

  // Show loading state
  resultDiv.innerHTML = `
    <div class="text-center p-3">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Recherche en cours...</span>
      </div>
      <p class="mt-2">Recherche du code: <strong>${code}</strong></p>
    </div>
  `;
  resultDiv.style.display = 'block';

  try {
    // Search in the database
    const result = await searchBarcode(code);

    if (result) {
      // Record the scan
      await recordBarcodeScan(code, 'Current User', 'Search', 'Web Scanner');

      // Display the result
      displayBarcodeResult(result);

      // Update recent scans
      displayRecentScans();
    } else {
      // No result found
      resultDiv.innerHTML = `
        <div class="alert alert-warning">
          <h6><i class="fas fa-exclamation-triangle me-2"></i>Code non trouvé</h6>
          <p class="mb-0">Le code <strong>${code}</strong> n'a pas été trouvé dans la base de données.</p>
          <small class="text-muted">Vérifiez que le code est correct ou contactez l'administrateur.</small>
        </div>
      `;
    }
  } catch (error) {
    console.error('Erreur lors de la recherche:', error);
    resultDiv.innerHTML = `
      <div class="alert alert-danger">
        <h6><i class="fas fa-times me-2"></i>Erreur de recherche</h6>
        <p class="mb-0">Une erreur s'est produite lors de la recherche.</p>
        <small class="text-muted">${error.message}</small>
      </div>
    `;
  }
}

function displayBarcodeResult(result) {
  const resultDiv = document.getElementById('scannerResult');
  if (!resultDiv) return;

  // Determine the section type for styling
  const sectionInfo = getSectionInfo(result.section);

  // Format the result display
  let html = `
    <div class="alert alert-success">
      <div class="d-flex justify-content-between align-items-start">
        <div>
          <h6><i class="${sectionInfo.icon} me-2"></i>${result.name || result.code}</h6>
          <p class="mb-1"><strong>Code:</strong> ${result.code}</p>
          <p class="mb-1"><strong>Format:</strong> <span class="badge bg-secondary">${result.format}</span></p>
          <p class="mb-1"><strong>Catégorie:</strong> ${result.category || 'N/A'}</p>
          <p class="mb-1"><strong>Section:</strong> <span class="badge ${sectionInfo.badgeClass}">${sectionInfo.name}</span></p>
        </div>
        <div class="text-end">
          <small class="text-muted">Trouvé dans ${sectionInfo.name}</small>
        </div>
      </div>
    </div>

    <div class="card mt-3">
      <div class="card-header">
        <h6 class="mb-0">Détails du produit</h6>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            ${result.description ? `<p><strong>Description:</strong><br>${result.description}</p>` : ''}
            ${result.supplier ? `<p><strong>Fournisseur:</strong> ${result.supplier}</p>` : ''}
            ${result.location ? `<p><strong>Emplacement:</strong> ${result.location}</p>` : ''}
            ${result.machine ? `<p><strong>Machine:</strong> ${result.machine}</p>` : ''}
          </div>
          <div class="col-md-6">
            ${result.price ? `<p><strong>Prix:</strong> ${result.price}€</p>` : ''}
            ${result.stock ? `<p><strong>Stock:</strong> ${result.stock}</p>` : ''}
            ${result.minStock ? `<p><strong>Stock minimum:</strong> ${result.minStock}</p>` : ''}
            ${result.order ? `<p><strong>Ordre:</strong> ${result.order}</p>` : ''}
            ${result.remaining ? `<p><strong>Reste:</strong> ${result.remaining}</p>` : ''}
            ${result.status ? `<p><strong>Statut:</strong> <span class="badge ${getStatusBadgeClass(result.status)}">${result.status}</span></p>` : ''}
          </div>
        </div>

        ${result.availableActions ? `
          <div class="mt-3">
            <strong>Actions disponibles:</strong>
            <div class="mt-2">
              ${result.availableActions.split(' ').map(action =>
    `<button class="btn btn-outline-primary btn-sm me-2" onclick="performAction('${result.code}', '${action}')">${action}</button>`
  ).join('')}
            </div>
          </div>
        ` : ''}

        <!-- Code-Barres Visuel -->
        <div class="mt-4">
          <h6><i class="fas fa-barcode me-2"></i>Code-Barres</h6>
          <div class="barcode-container bg-white border rounded p-3 text-center">
            <div class="barcode-visual mb-2">
              ${generateBarcodeVisual(result.code, result.format)}
            </div>
            <div class="barcode-text">
              <strong>${result.code}</strong>
            </div>
            <small class="text-muted">Format: ${result.format}</small>
          </div>
        </div>

        <div class="mt-3">
          <small class="text-muted">
            Dernière mise à jour: ${result.lastUpdated ? new Date(result.lastUpdated).toLocaleString('fr-FR') : 'N/A'}
          </small>
        </div>
      </div>
    </div>
  `;

  resultDiv.innerHTML = html;
}

function getSectionInfo(section) {
  const sectionMap = {
    'aptivProducts': { name: 'Produits Aptiv', icon: 'fas fa-industry', badgeClass: 'bg-primary' },
    'standardProducts': { name: 'Produits Standards', icon: 'fas fa-barcode', badgeClass: 'bg-info' },
    'upcProducts': { name: 'Produits UPC', icon: 'fas fa-globe-americas', badgeClass: 'bg-warning' },
    'internalCodes': { name: 'Codes Internes', icon: 'fas fa-cogs', badgeClass: 'bg-success' },
    'packagingCodes': { name: 'Codes Emballage', icon: 'fas fa-box', badgeClass: 'bg-secondary' },
    'testData': { name: 'Données Test', icon: 'fas fa-flask', badgeClass: 'bg-danger' },
    'dataTwist': { name: 'Data Twist Production', icon: 'fas fa-sync-alt', badgeClass: 'bg-warning text-dark' }
  };

  return sectionMap[section] || { name: 'Inconnu', icon: 'fas fa-question', badgeClass: 'bg-dark' };
}

// Fonction pour générer une représentation visuelle du code-barres
function generateBarcodeVisual(code, format) {
  // Générer un pattern de barres basé sur le code
  const barsPattern = generateBarsPattern(code, format);

  return `
    <div class="barcode-bars d-flex justify-content-center align-items-end" style="height: 60px; gap: 1px;">
      ${barsPattern}
    </div>
  `;
}

function generateBarsPattern(code, format) {
  // Patterns de base pour différents formats
  const formatPatterns = {
    'CODE128': generateCode128Pattern(code),
    'EAN13': generateEAN13Pattern(code),
    'UPC': generateUPCPattern(code),
    'CODE39': generateCode39Pattern(code),
    'ITF14': generateITF14Pattern(code)
  };

  return formatPatterns[format] || generateCode128Pattern(code);
}

function generateCode128Pattern(code) {
  let pattern = '';

  // Start pattern
  pattern += '<div class="bar" style="width: 2px; height: 100%; background: #000;"></div>';
  pattern += '<div class="space" style="width: 1px; height: 100%;"></div>';
  pattern += '<div class="bar" style="width: 1px; height: 100%; background: #000;"></div>';
  pattern += '<div class="space" style="width: 1px; height: 100%;"></div>';

  // Generate bars based on code characters
  for (let i = 0; i < code.length; i++) {
    const char = code.charCodeAt(i);
    const barWidth = (char % 3) + 1; // Width between 1-3px
    const barHeight = 80 + (char % 20); // Height variation

    // Bar
    pattern += `<div class="bar" style="width: ${barWidth}px; height: ${barHeight}%; background: #000;"></div>`;

    // Space (except for last character)
    if (i < code.length - 1) {
      const spaceWidth = (char % 2) + 1;
      pattern += `<div class="space" style="width: ${spaceWidth}px; height: 100%;"></div>`;
    }
  }

  // End pattern
  pattern += '<div class="space" style="width: 1px; height: 100%;"></div>';
  pattern += '<div class="bar" style="width: 1px; height: 100%; background: #000;"></div>';
  pattern += '<div class="space" style="width: 1px; height: 100%;"></div>';
  pattern += '<div class="bar" style="width: 2px; height: 100%; background: #000;"></div>';

  return pattern;
}

function generateEAN13Pattern(code) {
  let pattern = '';

  // Start guard
  pattern += '<div class="bar" style="width: 1px; height: 100%; background: #000;"></div>';
  pattern += '<div class="space" style="width: 1px; height: 100%;"></div>';
  pattern += '<div class="bar" style="width: 1px; height: 100%; background: #000;"></div>';

  // Generate pattern for EAN13
  for (let i = 0; i < Math.min(code.length, 13); i++) {
    const digit = parseInt(code[i]) || 0;
    const barWidth = (digit % 3) + 1;
    const barHeight = 85 + (digit % 15);

    pattern += `<div class="bar" style="width: ${barWidth}px; height: ${barHeight}%; background: #000;"></div>`;
    pattern += `<div class="space" style="width: 1px; height: 100%;"></div>`;

    // Center guard after 6 digits
    if (i === 5) {
      pattern += '<div class="space" style="width: 2px; height: 100%;"></div>';
      pattern += '<div class="bar" style="width: 1px; height: 100%; background: #000;"></div>';
      pattern += '<div class="space" style="width: 1px; height: 100%;"></div>';
      pattern += '<div class="bar" style="width: 1px; height: 100%; background: #000;"></div>';
      pattern += '<div class="space" style="width: 2px; height: 100%;"></div>';
    }
  }

  // End guard
  pattern += '<div class="bar" style="width: 1px; height: 100%; background: #000;"></div>';
  pattern += '<div class="space" style="width: 1px; height: 100%;"></div>';
  pattern += '<div class="bar" style="width: 1px; height: 100%; background: #000;"></div>';

  return pattern;
}

function generateUPCPattern(code) {
  // Similar to EAN13 but with UPC-specific pattern
  return generateEAN13Pattern(code);
}

function generateCode39Pattern(code) {
  let pattern = '';

  // Start/stop character (*)
  pattern += '<div class="bar" style="width: 2px; height: 100%; background: #000;"></div>';
  pattern += '<div class="space" style="width: 1px; height: 100%;"></div>';

  for (let i = 0; i < code.length; i++) {
    const char = code.charCodeAt(i);

    // 9 elements per character (5 bars, 4 spaces)
    for (let j = 0; j < 5; j++) {
      const barWidth = ((char + j) % 3) + 1;
      const barHeight = 80 + ((char + j) % 20);

      pattern += `<div class="bar" style="width: ${barWidth}px; height: ${barHeight}%; background: #000;"></div>`;

      if (j < 4) {
        pattern += `<div class="space" style="width: 1px; height: 100%;"></div>`;
      }
    }

    // Inter-character space
    if (i < code.length - 1) {
      pattern += '<div class="space" style="width: 2px; height: 100%;"></div>';
    }
  }

  // End character (*)
  pattern += '<div class="space" style="width: 1px; height: 100%;"></div>';
  pattern += '<div class="bar" style="width: 2px; height: 100%; background: #000;"></div>';

  return pattern;
}

function generateITF14Pattern(code) {
  let pattern = '';

  // Start pattern
  pattern += '<div class="bar" style="width: 2px; height: 100%; background: #000;"></div>';
  pattern += '<div class="space" style="width: 2px; height: 100%;"></div>';
  pattern += '<div class="bar" style="width: 1px; height: 100%; background: #000;"></div>';
  pattern += '<div class="space" style="width: 1px; height: 100%;"></div>';

  // Generate pairs of digits
  for (let i = 0; i < Math.min(code.length, 14); i += 2) {
    const digit1 = parseInt(code[i]) || 0;
    const digit2 = parseInt(code[i + 1]) || 0;

    // Wide and narrow bars/spaces based on digits
    const barWidth1 = (digit1 % 2) ? 3 : 1;
    const barWidth2 = (digit2 % 2) ? 3 : 1;
    const barHeight = 85 + ((digit1 + digit2) % 15);

    pattern += `<div class="bar" style="width: ${barWidth1}px; height: ${barHeight}%; background: #000;"></div>`;
    pattern += `<div class="space" style="width: ${barWidth2}px; height: 100%;"></div>`;
  }

  // End pattern
  pattern += '<div class="bar" style="width: 1px; height: 100%; background: #000;"></div>';
  pattern += '<div class="space" style="width: 1px; height: 100%;"></div>';
  pattern += '<div class="bar" style="width: 2px; height: 100%; background: #000;"></div>';
  pattern += '<div class="space" style="width: 2px; height: 100%;"></div>';

  return pattern;
}

// Fonction pour générer un mini code-barres pour les scans récents
function generateMiniBarcodeVisual(code, format) {
  const miniBarsPattern = generateMiniBarsPattern(code, format);

  return `
    <div class="mini-barcode-bars d-flex justify-content-center align-items-end" style="height: 30px; gap: 0.5px;">
      ${miniBarsPattern}
    </div>
  `;
}

function generateMiniBarsPattern(code, format) {
  // Version simplifiée pour les mini codes-barres
  let pattern = '';

  // Start pattern (simplifié)
  pattern += '<div class="mini-bar" style="width: 1px; height: 100%; background: #000;"></div>';
  pattern += '<div class="mini-space" style="width: 0.5px; height: 100%;"></div>';

  // Generate simplified bars based on code characters (moins de barres pour l'affichage mini)
  for (let i = 0; i < Math.min(code.length, 8); i++) { // Limiter à 8 caractères pour l'affichage mini
    const char = code.charCodeAt(i);
    const barWidth = (char % 2) + 0.5; // Width between 0.5-1.5px
    const barHeight = 70 + (char % 30); // Height variation

    // Bar
    pattern += `<div class="mini-bar" style="width: ${barWidth}px; height: ${barHeight}%; background: #000;"></div>`;

    // Space (except for last character)
    if (i < Math.min(code.length, 8) - 1) {
      pattern += `<div class="mini-space" style="width: 0.5px; height: 100%;"></div>`;
    }
  }

  // End pattern (simplifié)
  pattern += '<div class="mini-space" style="width: 0.5px; height: 100%;"></div>';
  pattern += '<div class="mini-bar" style="width: 1px; height: 100%; background: #000;"></div>';

  return pattern;
}

// Fonction pour déterminer le format d'un code
function getCodeFormat(code) {
  if (!code) return 'CODE128';

  // Détection basée sur le pattern du code
  if (code.startsWith('EK9-')) return 'CODE128';
  if (/^\d{13}$/.test(code)) return 'EAN13';
  if (/^\d{12}$/.test(code)) return 'UPC';
  if (/^[A-Z0-9\-\.\s\$\/\+\%\*]+$/.test(code)) return 'CODE39';
  if (/^\d{14}$/.test(code)) return 'ITF14';

  return 'CODE128'; // Par défaut
}

// Fonction pour afficher les détails rapides d'un code dans les scans récents
async function showMiniCodeDetails(code) {
  try {
    const result = await searchBarcode(code);

    if (result) {
      // Afficher dans une alert simple avec les détails principaux
      alert(`Détails du code ${code}:\n\nNom: ${result.name || 'N/A'}\nFormat: ${result.format}\nCatégorie: ${result.category || 'N/A'}\nDescription: ${result.description ? result.description.substring(0, 100) + '...' : 'N/A'}`);
    } else {
      alert(`Code ${code} non trouvé dans la base de données.`);
    }
  } catch (error) {
    console.error('Erreur lors de la récupération des détails:', error);
    alert('Erreur lors de la récupération des détails du code.');
  }
}

function getStatusBadgeClass(status) {
  const statusMap = {
    'Active': 'bg-success',
    'Pending': 'bg-warning',
    'Completed': 'bg-info',
    'Failed': 'bg-danger',
    'Scanned': 'bg-primary',
    'Processing': 'bg-warning',
    'Dispatched': 'bg-info',
    'Operational': 'bg-success',
    'Maintenance Required': 'bg-warning',
    'Certified': 'bg-success',
    'Rejected': 'bg-danger'
  };

  return statusMap[status] || 'bg-secondary';
}

async function displayRecentScans() {
  const recentScansDiv = document.getElementById('recentScans');
  if (!recentScansDiv) return;

  // Get local scan history
  const localScans = getScanHistory().slice(-3).reverse(); // Get last 3 local scans

  // Get Data twist codes to display as recent scans
  const dataTwistScans = await getDataTwistRecentScans();

  // Combine local scans with Data twist scans
  const allScans = [...localScans, ...dataTwistScans];

  if (allScans.length === 0) {
    recentScansDiv.innerHTML = '<p class="text-muted">Aucun scan récent</p>';
    return;
  }

  const html = allScans.map(scan => `
    <div class="recent-scan-item border-bottom py-3">
      <div class="d-flex justify-content-between align-items-start">
        <div class="flex-grow-1">
          <div class="d-flex align-items-center mb-2">
            <strong class="me-2">${scan.code}</strong>
            ${scan.source ? `<small class="badge bg-info">${scan.source}</small>` : ''}
          </div>

          <!-- Mini Code-Barres -->
          <div class="mini-barcode-container mb-2">
            <div class="mini-barcode-visual">
              ${generateMiniBarcodeVisual(scan.code, getCodeFormat(scan.code))}
            </div>
          </div>

          <small class="text-muted">${scan.action} - ${new Date(scan.timestamp).toLocaleString('fr-FR')}</small>
        </div>

        <div class="d-flex flex-column gap-1">
          <button class="btn btn-sm btn-outline-primary" onclick="searchSpecificCode('${scan.code}')" title="Scanner ce code">
            <i class="fas fa-search"></i>
          </button>
          <button class="btn btn-sm btn-outline-secondary" onclick="showMiniCodeDetails('${scan.code}')" title="Voir détails">
            <i class="fas fa-info"></i>
          </button>
        </div>
      </div>
    </div>
  `).join('');

  recentScansDiv.innerHTML = html;
}

async function getDataTwistRecentScans() {
  try {
    // Load CSV data to get Data twist codes
    const csvData = await loadBarcodeData();

    // Filter for Data twist codes (those that start with EK9- and are in production category)
    const dataTwistCodes = csvData.filter(item =>
      item.Code && item.Code.startsWith('EK9-') &&
      (item.Category === 'HAB' || item.Category === 'PPL' || item.Category === 'TAB' || item.Category === 'Production')
    ).slice(0, 7); // Get first 7 codes

    // Create fake recent scans from Data twist data
    const baseTime = Date.now() - (24 * 60 * 60 * 1000); // 24 hours ago

    return dataTwistCodes.map((item, index) => ({
      code: item.Code,
      action: 'Production Scan',
      timestamp: new Date(baseTime + (index * 2 * 60 * 60 * 1000)).toISOString(), // 2 hours apart
      user: 'Production System',
      location: item.Location || 'Production Line',
      source: 'Data Twist',
      result: 'Found'
    }));

  } catch (error) {
    console.error('Erreur lors du chargement des scans Data twist:', error);
    return [];
  }
}

function searchSpecificCode(code) {
  const scannerInput = document.getElementById('scannerInput');
  if (scannerInput) {
    scannerInput.value = code;
    searchBarcode();
  }
}

function performAction(code, action) {
  // This would typically send a request to the server
  console.log(`Performing action "${action}" on code "${code}"`);

  // Show a confirmation message
  const alertDiv = document.createElement('div');
  alertDiv.className = 'alert alert-info alert-dismissible fade show mt-3';
  alertDiv.innerHTML = `
    <strong>Action effectuée:</strong> ${action} sur le code ${code}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;

  const resultDiv = document.getElementById('scannerResult');
  if (resultDiv) {
    resultDiv.appendChild(alertDiv);
  }

  // Record the action
  recordBarcodeScan(code, 'Current User', action, 'Web Scanner');

  // Refresh recent scans to show the new action
  displayRecentScans();
}

// Function to refresh Data twist scans periodically
function refreshDataTwistScans() {
  displayRecentScans();
}

// Auto-refresh recent scans every 30 seconds to simulate real-time updates
setInterval(refreshDataTwistScans, 30000);



// Fonction pour afficher le résultat d'un scan unicos
function displayUnicosResult(result) {
  const resultDiv = document.getElementById('unicosResult');

  // Afficher d'abord le format T24 SETUP
  displayT24SetupFormat(result);

  // Puis afficher les détails complets
  const html = `
    <div class="alert alert-success mt-3">
      <div class="d-flex justify-content-between align-items-start">
        <div>
          <h6><i class="fas fa-check-circle me-2"></i>Code unicos trouvé!</h6>
          <span class="badge bg-warning text-dark">
            <i class="fas fa-sync-alt me-1"></i> Data Twist Production
          </span>
        </div>
        <div class="d-flex gap-2">
          <button class="btn btn-outline-success btn-sm" onclick="performAction('${result.code}', 'Valider')" title="Valider">
            <i class="fas fa-check me-1"></i> Valider
          </button>
          <button class="btn btn-outline-warning btn-sm" onclick="performAction('${result.code}', 'Réviser')" title="Réviser">
            <i class="fas fa-edit me-1"></i> Réviser
          </button>
          <button class="btn btn-outline-danger btn-sm" onclick="performAction('${result.code}', 'Annuler')" title="Annuler">
            <i class="fas fa-times me-1"></i> Annuler
          </button>
        </div>
      </div>
    </div>

    <div class="card mt-3">
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <h6><i class="fas fa-info-circle me-2"></i>Informations Générales</h6>
            <p><strong>Code Unicos:</strong> <span class="text-primary">${result.code}</span></p>
            <p><strong>Nom:</strong> ${result.name}</p>
            <p><strong>Catégorie:</strong> <span class="badge bg-info">${result.category}</span></p>
            <p><strong>Format:</strong> ${result.format}</p>
            <p><strong>Statut:</strong> <span class="badge ${getStatusClass(result.status)}">${result.status}</span></p>
          </div>
          <div class="col-md-6">
            <h6><i class="fas fa-cogs me-2"></i>Données de Production</h6>
            <p><strong>Machine:</strong> <span class="text-info">${result.machine || 'N/A'}</span></p>
            <p><strong>Ordre:</strong> ${result.order || 'N/A'}</p>
            <p><strong>Reste:</strong> ${result.remaining || 'N/A'}</p>
            <p><strong>Scanné:</strong> <span class="badge ${result.scanned === 'Oui' ? 'bg-success' : 'bg-secondary'}">${result.scanned}</span></p>
            <p><strong>Emplacement:</strong> ${result.location || 'N/A'}</p>
          </div>
        </div>

        <hr>

        <div class="row">
          <div class="col-md-12">
            <h6><i class="fas fa-file-alt me-2"></i>Description</h6>
            <p class="text-muted">${result.description}</p>

            <h6><i class="fas fa-tasks me-2"></i>Actions Disponibles</h6>
            <p><span class="badge bg-light text-dark">${result.actions}</span></p>
          </div>
        </div>

        <!-- Code-Barres Unicos Proéminent -->
        <div class="mt-4">
          <div class="card border-warning">
            <div class="card-header bg-warning text-dark text-center">
              <h5 class="mb-0">
                <i class="fas fa-barcode me-2"></i>
                Code-Barres Unicos - ${result.code}
              </h5>
            </div>
            <div class="card-body">
              <div class="barcode-container bg-white border rounded p-4 text-center">
                <div class="barcode-visual mb-3" style="min-height: 80px;">
                  ${generateBarcodeVisual(result.code, result.format)}
                </div>
                <div class="barcode-text mb-2">
                  <h4 class="text-primary font-monospace">${result.code}</h4>
                </div>
                <div class="barcode-info">
                  <span class="badge bg-warning text-dark me-2">Format: ${result.format}</span>
                  <span class="badge bg-info text-white me-2">Data Twist</span>
                  <span class="badge bg-success text-white">Unicos</span>
                </div>

                <!-- Actions rapides sur le code-barres -->
                <div class="mt-3">
                  <button class="btn btn-outline-primary btn-sm me-2" onclick="copyToClipboard('${result.code}')" title="Copier le code">
                    <i class="fas fa-copy me-1"></i> Copier
                  </button>
                  <button class="btn btn-outline-secondary btn-sm me-2" onclick="printBarcode('${result.code}')" title="Imprimer code-barres">
                    <i class="fas fa-print me-1"></i> Imprimer
                  </button>
                  <button class="btn btn-outline-info btn-sm" onclick="enlargeBarcode('${result.code}')" title="Agrandir">
                    <i class="fas fa-expand me-1"></i> Agrandir
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;

  resultDiv.innerHTML += html;
}

// Fonction pour afficher le format T24 SETUP
function displayT24SetupFormat(result) {
  const resultDiv = document.getElementById('unicosResult');
  const currentDate = new Date();
  const dateStr = currentDate.toISOString().split('T')[0];
  const timeStr = currentDate.toTimeString().split(' ')[0].substring(0, 5);

  // Extraire les informations du code unicos
  const codeInfo = parseUnicosCode(result.code);

  const t24Html = `
    <div class="t24-setup-container">
      <div class="t24-header">
        <div class="twist-area-title">
          <h2>T w i s t</h2>
          <h2>A r e a</h2>
        </div>
        <div class="t24-datetime">
          ${dateStr} ${timeStr} | shift: MATIN
        </div>
        <div class="t24-setup-title">
          <h3><u>T24 SETUP</u></h3>
        </div>
      </div>

      <div class="t24-table">
        <table class="setup-table">
          <thead>
            <tr>
              <th>Unico</th>
              <th>Groupe</th>
              <th>Ordre</th>
              <th>Reste</th>
              <th>Date</th>
            </tr>
          </thead>
          <tbody>
            <tr class="scanned-row">
              <td class="unico-code">${result.code}</td>
              <td class="groupe">${codeInfo.groupe}</td>
              <td class="ordre">${result.order || '400'}</td>
              <td class="reste">${result.remaining || '400'}</td>
              <td class="date-time">
                ${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}<br>
                ${timeStr}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="t24-actions">
        <button class="btn btn-success me-2" onclick="printT24Setup('${result.code}')">
          <i class="fas fa-print me-1"></i> Imprimer T24
        </button>
        <button class="btn btn-info me-2" onclick="exportT24Setup('${result.code}')">
          <i class="fas fa-download me-1"></i> Exporter
        </button>
        <button class="btn btn-warning" onclick="addToT24Setup('${result.code}')">
          <i class="fas fa-plus me-1"></i> Ajouter au Setup
        </button>
      </div>
    </div>
  `;

  resultDiv.innerHTML = t24Html;
}

// Fonction pour parser un code unicos et extraire les informations
function parseUnicosCode(code) {
  // Format: EK9-XXX-YYYY où XXX est le groupe et YYYY le type
  const parts = code.split('-');

  return {
    prefix: parts[0] || 'EK9',
    groupe: parts[1] || 'TWIST',
    type: parts[2] || 'SETUP',
    fullCode: code
  };
}

// Fonction pour imprimer le format T24 SETUP
function printT24Setup(code) {
  try {
    const printContent = generateT24PrintContent(code);

    const printWindow = window.open('', '_blank', 'width=800,height=600');

    if (!printWindow) {
      showMessage('Impossible d\'ouvrir la fenêtre d\'impression.', 'warning');
      return;
    }

    printWindow.document.write(printContent);
    printWindow.document.close();

    printWindow.onload = function () {
      printWindow.focus();
      printWindow.print();
      setTimeout(() => {
        printWindow.close();
      }, 1000);
    };

    recordBarcodeScan(code, 'Current User', 'Imprimer T24', 'T24 Setup');
    showMessage(`T24 SETUP pour ${code} envoyé à l'impression!`, 'success');

  } catch (error) {
    console.error('Erreur lors de l\'impression T24:', error);
    showMessage('Erreur lors de l\'impression T24.', 'danger');
  }
}

// Fonction pour générer le contenu d'impression T24
function generateT24PrintContent(code) {
  const currentDate = new Date();
  const dateStr = currentDate.toISOString().split('T')[0];
  const timeStr = currentDate.toTimeString().split(' ')[0].substring(0, 5);
  const codeInfo = parseUnicosCode(code);

  return `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>T24 SETUP - ${code}</title>
      <style>
        body {
          font-family: 'Courier New', monospace;
          margin: 20px;
          background-color: #f5f5f5;
          color: #333;
        }
        .t24-container {
          background-color: white;
          padding: 30px;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .twist-title {
          text-align: center;
          font-size: 36px;
          font-weight: bold;
          letter-spacing: 8px;
          margin-bottom: 10px;
        }
        .area-title {
          text-align: center;
          font-size: 32px;
          font-weight: bold;
          letter-spacing: 6px;
          margin-bottom: 30px;
        }
        .datetime-info {
          text-align: center;
          font-size: 16px;
          margin-bottom: 30px;
        }
        .setup-title {
          text-align: center;
          font-size: 24px;
          font-weight: bold;
          text-decoration: underline;
          margin-bottom: 40px;
        }
        .setup-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;
        }
        .setup-table th, .setup-table td {
          border: 2px solid #333;
          padding: 12px;
          text-align: center;
          font-size: 14px;
        }
        .setup-table th {
          background-color: #f0f0f0;
          font-weight: bold;
        }
        .unico-code {
          font-weight: bold;
          font-size: 16px;
        }
        .footer-info {
          text-align: center;
          font-size: 12px;
          color: #666;
          margin-top: 30px;
        }
        @media print {
          body { margin: 0; background-color: white; }
          .t24-container { box-shadow: none; }
        }
      </style>
    </head>
    <body>
      <div class="t24-container">
        <div class="twist-title">T w i s t</div>
        <div class="area-title">A r e a</div>

        <div class="datetime-info">
          ${dateStr} ${timeStr} | shift: MATIN
        </div>

        <div class="setup-title">T24 SETUP</div>

        <table class="setup-table">
          <thead>
            <tr>
              <th>Unico</th>
              <th>Groupe</th>
              <th>Ordre</th>
              <th>Reste</th>
              <th>Date</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="unico-code">${code}</td>
              <td>${codeInfo.groupe}</td>
              <td>400</td>
              <td>400</td>
              <td>
                ${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}<br>
                ${timeStr}
              </td>
            </tr>
          </tbody>
        </table>

        <div class="footer-info">
          Document généré automatiquement - APTIV Twisting Monitoring Tool<br>
          ${new Date().toLocaleString('fr-FR')}
        </div>
      </div>
    </body>
    </html>
  `;
}

// Fonction pour exporter le T24 SETUP
function exportT24Setup(code) {
  try {
    const currentDate = new Date();
    const dateStr = currentDate.toISOString().split('T')[0];
    const timeStr = currentDate.toTimeString().split(' ')[0].substring(0, 5);
    const codeInfo = parseUnicosCode(code);

    const t24Data = {
      title: "T24 SETUP",
      area: "Twist Area",
      date: dateStr,
      time: timeStr,
      shift: "MATIN",
      entries: [
        {
          unico: code,
          groupe: codeInfo.groupe,
          ordre: "400",
          reste: "400",
          date: `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}`,
          time: timeStr
        }
      ],
      generated: new Date().toISOString(),
      version: "1.0"
    };

    const dataStr = JSON.stringify(t24Data, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `T24-SETUP-${code}-${dateStr}.json`;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    recordBarcodeScan(code, 'Current User', 'Export T24', 'T24 Setup');
    showMessage(`T24 SETUP pour ${code} exporté avec succès!`, 'success');

  } catch (error) {
    console.error('Erreur lors de l\'export T24:', error);
    showMessage('Erreur lors de l\'export T24.', 'danger');
  }
}

// Fonction pour ajouter au T24 Setup (simulation d'ajout à une liste)
function addToT24Setup(code) {
  try {
    // Récupérer la liste T24 existante ou créer une nouvelle
    let t24List = JSON.parse(localStorage.getItem('t24SetupList') || '[]');

    const currentDate = new Date();
    const codeInfo = parseUnicosCode(code);

    const newEntry = {
      id: Date.now(),
      unico: code,
      groupe: codeInfo.groupe,
      ordre: "400",
      reste: "400",
      date: currentDate.toISOString().split('T')[0],
      time: currentDate.toTimeString().split(' ')[0].substring(0, 5),
      shift: "MATIN",
      addedAt: new Date().toISOString(),
      status: "Active"
    };

    // Vérifier si le code existe déjà
    const existingIndex = t24List.findIndex(item => item.unico === code);

    if (existingIndex !== -1) {
      // Mettre à jour l'entrée existante
      t24List[existingIndex] = { ...t24List[existingIndex], ...newEntry };
      showMessage(`Code ${code} mis à jour dans le T24 Setup!`, 'info');
    } else {
      // Ajouter nouvelle entrée
      t24List.push(newEntry);
      showMessage(`Code ${code} ajouté au T24 Setup!`, 'success');
    }

    // Limiter à 50 entrées maximum
    if (t24List.length > 50) {
      t24List = t24List.slice(-50);
    }

    // Sauvegarder dans localStorage
    localStorage.setItem('t24SetupList', JSON.stringify(t24List));

    // Enregistrer l'action
    recordBarcodeScan(code, 'Current User', 'Ajout T24 Setup', 'T24 Setup');

    // Mettre à jour l'affichage si nécessaire
    updateT24SetupDisplay();

  } catch (error) {
    console.error('Erreur lors de l\'ajout au T24 Setup:', error);
    showMessage('Erreur lors de l\'ajout au T24 Setup.', 'danger');
  }
}

// Fonction pour mettre à jour l'affichage du T24 Setup
function updateT24SetupDisplay() {
  try {
    const t24List = JSON.parse(localStorage.getItem('t24SetupList') || '[]');

    // Si on a des entrées, on peut les afficher quelque part
    if (t24List.length > 0) {
      console.log('T24 Setup List:', t24List);

      // Optionnel: Afficher un badge avec le nombre d'entrées
      const setupBadge = document.querySelector('.t24-setup-badge');
      if (setupBadge) {
        setupBadge.textContent = t24List.length;
        setupBadge.style.display = t24List.length > 0 ? 'inline' : 'none';
      }
    }

  } catch (error) {
    console.error('Erreur lors de la mise à jour de l\'affichage T24:', error);
  }
}

// Fonction pour afficher la liste complète T24 Setup
function showT24SetupList() {
  try {
    const t24List = JSON.parse(localStorage.getItem('t24SetupList') || '[]');

    if (t24List.length === 0) {
      showMessage('Aucune entrée dans le T24 Setup.', 'info');
      return;
    }

    let tableRows = '';
    t24List.forEach((entry, index) => {
      tableRows += `
        <tr>
          <td>${entry.unico}</td>
          <td>${entry.groupe}</td>
          <td>${entry.ordre}</td>
          <td>${entry.reste}</td>
          <td>${entry.date}<br><small>${entry.time}</small></td>
          <td>
            <button class="btn btn-sm btn-outline-danger" onclick="removeFromT24Setup(${entry.id})">
              <i class="fas fa-trash"></i>
            </button>
          </td>
        </tr>
      `;
    });

    const modalContent = `
      <div class="modal fade" id="t24SetupListModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
              <h5 class="modal-title">
                <i class="fas fa-list me-2"></i>
                T24 SETUP - Liste Complète (${t24List.length} entrées)
              </h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <div class="table-responsive">
                <table class="table table-striped table-hover">
                  <thead class="table-dark">
                    <tr>
                      <th>Unico</th>
                      <th>Groupe</th>
                      <th>Ordre</th>
                      <th>Reste</th>
                      <th>Date/Heure</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${tableRows}
                  </tbody>
                </table>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-warning" onclick="exportFullT24Setup()">
                <i class="fas fa-download me-1"></i> Exporter Tout
              </button>
              <button type="button" class="btn btn-danger" onclick="clearT24Setup()">
                <i class="fas fa-trash me-1"></i> Vider Liste
              </button>
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Supprimer la modal existante si elle existe
    const existingModal = document.getElementById('t24SetupListModal');
    if (existingModal) {
      existingModal.remove();
    }

    // Ajouter la nouvelle modal
    document.body.insertAdjacentHTML('beforeend', modalContent);

    // Afficher la modal
    const modal = new bootstrap.Modal(document.getElementById('t24SetupListModal'));
    modal.show();

  } catch (error) {
    console.error('Erreur lors de l\'affichage de la liste T24:', error);
    showMessage('Erreur lors de l\'affichage de la liste T24.', 'danger');
  }
}

// Fonction pour supprimer une entrée du T24 Setup
function removeFromT24Setup(entryId) {
  try {
    let t24List = JSON.parse(localStorage.getItem('t24SetupList') || '[]');

    const initialLength = t24List.length;
    t24List = t24List.filter(entry => entry.id !== entryId);

    if (t24List.length < initialLength) {
      localStorage.setItem('t24SetupList', JSON.stringify(t24List));
      showMessage('Entrée supprimée du T24 Setup!', 'success');

      // Fermer et rouvrir la modal pour actualiser
      const modal = bootstrap.Modal.getInstance(document.getElementById('t24SetupListModal'));
      if (modal) {
        modal.hide();
        setTimeout(() => showT24SetupList(), 300);
      }

      updateT24SetupDisplay();
    } else {
      showMessage('Entrée non trouvée.', 'warning');
    }

  } catch (error) {
    console.error('Erreur lors de la suppression T24:', error);
    showMessage('Erreur lors de la suppression.', 'danger');
  }
}

// Fonction pour exporter toute la liste T24 Setup
function exportFullT24Setup() {
  try {
    const t24List = JSON.parse(localStorage.getItem('t24SetupList') || '[]');

    if (t24List.length === 0) {
      showMessage('Aucune donnée à exporter.', 'warning');
      return;
    }

    const currentDate = new Date();
    const exportData = {
      title: "T24 SETUP - Export Complet",
      area: "Twist Area",
      exportDate: currentDate.toISOString(),
      totalEntries: t24List.length,
      entries: t24List,
      version: "1.0"
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `T24-SETUP-COMPLET-${currentDate.toISOString().split('T')[0]}.json`;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showMessage(`${t24List.length} entrées T24 exportées avec succès!`, 'success');

  } catch (error) {
    console.error('Erreur lors de l\'export complet T24:', error);
    showMessage('Erreur lors de l\'export complet.', 'danger');
  }
}

// Fonction pour vider toute la liste T24 Setup
function clearT24Setup() {
  if (confirm('Êtes-vous sûr de vouloir vider toute la liste T24 Setup ? Cette action est irréversible.')) {
    try {
      localStorage.removeItem('t24SetupList');

      showMessage('Liste T24 Setup vidée avec succès!', 'success');

      // Fermer la modal
      const modal = bootstrap.Modal.getInstance(document.getElementById('t24SetupListModal'));
      if (modal) {
        modal.hide();
      }

      updateT24SetupDisplay();

    } catch (error) {
      console.error('Erreur lors du vidage T24:', error);
      showMessage('Erreur lors du vidage de la liste.', 'danger');
    }
  }
}

// Initialiser l'affichage T24 au chargement
document.addEventListener('DOMContentLoaded', function () {
  updateT24SetupDisplay();
});



async function applyUnicosFilters() {
  const group = document.getElementById('unicosGroupFilter').value;
  const machine = document.getElementById('unicosMachineFilter').value;
  const status = document.getElementById('unicosStatusFilter').value;

  try {
    // Charger tous les codes unicos si pas encore fait
    if (allUnicosCodes.length === 0) {
      allUnicosCodes = await loadDataTwistCodes();
    }

    // Appliquer les filtres
    filteredUnicosCodes = allUnicosCodes.filter(code => {
      let matches = true;

      if (group && !code.category.includes(group)) {
        matches = false;
      }

      if (machine && code.machine !== machine) {
        matches = false;
      }

      if (status && code.status !== status) {
        matches = false;
      }

      return matches;
    });

    // Afficher les résultats filtrés
    displayFilteredUnicosResults();

  } catch (error) {
    console.error('Erreur lors du filtrage:', error);
  }
}

function displayFilteredUnicosResults() {
  const resultDiv = document.getElementById('unicosResult');

  if (filteredUnicosCodes.length === 0) {
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = `
      <div class="alert alert-info">
        <h6><i class="fas fa-filter me-2"></i>Aucun résultat</h6>
        <p>Aucun code unicos ne correspond aux filtres sélectionnés.</p>
      </div>
    `;
    return;
  }

  const html = `
    <div class="alert alert-success">
      <h6><i class="fas fa-filter me-2"></i>Résultats filtrés</h6>
      <p>Trouvé <strong>${filteredUnicosCodes.length}</strong> codes unicos correspondant aux filtres.</p>
    </div>

    <div class="card">
      <div class="card-body">
        <div class="row">
          ${filteredUnicosCodes.slice(0, 10).map(code => `
            <div class="col-md-6 mb-3">
              <div class="card border-warning">
                <div class="card-body p-3">
                  <h6 class="card-title">${code.code}</h6>
                  <p class="card-text">
                    <small class="text-muted">
                      ${code.category} | ${code.machine} | ${code.status}
                    </small>
                  </p>
                  <button class="btn btn-warning btn-sm" onclick="quickScanUnicos('${code.code}')">
                    <i class="fas fa-search me-1"></i> Scanner
                  </button>
                </div>
              </div>
            </div>
          `).join('')}
        </div>

        ${filteredUnicosCodes.length > 10 ? `
          <div class="text-center mt-3">
            <small class="text-muted">Affichage des 10 premiers résultats sur ${filteredUnicosCodes.length}</small>
          </div>
        ` : ''}
      </div>
    </div>
  `;

  resultDiv.style.display = 'block';
  resultDiv.innerHTML = html;
}

// Fonction pour charger les statistiques unicos
async function loadUnicosStats() {
  const statsDiv = document.getElementById('unicosStats');

  try {
    // Charger tous les codes unicos
    if (allUnicosCodes.length === 0) {
      allUnicosCodes = await loadDataTwistCodes();
    }

    // Calculer les statistiques
    unicosStats.total = allUnicosCodes.length;
    unicosStats.active = allUnicosCodes.filter(code => code.status === 'Active').length;
    unicosStats.scanned = allUnicosCodes.filter(code => code.scanned === 'Oui').length;

    // Compter les machines uniques
    const uniqueMachines = new Set(allUnicosCodes.map(code => code.machine).filter(m => m));
    unicosStats.machines = uniqueMachines.size;

    // Mettre à jour l'affichage
    document.getElementById('totalUnicosCount').textContent = unicosStats.total;
    document.getElementById('activeUnicosCount').textContent = unicosStats.active;
    document.getElementById('scannedUnicosCount').textContent = unicosStats.scanned;
    document.getElementById('machinesCount').textContent = unicosStats.machines;

    // Afficher la section statistiques
    statsDiv.style.display = 'block';

    // Faire défiler vers les statistiques
    statsDiv.scrollIntoView({ behavior: 'smooth' });

  } catch (error) {
    console.error('Erreur lors du chargement des statistiques:', error);
    alert('Erreur lors du chargement des statistiques unicos.');
  }
}









// Fonction pour générer le contenu d'impression spécialisé unicos
function generateUnicosPrintContent(code) {
  const currentDate = new Date().toLocaleString('fr-FR');

  return `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Code-Barres Unicos - ${code}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; color: #333; }
        .header { text-align: center; border-bottom: 3px solid #ffc107; padding-bottom: 20px; margin-bottom: 30px; }
        .company-logo { font-size: 28px; font-weight: bold; color: #ffc107; margin-bottom: 10px; }
        .report-title { font-size: 22px; margin: 10px 0; }
        .barcode-section { text-align: center; margin: 40px 0; padding: 30px; background-color: #f8f9fa; border-radius: 8px; }
        .barcode-visual { margin: 20px 0; min-height: 100px; }
        .code-display { font-size: 32px; font-weight: bold; font-family: 'Courier New', monospace; margin: 20px 0; letter-spacing: 3px; }
        .badges { margin: 20px 0; }
        .badge { display: inline-block; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: bold; margin: 0 5px; }
        .badge-warning { background-color: #ffc107; color: #000; }
        .badge-info { background-color: #0dcaf0; color: #000; }
        .badge-success { background-color: #198754; color: #fff; }
        .footer { margin-top: 50px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; font-size: 12px; color: #666; }
        @media print { body { margin: 0; } .no-print { display: none; } }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="company-logo">APTIV - Data Twist Production</div>
        <div class="report-title">Code-Barres Unicos</div>
        <div>Généré le ${currentDate}</div>
      </div>

      <div class="barcode-section">
        <h2>Code Unicos: ${code}</h2>

        <div class="barcode-visual">
          <!-- Code-barres sera généré ici -->
          <div style="display: flex; justify-content: center; align-items: end; height: 80px; gap: 1px;">
            ${generateBarsPattern(code, 'CODE128')}
          </div>
        </div>

        <div class="code-display">${code}</div>

        <div class="badges">
          <span class="badge badge-warning">Format: CODE128</span>
          <span class="badge badge-info">Data Twist</span>
          <span class="badge badge-success">Unicos</span>
        </div>
      </div>

      <div class="footer">
        <p>Code-barres généré automatiquement pour le système Data Twist</p>
        <p>APTIV - Twisting Monitoring Tool - Scanner Codes Unicos</p>
        <p>Document généré le ${currentDate}</p>
      </div>
    </body>
    </html>
  `;
}

// Fonction pour afficher des messages
function showMessage(message, type = 'info') {
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
  alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;

  document.body.appendChild(alertDiv);

  // Supprimer automatiquement après 3 secondes
  setTimeout(() => {
    if (alertDiv.parentElement) {
      alertDiv.remove();
    }
  }, 3000);
}

// ===== SCAN SETUP CONFIGURATION =====

// Variables globales pour la configuration du scan
let scanConfig = {
  autoScan: true,
  scanDelay: 500,
  soundEnabled: true,
  vibrationEnabled: true,
  autoComplete: true,
  scanMode: 'unicos', // 'unicos', 'general', 'both'
  barcodeFormats: ['CODE128', 'CODE39', 'EAN13', 'EAN8'],
  duplicateHandling: 'warn', // 'allow', 'warn', 'block'
  scanHistory: true,
  maxHistoryItems: 100,
  theme: 'default' // 'default', 'dark', 'high-contrast'
};

// Charger la configuration depuis localStorage
function loadScanConfig() {
  try {
    const savedConfig = localStorage.getItem('scanConfiguration');
    if (savedConfig) {
      scanConfig = { ...scanConfig, ...JSON.parse(savedConfig) };
    }
  } catch (error) {
    console.error('Erreur lors du chargement de la configuration:', error);
  }
}

// Sauvegarder la configuration dans localStorage
function saveScanConfig() {
  try {
    localStorage.setItem('scanConfiguration', JSON.stringify(scanConfig));
    showMessage('Configuration du scan sauvegardée!', 'success');
  } catch (error) {
    console.error('Erreur lors de la sauvegarde de la configuration:', error);
    showMessage('Erreur lors de la sauvegarde de la configuration.', 'danger');
  }
}

// Fonction pour ouvrir la modal de configuration du scan
function openScanSetup() {
  // Charger la configuration actuelle
  loadScanConfig();

  const modalContent = `
    <div class="modal fade" id="scanSetupModal" tabindex="-1">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header bg-info text-white">
            <h5 class="modal-title">
              <i class="fas fa-cog me-2"></i>
              Scan Setup - Configuration du Scanner
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">

            <!-- Onglets de configuration -->
            <ul class="nav nav-tabs" id="setupTabs" role="tablist">
              <li class="nav-item" role="presentation">
                <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                  <i class="fas fa-cog me-1"></i> Général
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link" id="scanner-tab" data-bs-toggle="tab" data-bs-target="#scanner" type="button" role="tab">
                  <i class="fas fa-barcode me-1"></i> Scanner
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link" id="feedback-tab" data-bs-toggle="tab" data-bs-target="#feedback" type="button" role="tab">
                  <i class="fas fa-volume-up me-1"></i> Feedback
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link" id="advanced-tab" data-bs-toggle="tab" data-bs-target="#advanced" type="button" role="tab">
                  <i class="fas fa-tools me-1"></i> Avancé
                </button>
              </li>
            </ul>

            <div class="tab-content mt-3" id="setupTabContent">

              <!-- Onglet Général -->
              <div class="tab-pane fade show active" id="general" role="tabpanel">
                <div class="row">
                  <div class="col-md-6">
                    <h6><i class="fas fa-play me-2"></i>Mode de Scan</h6>
                    <div class="mb-3">
                      <select id="scanModeSelect" class="form-select">
                        <option value="unicos">Codes Unicos uniquement</option>
                        <option value="general">Scanner général</option>
                        <option value="both">Les deux modes</option>
                      </select>
                      <small class="text-muted">Définit le type de codes à scanner prioritairement</small>
                    </div>

                    <div class="form-check form-switch mb-3">
                      <input class="form-check-input" type="checkbox" id="autoScanCheck">
                      <label class="form-check-label" for="autoScanCheck">
                        <strong>Scan automatique</strong>
                      </label>
                      <small class="d-block text-muted">Lance le scan automatiquement après saisie</small>
                    </div>

                    <div class="form-check form-switch mb-3">
                      <input class="form-check-input" type="checkbox" id="autoCompleteCheck">
                      <label class="form-check-label" for="autoCompleteCheck">
                        <strong>Auto-complétion</strong>
                      </label>
                      <small class="d-block text-muted">Complète automatiquement les codes unicos (EK9-)</small>
                    </div>
                  </div>

                  <div class="col-md-6">
                    <h6><i class="fas fa-clock me-2"></i>Délai de Scan</h6>
                    <div class="mb-3">
                      <label for="scanDelayRange" class="form-label">Délai: <span id="delayValue">500</span>ms</label>
                      <input type="range" class="form-range" id="scanDelayRange" min="100" max="2000" step="100" value="500">
                      <small class="text-muted">Temps d'attente avant le scan automatique</small>
                    </div>

                    <h6><i class="fas fa-history me-2"></i>Historique</h6>
                    <div class="form-check form-switch mb-2">
                      <input class="form-check-input" type="checkbox" id="scanHistoryCheck">
                      <label class="form-check-label" for="scanHistoryCheck">
                        <strong>Enregistrer l'historique</strong>
                      </label>
                    </div>

                    <div class="mb-3">
                      <label for="maxHistorySelect" class="form-label">Nombre max d'éléments:</label>
                      <select id="maxHistorySelect" class="form-select">
                        <option value="50">50 éléments</option>
                        <option value="100">100 éléments</option>
                        <option value="200">200 éléments</option>
                        <option value="500">500 éléments</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Onglet Scanner -->
              <div class="tab-pane fade" id="scanner" role="tabpanel">
                <div class="row">
                  <div class="col-md-6">
                    <h6><i class="fas fa-barcode me-2"></i>Formats de Code-Barres</h6>
                    <div class="mb-3">
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="code128Check" value="CODE128">
                        <label class="form-check-label" for="code128Check">
                          <strong>CODE128</strong> <span class="badge bg-warning text-dark">Unicos</span>
                        </label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="code39Check" value="CODE39">
                        <label class="form-check-label" for="code39Check">
                          <strong>CODE39</strong> <span class="badge bg-info">Standard</span>
                        </label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="ean13Check" value="EAN13">
                        <label class="form-check-label" for="ean13Check">
                          <strong>EAN13</strong> <span class="badge bg-success">Produits</span>
                        </label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="ean8Check" value="EAN8">
                        <label class="form-check-label" for="ean8Check">
                          <strong>EAN8</strong> <span class="badge bg-success">Produits</span>
                        </label>
                      </div>
                    </div>
                  </div>

                  <div class="col-md-6">
                    <h6><i class="fas fa-copy me-2"></i>Gestion des Doublons</h6>
                    <div class="mb-3">
                      <div class="form-check">
                        <input class="form-check-input" type="radio" name="duplicateHandling" id="allowDuplicates" value="allow">
                        <label class="form-check-label" for="allowDuplicates">
                          <strong>Autoriser</strong> - Permet les scans multiples
                        </label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="radio" name="duplicateHandling" id="warnDuplicates" value="warn">
                        <label class="form-check-label" for="warnDuplicates">
                          <strong>Avertir</strong> - Affiche un avertissement
                        </label>
                      </div>
                      <div class="form-check">
                        <input class="form-check-input" type="radio" name="duplicateHandling" id="blockDuplicates" value="block">
                        <label class="form-check-label" for="blockDuplicates">
                          <strong>Bloquer</strong> - Empêche les doublons
                        </label>
                      </div>
                    </div>

                    <div class="alert alert-info">
                      <small>
                        <i class="fas fa-info-circle me-1"></i>
                        <strong>Recommandé:</strong> Mode "Avertir" pour les codes unicos
                      </small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Onglet Feedback -->
              <div class="tab-pane fade" id="feedback" role="tabpanel">
                <div class="row">
                  <div class="col-md-6">
                    <h6><i class="fas fa-volume-up me-2"></i>Feedback Audio</h6>
                    <div class="form-check form-switch mb-3">
                      <input class="form-check-input" type="checkbox" id="soundEnabledCheck">
                      <label class="form-check-label" for="soundEnabledCheck">
                        <strong>Sons activés</strong>
                      </label>
                      <small class="d-block text-muted">Joue un son lors du scan</small>
                    </div>

                    <div class="mb-3">
                      <button class="btn btn-outline-primary btn-sm" onclick="testScanSound('success')">
                        <i class="fas fa-play me-1"></i> Test Son Succès
                      </button>
                      <button class="btn btn-outline-warning btn-sm" onclick="testScanSound('warning')">
                        <i class="fas fa-play me-1"></i> Test Son Avertissement
                      </button>
                      <button class="btn btn-outline-danger btn-sm" onclick="testScanSound('error')">
                        <i class="fas fa-play me-1"></i> Test Son Erreur
                      </button>
                    </div>
                  </div>

                  <div class="col-md-6">
                    <h6><i class="fas fa-mobile-alt me-2"></i>Feedback Tactile</h6>
                    <div class="form-check form-switch mb-3">
                      <input class="form-check-input" type="checkbox" id="vibrationEnabledCheck">
                      <label class="form-check-label" for="vibrationEnabledCheck">
                        <strong>Vibration activée</strong>
                      </label>
                      <small class="d-block text-muted">Vibre sur les appareils mobiles</small>
                    </div>

                    <div class="mb-3">
                      <button class="btn btn-outline-info btn-sm" onclick="testVibration()">
                        <i class="fas fa-mobile-alt me-1"></i> Test Vibration
                      </button>
                    </div>

                    <div class="alert alert-warning">
                      <small>
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        La vibration ne fonctionne que sur les appareils mobiles
                      </small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Onglet Avancé -->
              <div class="tab-pane fade" id="advanced" role="tabpanel">
                <div class="row">
                  <div class="col-md-6">
                    <h6><i class="fas fa-palette me-2"></i>Thème d'Interface</h6>
                    <div class="mb-3">
                      <select id="themeSelect" class="form-select">
                        <option value="default">Thème par défaut</option>
                        <option value="dark">Thème sombre</option>
                        <option value="high-contrast">Contraste élevé</option>
                      </select>
                      <small class="text-muted">Apparence de l'interface de scan</small>
                    </div>

                    <h6><i class="fas fa-database me-2"></i>Données</h6>
                    <div class="d-grid gap-2">
                      <button class="btn btn-outline-warning" onclick="exportScanConfig()">
                        <i class="fas fa-download me-1"></i> Exporter Configuration
                      </button>
                      <button class="btn btn-outline-info" onclick="importScanConfig()">
                        <i class="fas fa-upload me-1"></i> Importer Configuration
                      </button>
                    </div>
                  </div>

                  <div class="col-md-6">
                    <h6><i class="fas fa-trash me-2"></i>Réinitialisation</h6>
                    <div class="alert alert-warning">
                      <small>
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        Ces actions sont irréversibles
                      </small>
                    </div>

                    <div class="d-grid gap-2">
                      <button class="btn btn-outline-warning" onclick="clearScanHistory()">
                        <i class="fas fa-history me-1"></i> Vider l'Historique
                      </button>
                      <button class="btn btn-outline-danger" onclick="resetScanConfig()">
                        <i class="fas fa-undo me-1"></i> Réinitialiser Configuration
                      </button>
                    </div>

                    <div class="mt-3">
                      <small class="text-muted">
                        <strong>Version:</strong> Scanner Unicos v1.0<br>
                        <strong>Dernière mise à jour:</strong> 17/07/2025
                      </small>
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
            <button type="button" class="btn btn-primary" onclick="applyScanConfig()">
              <i class="fas fa-save me-1"></i> Appliquer Configuration
            </button>
          </div>
        </div>
      </div>
    </div>
  `;

  // Supprimer la modal existante si elle existe
  const existingModal = document.getElementById('scanSetupModal');
  if (existingModal) {
    existingModal.remove();
  }

  // Ajouter la nouvelle modal au body
  document.body.insertAdjacentHTML('beforeend', modalContent);

  // Initialiser les valeurs de la modal avec la configuration actuelle
  initializeScanSetupModal();

  // Afficher la modal
  const modal = new bootstrap.Modal(document.getElementById('scanSetupModal'));
  modal.show();
}

// Fonction pour initialiser les valeurs de la modal
function initializeScanSetupModal() {
  try {
    // Onglet Général
    document.getElementById('scanModeSelect').value = scanConfig.scanMode;
    document.getElementById('autoScanCheck').checked = scanConfig.autoScan;
    document.getElementById('autoCompleteCheck').checked = scanConfig.autoComplete;
    document.getElementById('scanDelayRange').value = scanConfig.scanDelay;
    document.getElementById('delayValue').textContent = scanConfig.scanDelay;
    document.getElementById('scanHistoryCheck').checked = scanConfig.scanHistory;
    document.getElementById('maxHistorySelect').value = scanConfig.maxHistoryItems;

    // Onglet Scanner
    scanConfig.barcodeFormats.forEach(format => {
      const checkbox = document.getElementById(format.toLowerCase() + 'Check');
      if (checkbox) checkbox.checked = true;
    });

    document.querySelector(`input[name="duplicateHandling"][value="${scanConfig.duplicateHandling}"]`).checked = true;

    // Onglet Feedback
    document.getElementById('soundEnabledCheck').checked = scanConfig.soundEnabled;
    document.getElementById('vibrationEnabledCheck').checked = scanConfig.vibrationEnabled;

    // Onglet Avancé
    document.getElementById('themeSelect').value = scanConfig.theme;

    // Event listeners
    document.getElementById('scanDelayRange').addEventListener('input', function (e) {
      document.getElementById('delayValue').textContent = e.target.value;
    });

  } catch (error) {
    console.error('Erreur lors de l\'initialisation de la modal:', error);
  }
}

// Fonction pour appliquer la configuration
function applyScanConfig() {
  try {
    // Récupérer les valeurs de la modal
    scanConfig.scanMode = document.getElementById('scanModeSelect').value;
    scanConfig.autoScan = document.getElementById('autoScanCheck').checked;
    scanConfig.autoComplete = document.getElementById('autoCompleteCheck').checked;
    scanConfig.scanDelay = parseInt(document.getElementById('scanDelayRange').value);
    scanConfig.scanHistory = document.getElementById('scanHistoryCheck').checked;
    scanConfig.maxHistoryItems = parseInt(document.getElementById('maxHistorySelect').value);

    // Formats de code-barres
    scanConfig.barcodeFormats = [];
    ['code128', 'code39', 'ean13', 'ean8'].forEach(format => {
      const checkbox = document.getElementById(format + 'Check');
      if (checkbox && checkbox.checked) {
        scanConfig.barcodeFormats.push(format.toUpperCase());
      }
    });

    // Gestion des doublons
    const duplicateRadio = document.querySelector('input[name="duplicateHandling"]:checked');
    if (duplicateRadio) {
      scanConfig.duplicateHandling = duplicateRadio.value;
    }

    // Feedback
    scanConfig.soundEnabled = document.getElementById('soundEnabledCheck').checked;
    scanConfig.vibrationEnabled = document.getElementById('vibrationEnabledCheck').checked;

    // Thème
    scanConfig.theme = document.getElementById('themeSelect').value;

    // Sauvegarder la configuration
    saveScanConfig();

    // Appliquer les changements
    applyScanConfigChanges();

    // Fermer la modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('scanSetupModal'));
    modal.hide();

  } catch (error) {
    console.error('Erreur lors de l\'application de la configuration:', error);
    showMessage('Erreur lors de l\'application de la configuration.', 'danger');
  }
}

// Fonction pour appliquer les changements de configuration
function applyScanConfigChanges() {
  try {
    // Appliquer le thème
    applyTheme(scanConfig.theme);

    // Mettre à jour l'auto-complétion
    updateAutoCompletion();

    // Afficher un message de confirmation
    showMessage('Configuration appliquée avec succès!', 'success');

    console.log('Configuration appliquée:', scanConfig);

  } catch (error) {
    console.error('Erreur lors de l\'application des changements:', error);
  }
}

// Fonction pour appliquer le thème
function applyTheme(theme) {
  const body = document.body;

  // Supprimer les classes de thème existantes
  body.classList.remove('theme-default', 'theme-dark', 'theme-high-contrast');

  // Appliquer le nouveau thème
  body.classList.add(`theme-${theme}`);
}

// Fonction pour mettre à jour l'auto-complétion
function updateAutoCompletion() {
  const unicosInput = document.getElementById('unicosInput');
  if (unicosInput && scanConfig.autoComplete) {
    // L'auto-complétion est déjà implémentée dans le DOMContentLoaded
    console.log('Auto-complétion activée');
  }
}

// Fonctions de test pour le feedback
function testScanSound(type) {
  try {
    // Créer un contexte audio simple
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    // Fréquences différentes selon le type
    const frequencies = {
      success: 800,
      warning: 600,
      error: 400
    };

    oscillator.frequency.setValueAtTime(frequencies[type] || 600, audioContext.currentTime);
    oscillator.type = 'sine';

    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.2);

    showMessage(`Son de test ${type} joué!`, 'info');

  } catch (error) {
    console.error('Erreur lors du test audio:', error);
    showMessage('Impossible de jouer le son de test.', 'warning');
  }
}

function testVibration() {
  try {
    if (navigator.vibrate) {
      navigator.vibrate([100, 50, 100]);
      showMessage('Test de vibration effectué!', 'info');
    } else {
      showMessage('Vibration non supportée sur cet appareil.', 'warning');
    }
  } catch (error) {
    console.error('Erreur lors du test de vibration:', error);
    showMessage('Erreur lors du test de vibration.', 'warning');
  }
}

// Fonctions avancées de configuration

// Exporter la configuration
function exportScanConfig() {
  try {
    const configData = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      config: scanConfig
    };

    const dataStr = JSON.stringify(configData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `scan-config-${new Date().toISOString().split('T')[0]}.json`;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showMessage('Configuration exportée avec succès!', 'success');

  } catch (error) {
    console.error('Erreur lors de l\'export:', error);
    showMessage('Erreur lors de l\'export de la configuration.', 'danger');
  }
}

// Importer la configuration
function importScanConfig() {
  try {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';

    input.onchange = function (e) {
      const file = e.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = function (e) {
        try {
          const importedData = JSON.parse(e.target.result);

          if (importedData.config) {
            // Valider et appliquer la configuration importée
            scanConfig = { ...scanConfig, ...importedData.config };
            saveScanConfig();

            // Réinitialiser la modal si elle est ouverte
            const modal = document.getElementById('scanSetupModal');
            if (modal) {
              initializeScanSetupModal();
            }

            showMessage('Configuration importée avec succès!', 'success');
          } else {
            showMessage('Fichier de configuration invalide.', 'warning');
          }
        } catch (error) {
          console.error('Erreur lors de l\'import:', error);
          showMessage('Erreur lors de l\'import du fichier.', 'danger');
        }
      };

      reader.readAsText(file);
    };

    input.click();

  } catch (error) {
    console.error('Erreur lors de l\'import:', error);
    showMessage('Erreur lors de l\'import de la configuration.', 'danger');
  }
}

// Vider l'historique des scans
function clearScanHistory() {
  if (confirm('Êtes-vous sûr de vouloir vider l\'historique des scans ? Cette action est irréversible.')) {
    try {
      localStorage.removeItem('barcodeScanHistory');

      // Actualiser l'affichage des scans récents
      displayRecentScans();

      showMessage('Historique des scans vidé avec succès!', 'success');

    } catch (error) {
      console.error('Erreur lors du vidage de l\'historique:', error);
      showMessage('Erreur lors du vidage de l\'historique.', 'danger');
    }
  }
}

// Réinitialiser la configuration
function resetScanConfig() {
  if (confirm('Êtes-vous sûr de vouloir réinitialiser toute la configuration ? Cette action est irréversible.')) {
    try {
      // Configuration par défaut
      scanConfig = {
        autoScan: true,
        scanDelay: 500,
        soundEnabled: true,
        vibrationEnabled: true,
        autoComplete: true,
        scanMode: 'unicos',
        barcodeFormats: ['CODE128', 'CODE39', 'EAN13', 'EAN8'],
        duplicateHandling: 'warn',
        scanHistory: true,
        maxHistoryItems: 100,
        theme: 'default'
      };

      // Supprimer la configuration sauvegardée
      localStorage.removeItem('scanConfiguration');

      // Réinitialiser la modal si elle est ouverte
      const modal = document.getElementById('scanSetupModal');
      if (modal) {
        initializeScanSetupModal();
      }

      // Appliquer les changements
      applyScanConfigChanges();

      showMessage('Configuration réinitialisée avec succès!', 'success');

    } catch (error) {
      console.error('Erreur lors de la réinitialisation:', error);
      showMessage('Erreur lors de la réinitialisation.', 'danger');
    }
  }
}

// Charger la configuration au démarrage
document.addEventListener('DOMContentLoaded', function () {
  loadScanConfig();
  applyScanConfigChanges();
});

// Function to load more Data Twist scans
async function loadMoreDataTwistScans() {
  const recentScansDiv = document.getElementById('recentScans');
  if (!recentScansDiv) return;

  // Show loading state
  recentScansDiv.innerHTML = '<p class="text-muted"><i class="fas fa-spinner fa-spin me-2"></i>Chargement des nouveaux scans...</p>';

  try {
    // Load fresh data
    await displayRecentScans();

    // Show success message briefly
    const successMsg = document.createElement('div');
    successMsg.className = 'alert alert-success alert-dismissible fade show mt-2';
    successMsg.innerHTML = `
      <i class="fas fa-check me-2"></i>Scans actualisés avec les dernières données Data Twist
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const parentDiv = recentScansDiv.parentElement;
    if (parentDiv) {
      parentDiv.appendChild(successMsg);

      // Auto-remove success message after 3 seconds
      setTimeout(() => {
        if (successMsg.parentElement) {
          successMsg.remove();
        }
      }, 3000);
    }

  } catch (error) {
    console.error('Erreur lors du chargement des scans:', error);
    recentScansDiv.innerHTML = '<p class="text-danger"><i class="fas fa-exclamation-triangle me-2"></i>Erreur lors du chargement des scans</p>';
  }
}

// Function to simulate real-time Data Twist scans
function simulateDataTwistScan(code) {
  // Add a new scan to local storage to simulate real-time scanning
  const scanRecord = {
    code: code,
    timestamp: new Date().toISOString(),
    user: 'Production System',
    action: 'Auto Scan',
    location: 'Production Line',
    result: 'Scanned'
  };

  // Get existing scans and add the new one
  const scans = JSON.parse(localStorage.getItem('barcodeScanHistory') || '[]');
  scans.push(scanRecord);

  // Keep only the last 100 scans
  if (scans.length > 100) {
    scans.splice(0, scans.length - 100);
  }

  localStorage.setItem('barcodeScanHistory', JSON.stringify(scans));

  // Refresh the display
  displayRecentScans();
}






