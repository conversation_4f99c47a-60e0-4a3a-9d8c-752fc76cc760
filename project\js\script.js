
// Configuration globale
const CONFIG = {
  colors: {
    success: '#20c997',
    warning: '#ffc107',
    danger: '#dc3545',
    primary: '#4e73df',
    secondary: '#6c757d'
  },
  productionTarget: 1000,
  endShiftTarget: 1200
};

// Éléments DOM fréquemment utilisés
const DOM = {
  yearElement: document.getElementById('current-year'),
  productionCanvas: document.getElementById('productionChart'),
  hourlyProductionCanvas: document.getElementById('hourlyProductionChart'),
  machineInfoBody: document.getElementById('machineInfoBody')
};

// Initialisation de l'application
document.addEventListener('DOMContentLoaded', function () {
  initApp();
});

function initApp() {
  // Initialiser l'année dans le footer
  if (DOM.yearElement) {
    DOM.yearElement.textContent = new Date().getFullYear();
  }

  // Initialiser les tooltips
  initTooltips();

  // Initialiser les graphiques
  if (DOM.productionCanvas) {
    initProductionChart();
  }

  if (DOM.hourlyProductionCanvas) {
    initHourlyProductionChart('today');
  }

  // Initialiser la table des machines
  if (DOM.machineInfoBody) {
    displayMachines();
  }

  // Initialiser le scanner de code-barres
  initBarcodeScanner();

  // Initialiser les écouteurs d'événements
  initEventListeners();
}

function initTooltips() {
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.forEach(tooltipTriggerEl => {
    new bootstrap.Tooltip(tooltipTriggerEl);
  });
}

function initProductionChart() {
  try {
    const productionCtx = DOM.productionCanvas.getContext('2d');

    // Données de production par machine
    const machineLabels = ['MC-10 CXC', 'MC-11 CXC', 'MC-12 CXC', 'MC-13 CXC', 'MC-14 CXC', 'MC-15 CXC', 'MC-16 CXC', 'MC-17 CXC'];
    const productionData = [950, 1100, 800, 1200, 650, 900, 1000, 1300];

    // Calcul des couleurs en fonction du pourcentage par rapport à la cible
    const backgroundColors = productionData.map(quantity => {
      const percentage = (quantity / CONFIG.productionTarget) * 100;
      if (percentage >= 100) return CONFIG.colors.success;  // Vert si objectif atteint
      if (percentage >= 80) return CONFIG.colors.warning;   // Jaune si proche de l'objectif
      return CONFIG.colors.danger;                          // Rouge si en dessous de 80%
    });

    // Création du graphique
    return new Chart(productionCtx, {
      type: 'bar',
      data: {
        labels: machineLabels,
        datasets: [{
          label: 'Production',
          data: productionData,
          backgroundColor: backgroundColors,
          borderColor: backgroundColors.map(color => color.replace('0.6', '1')),
          borderWidth: 1,
          barPercentage: 0.7,
          categoryPercentage: 0.8
        }]
      },
      options: getChartOptions()
    });
  } catch (error) {
    console.error('Erreur lors de l\'initialisation du graphique de production:', error);
    return null;
  }
}

function getChartOptions() {
  return {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        beginAtZero: true,
        title: { display: true, text: 'Quantité Produite' },
        grid: { drawBorder: false },
        ticks: { callback: value => value.toLocaleString() }
      },
      x: { grid: { display: false } }
    },
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          usePointStyle: true,
          padding: 20
        }
      },
      tooltip: {
        callbacks: {
          label: function (context) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              label += context.parsed.y;
            }
            return label;
          }
        }
      },
      animation: {
        duration: 1000,
        easing: 'easeInOutQuart'
      }
    }
  };
}

// Start Production Functionality
function initProductionControls() {
  const startProductionBtn = document.getElementById('startProductionBtn');
  const startProductionModal = new bootstrap.Modal(document.getElementById('startProductionModal'));
  const machineSelectModal = document.getElementById('machineSelectModal');
  const confirmStartBtn = document.getElementById('confirmStartProduction');

  if (startProductionBtn) {
    startProductionBtn.addEventListener('click', function () {
      startProductionModal.show();
    });
  }

  // Populate machine dropdown in modal if it exists
  if (machineSelectModal) {
    const machineOptions = document.querySelectorAll('#machineSelect option[value^="T"]');
    machineOptions.forEach(option => {
      if (option.value) {
        const newOption = document.createElement('option');
        newOption.value = option.value;
        newOption.textContent = option.textContent;
        machineSelectModal.appendChild(newOption);
      }
    });
  }

  // Handle form submission
  if (confirmStartBtn) {
    confirmStartBtn.addEventListener('click', function () {
      const machine = machineSelectModal ? machineSelectModal.value : '';
      const orderNumber = document.getElementById('orderNumber') ? document.getElementById('orderNumber').value : '';
      const quantity = document.getElementById('quantity') ? document.getElementById('quantity').value : '';

      if (!machine || !orderNumber || !quantity) {
        alert('Please fill in all fields');
        return;
      }

      // Here you would typically make an API call to start production
      console.log(`Starting production for ${machine}, Order: ${orderNumber}, Quantity: ${quantity}`);

      // Show success message
      const machineName = machineSelectModal ? machineSelectModal.options[machineSelectModal.selectedIndex].text : machine;
      alert(`Production started for ${machineName}\nOrder: ${orderNumber}\nQuantity: ${quantity}`);

      // Reset form and close modal
      if (document.getElementById('startProductionForm')) {
        document.getElementById('startProductionForm').reset();
      }
      startProductionModal.hide();
    });
  }
}

// Initialisation des contrôles de production
initProductionControls();

// Données des machines (à remplacer par un appel API dans un environnement de production)
const machinesData = [
  { id: 'MC-10', modele: 'CXC-2000', annee: 2020, statut: 'En service', derniereMaintenance: '2025-06-15', prochaineMaintenance: '2025-08-15', heuresFonctionnement: 2450 },
  { id: 'MC-11', modele: 'CXC-2000', annee: 2020, statut: 'En service', derniereMaintenance: '2025-06-20', prochaineMaintenance: '2025-08-20', heuresFonctionnement: 2380 },
  { id: 'MC-12', modele: 'CXC-2000', annee: 2021, statut: 'Maintenance', derniereMaintenance: '2025-06-10', prochaineMaintenance: '2025-08-10', heuresFonctionnement: 1980 },
  { id: 'MC-13', modele: 'CXC-3000', annee: 2021, statut: 'En service', derniereMaintenance: '2025-06-25', prochaineMaintenance: '2025-08-25', heuresFonctionnement: 2150 },
  { id: 'MC-14', modele: 'CXC-3000', annee: 2022, statut: 'En service', derniereMaintenance: '2025-07-01', prochaineMaintenance: '2025-09-01', heuresFonctionnement: 1750 },
  { id: 'MC-15', modele: 'CXC-4000', annee: 2022, statut: 'Hors service', derniereMaintenance: '2025-05-30', prochaineMaintenance: '2025-07-30', heuresFonctionnement: 1650 },
  { id: 'MC-16', modele: 'CXC-4000', annee: 2023, statut: 'En service', derniereMaintenance: '2025-07-05', prochaineMaintenance: '2025-09-05', heuresFonctionnement: 1200 },
  { id: 'MC-17', modele: 'CXC-4000', annee: 2023, statut: 'En service', derniereMaintenance: '2025-07-10', prochaineMaintenance: '2025-09-10', heuresFonctionnement: 980 }

];

// Fonction pour formater la date
function formatDate(dateString) {
  const options = { year: 'numeric', month: '2-digit', day: '2-digit' };
  return new Date(dateString).toLocaleDateString('fr-FR', options);
}

// Fonction pour obtenir la classe CSS en fonction du statut
function getStatusClass(statut) {
  switch (statut.toLowerCase()) {
    case 'en service':
      return 'badge bg-success';
    case 'maintenance':
      return 'badge bg-warning text-dark';
    case 'hors service':
      return 'badge bg-danger';
    default:
      return 'badge bg-secondary';
  }
}

// Fonction pour afficher les machines
function displayMachines() {
  const tbody = document.getElementById('machineInfoBody');
  tbody.innerHTML = ''; // Vider le tableau

  machinesData.forEach(machine => {
    const tr = document.createElement('tr');

    // Définir la classe de la ligne en fonction du statut
    if (machine.statut === 'Hors service') {
      tr.classList.add('table-danger');
    } else if (machine.statut === 'Maintenance') {
      tr.classList.add('table-warning');
    }

    tr.innerHTML = `
                    <td><strong>${machine.id}</strong></td>
                    <td>${machine.modele}</td>
                    <td>${machine.annee}</td>
                    <td><span class="${getStatusClass(machine.statut)}">${machine.statut}</span></td>
                    <td>${formatDate(machine.derniereMaintenance)}</td>
                    <td>${formatDate(machine.prochaineMaintenance)}</td>
                    <td>${machine.heuresFonctionnement.toLocaleString()} h</td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" title="Voir les détails">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-warning me-1" title="Planifier une maintenance">
                            <i class="fas fa-tools"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" title="Historique">
                            <i class="fas fa-history"></i>
                        </button>
                    </td>
                `;
    tbody.appendChild(tr);
  });
}

// Données de production par heure (exemple)
const hourlyProductionData = {
  today: {
    labels: Array.from({ length: 24 }, (_, i) => `${i}:00`),
    actual: Array.from({ length: 24 }, () => Math.floor(Math.random() * 100) + 50),
    target: Array(24).fill(80)
  },
  yesterday: {
    labels: Array.from({ length: 24 }, (_, i) => `${i}:00`),
    actual: Array.from({ length: 24 }, () => Math.floor(Math.random() * 90) + 40),
    target: Array(24).fill(80)
  },
  week: {
    labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
    actual: [550, 620, 580, 600, 650, 700, 450],
    target: Array(7).fill(600)
  }
};

// Initialisation du graphique de production par heure
let hourlyProductionChart;

function initHourlyProductionChart(period = 'today') {
  const ctx = document.getElementById('hourlyProductionChart').getContext('2d');
  const data = hourlyProductionData[period];

  // Détruire le graphique existant s'il existe
  if (hourlyProductionChart) {
    hourlyProductionChart.destroy();
  }

  // Créer un nouveau graphique
  hourlyProductionChart = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: data.labels,
      datasets: [
        {
          label: 'Production Réelle',
          data: data.actual,
          backgroundColor: 'rgba(78, 115, 223, 0.8)',
          borderColor: 'rgba(78, 115, 223, 1)',
          borderWidth: 1,
          borderRadius: 4,
          yAxisID: 'y',
          barPercentage: 0.8,
          categoryPercentage: 0.8
        },
        {
          label: 'Objectif',
          data: data.target,
          type: 'line',
          borderColor: 'rgba(220, 53, 69, 0.8)',
          borderWidth: 2,
          borderDash: [5, 5],
          fill: false,
          pointRadius: 0,
          yAxisID: 'y'
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        mode: 'index',
        intersect: false
      },
      scales: {
        y: {
          beginAtZero: true,
          type: 'linear',
          display: true,
          position: 'left',
          title: {
            display: true,
            text: 'Quantité produite'
          },
          grid: {
            drawOnChartArea: true,
            drawBorder: false
          }
        },
        x: {
          grid: {
            display: false
          },
          title: {
            display: true,
            text: period === 'week' ? 'Jours de la Semaine' : 'Heures de la Journée'
          }
        }
      },
      plugins: {
        legend: {
          display: true,
          position: 'top',
          labels: {
            usePointStyle: true,
            boxWidth: 6
          }
        },
        tooltip: {
          mode: 'index',
          intersect: false,
          callbacks: {
            label: function (context) {
              return `${context.dataset.label}: ${context.parsed.y} unités`;
            }
          }
        }
      },
      animation: {
        duration: 1000,
        easing: 'easeInOutQuart'
      }
    }
  });
}

// Gestion des boutons de période
document.querySelectorAll('[data-period]').forEach(button => {
  button.addEventListener('click', function () {
    // Mettre à jour l'état actif des boutons
    document.querySelectorAll('[data-period]').forEach(btn => {
      btn.classList.remove('active');
    });
    this.classList.add('active');

    // Mettre à jour le graphique avec la période sélectionnée
    initHourlyProductionChart(this.dataset.period);
  });
});

// Appeler les fonctions d'initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function () {
  displayMachines();
  initHourlyProductionChart('today');

  // Initialiser les tooltips
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });

  // Activer les liens de navigation
  const dashboardLink = document.getElementById('dashboard-link');
  const reportsLink = document.getElementById('reports-link');
  const dashboardSection = document.querySelector('.dashboard');
  const reportsSection = document.getElementById('reportModal');

  dashboardLink.addEventListener('click', function (e) {
    e.preventDefault();
    dashboardSection.style.display = 'block';
    new bootstrap.Modal(reportsSection).hide();
  });

  reportsLink.addEventListener('click', function (e) {
    e.preventDefault();
    dashboardSection.style.display = 'none';
    new bootstrap.Modal(reportsSection).show();
  });

  // Charger les données et mettre à jour le tableau
  loadAndDisplayData();

  // Activer le bouton de génération de rapport
  const generateReportBtn = document.getElementById('generateReportBtn');
  generateReportBtn.addEventListener('click', function () {
    const reportType = document.getElementById('reportType').value;
    const reportFormat = document.getElementById('reportFormat').value;
    const startDate = document.getElementById('reportStartDate').value;
    const endDate = document.getElementById('reportEndDate').value;
    const machines = Array.from(document.getElementById('reportMachines').selectedOptions).map(option => option.value);
    const includeCharts = document.getElementById('includeCharts').checked;

    alert(`Rapport généré:\nType: ${reportType}\nFormat: ${reportFormat}\nDate de début: ${startDate}\nDate de fin: ${endDate}\nMachines: ${machines.join(', ')}\nInclure les graphiques: ${includeCharts}`);
  });
});

// Charger les données et mettre à jour le tableau
async function loadAndDisplayData() {
  try {
    const response = await fetch('js/data.json');
    const result = await response.json();
    const data = result.data;

    // Calculer les moyennes
    const averages = {
      output: data.reduce((sum, item) => sum + (item.output || 0), 0) / data.length,
      uptime: data.reduce((sum, item) => sum + (item.uptime || 0), 0) / data.length,
      unplanned_dt: data.reduce((sum, item) => sum + (item.unplanned_dt || 0), 0) / data.length,
      planned_dt: data.reduce((sum, item) => sum + (item.planned_dt || 0), 0) / data.length,
      setup_time: data.reduce((sum, item) => sum + (item.setup_time || 0), 0) / data.length,
      pmh: data.reduce((sum, item) => sum + (item.pmh || 0), 0) / data.length,
      ftq: data.reduce((sum, item) => sum + (item.ftq || 0), 0) / data.length,
      throughput: data.reduce((sum, item) => sum + (item.throughput || 0), 0) / data.length,
      avg_target_setup: data.reduce((sum, item) => sum + (item.avg_target_setup || 0), 0) / data.length,
      compliance_setup: data.reduce((sum, item) => sum + (item.compliance_setup || 0), 0) / data.length,
    };

    // Mettre à jour le tableau
    const tableBody = document.querySelector('.table-bordered tbody');
    const newRow = `
                    <tr>
                        <td><strong>Averages</strong></td>
                        <td><span class="status-badge bg-info"></span> ${averages.output.toFixed(2)}</td>
                        <td><span class="status-badge bg-info"></span> ${averages.uptime.toFixed(2)}%</td>
                        <td><span class="status-badge bg-info"></span> ${averages.unplanned_dt.toFixed(2)}%</td>
                        <td><span class="status-badge bg-info"></span> ${averages.planned_dt.toFixed(2)}%</td>
                        <td><span class="status-badge bg-info"></span> ${averages.setup_time.toFixed(2)}%</td>
                        <td>00:00:00</td>
                        <td>${averages.pmh.toFixed(2)}</td>
                        <td>${averages.ftq.toFixed(2)}%</td>
                        <td>${averages.throughput.toFixed(2)}</td>
                        <td>00:00:00</td>
                        <td>${averages.compliance_setup.toFixed(2)}%</td>
                    </tr>
                `;
    tableBody.innerHTML += newRow;

  } catch (error) {
    console.error('Error loading or processing data:', error);
  }
}

// ===== BARCODE SCANNER FUNCTIONS =====

function initBarcodeScanner() {
  const scannerInput = document.getElementById('scannerInput');
  if (scannerInput) {
    // Auto-focus on scanner input
    scannerInput.focus();

    // Handle Enter key press
    scannerInput.addEventListener('keypress', function (e) {
      if (e.key === 'Enter') {
        searchBarcode();
      }
    });

    // Auto-search after a short delay when typing stops
    let searchTimeout;
    scannerInput.addEventListener('input', function () {
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        if (scannerInput.value.length > 3) {
          searchBarcode();
        }
      }, 500);
    });
  }

  // Load recent scans on page load
  displayRecentScans();
}

async function searchBarcode() {
  const scannerInput = document.getElementById('scannerInput');
  const resultDiv = document.getElementById('scannerResult');

  if (!scannerInput || !resultDiv) return;

  const code = scannerInput.value.trim();
  if (!code) {
    resultDiv.style.display = 'none';
    return;
  }

  // Show loading state
  resultDiv.innerHTML = `
    <div class="text-center p-3">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Recherche en cours...</span>
      </div>
      <p class="mt-2">Recherche du code: <strong>${code}</strong></p>
    </div>
  `;
  resultDiv.style.display = 'block';

  try {
    // Search in the database
    const result = await searchBarcode(code);

    if (result) {
      // Record the scan
      await recordBarcodeScan(code, 'Current User', 'Search', 'Web Scanner');

      // Display the result
      displayBarcodeResult(result);

      // Update recent scans
      displayRecentScans();
    } else {
      // No result found
      resultDiv.innerHTML = `
        <div class="alert alert-warning">
          <h6><i class="fas fa-exclamation-triangle me-2"></i>Code non trouvé</h6>
          <p class="mb-0">Le code <strong>${code}</strong> n'a pas été trouvé dans la base de données.</p>
          <small class="text-muted">Vérifiez que le code est correct ou contactez l'administrateur.</small>
        </div>
      `;
    }
  } catch (error) {
    console.error('Erreur lors de la recherche:', error);
    resultDiv.innerHTML = `
      <div class="alert alert-danger">
        <h6><i class="fas fa-times me-2"></i>Erreur de recherche</h6>
        <p class="mb-0">Une erreur s'est produite lors de la recherche.</p>
        <small class="text-muted">${error.message}</small>
      </div>
    `;
  }
}

function displayBarcodeResult(result) {
  const resultDiv = document.getElementById('scannerResult');
  if (!resultDiv) return;

  // Determine the section type for styling
  const sectionInfo = getSectionInfo(result.section);

  // Format the result display
  let html = `
    <div class="alert alert-success">
      <div class="d-flex justify-content-between align-items-start">
        <div>
          <h6><i class="${sectionInfo.icon} me-2"></i>${result.name || result.code}</h6>
          <p class="mb-1"><strong>Code:</strong> ${result.code}</p>
          <p class="mb-1"><strong>Format:</strong> <span class="badge bg-secondary">${result.format}</span></p>
          <p class="mb-1"><strong>Catégorie:</strong> ${result.category || 'N/A'}</p>
          <p class="mb-1"><strong>Section:</strong> <span class="badge ${sectionInfo.badgeClass}">${sectionInfo.name}</span></p>
        </div>
        <div class="text-end">
          <small class="text-muted">Trouvé dans ${sectionInfo.name}</small>
        </div>
      </div>
    </div>

    <div class="card mt-3">
      <div class="card-header">
        <h6 class="mb-0">Détails du produit</h6>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            ${result.description ? `<p><strong>Description:</strong><br>${result.description}</p>` : ''}
            ${result.supplier ? `<p><strong>Fournisseur:</strong> ${result.supplier}</p>` : ''}
            ${result.location ? `<p><strong>Emplacement:</strong> ${result.location}</p>` : ''}
            ${result.machine ? `<p><strong>Machine:</strong> ${result.machine}</p>` : ''}
          </div>
          <div class="col-md-6">
            ${result.price ? `<p><strong>Prix:</strong> ${result.price}€</p>` : ''}
            ${result.stock ? `<p><strong>Stock:</strong> ${result.stock}</p>` : ''}
            ${result.minStock ? `<p><strong>Stock minimum:</strong> ${result.minStock}</p>` : ''}
            ${result.order ? `<p><strong>Ordre:</strong> ${result.order}</p>` : ''}
            ${result.remaining ? `<p><strong>Reste:</strong> ${result.remaining}</p>` : ''}
            ${result.status ? `<p><strong>Statut:</strong> <span class="badge ${getStatusBadgeClass(result.status)}">${result.status}</span></p>` : ''}
          </div>
        </div>

        ${result.availableActions ? `
          <div class="mt-3">
            <strong>Actions disponibles:</strong>
            <div class="mt-2">
              ${result.availableActions.split(' ').map(action =>
    `<button class="btn btn-outline-primary btn-sm me-2" onclick="performAction('${result.code}', '${action}')">${action}</button>`
  ).join('')}
            </div>
          </div>
        ` : ''}

        <div class="mt-3 d-flex justify-content-between align-items-center">
          <small class="text-muted">
            Dernière mise à jour: ${result.lastUpdated ? new Date(result.lastUpdated).toLocaleString('fr-FR') : 'N/A'}
          </small>
          <button class="btn btn-outline-secondary btn-sm" onclick="printScanResult('${result.code}')">
            <i class="fas fa-print me-1"></i> Imprimer
          </button>
        </div>
      </div>
    </div>
  `;

  resultDiv.innerHTML = html;
}

function getSectionInfo(section) {
  const sectionMap = {
    'aptivProducts': { name: 'Produits Aptiv', icon: 'fas fa-industry', badgeClass: 'bg-primary' },
    'standardProducts': { name: 'Produits Standards', icon: 'fas fa-barcode', badgeClass: 'bg-info' },
    'upcProducts': { name: 'Produits UPC', icon: 'fas fa-globe-americas', badgeClass: 'bg-warning' },
    'internalCodes': { name: 'Codes Internes', icon: 'fas fa-cogs', badgeClass: 'bg-success' },
    'packagingCodes': { name: 'Codes Emballage', icon: 'fas fa-box', badgeClass: 'bg-secondary' },
    'testData': { name: 'Données Test', icon: 'fas fa-flask', badgeClass: 'bg-danger' }
  };

  return sectionMap[section] || { name: 'Inconnu', icon: 'fas fa-question', badgeClass: 'bg-dark' };
}

function getStatusBadgeClass(status) {
  const statusMap = {
    'Active': 'bg-success',
    'Pending': 'bg-warning',
    'Completed': 'bg-info',
    'Failed': 'bg-danger',
    'Scanned': 'bg-primary',
    'Processing': 'bg-warning',
    'Dispatched': 'bg-info',
    'Operational': 'bg-success',
    'Maintenance Required': 'bg-warning',
    'Certified': 'bg-success',
    'Rejected': 'bg-danger'
  };

  return statusMap[status] || 'bg-secondary';
}

function displayRecentScans() {
  const recentScansDiv = document.getElementById('recentScans');
  if (!recentScansDiv) return;

  const scans = getScanHistory().slice(-5).reverse(); // Get last 5 scans

  if (scans.length === 0) {
    recentScansDiv.innerHTML = '<p class="text-muted">Aucun scan récent</p>';
    return;
  }

  const html = scans.map(scan => `
    <div class="d-flex justify-content-between align-items-center border-bottom py-2">
      <div>
        <strong>${scan.code}</strong>
        <small class="text-muted d-block">${scan.action} - ${new Date(scan.timestamp).toLocaleString('fr-FR')}</small>
      </div>
      <button class="btn btn-sm btn-outline-primary" onclick="searchSpecificCode('${scan.code}')">
        <i class="fas fa-search"></i>
      </button>
    </div>
  `).join('');

  recentScansDiv.innerHTML = html;
}

function searchSpecificCode(code) {
  const scannerInput = document.getElementById('scannerInput');
  if (scannerInput) {
    scannerInput.value = code;
    searchBarcode();
  }
}

function performAction(code, action) {
  // This would typically send a request to the server
  console.log(`Performing action "${action}" on code "${code}"`);

  // Show a confirmation message
  const alertDiv = document.createElement('div');
  alertDiv.className = 'alert alert-info alert-dismissible fade show mt-3';
  alertDiv.innerHTML = `
    <strong>Action effectuée:</strong> ${action} sur le code ${code}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;

  const resultDiv = document.getElementById('scannerResult');
  if (resultDiv) {
    resultDiv.appendChild(alertDiv);
  }

  // Record the action
  recordBarcodeScan(code, 'Current User', action, 'Web Scanner');
}

// ===== PRINT FUNCTIONS =====

async function printScanResult(code) {
  try {
    // Get the current scan result data
    const result = await searchBarcode(code);
    if (!result) {
      alert('Impossible d\'imprimer: données non trouvées pour le code ' + code);
      return;
    }

    // Create print content
    const printContent = generatePrintContent(result);

    // Create a new window for printing
    const printWindow = window.open('', '_blank', 'width=800,height=600');

    if (!printWindow) {
      alert('Impossible d\'ouvrir la fenêtre d\'impression. Vérifiez que les pop-ups sont autorisés.');
      return;
    }

    // Write the content to the print window
    printWindow.document.write(printContent);
    printWindow.document.close();

    // Wait for content to load then print
    printWindow.onload = function () {
      printWindow.focus();
      printWindow.print();

      // Close the window after printing (optional)
      setTimeout(() => {
        printWindow.close();
      }, 1000);
    };

    // Record the print action
    recordBarcodeScan(code, 'Current User', 'Print', 'Web Scanner');

  } catch (error) {
    console.error('Erreur lors de l\'impression:', error);
    alert('Erreur lors de l\'impression: ' + error.message);
  }
}

function generatePrintContent(result) {
  const sectionInfo = getSectionInfo(result.section);
  const currentDate = new Date().toLocaleString('fr-FR');

  return `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Rapport de Scan - ${result.code}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 20px;
          color: #333;
          line-height: 1.6;
        }
        .header {
          text-align: center;
          border-bottom: 2px solid #007bff;
          padding-bottom: 20px;
          margin-bottom: 30px;
        }
        .company-logo {
          font-size: 24px;
          font-weight: bold;
          color: #007bff;
          margin-bottom: 10px;
        }
        .report-title {
          font-size: 20px;
          margin: 10px 0;
        }
        .scan-info {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        .code-display {
          font-size: 24px;
          font-weight: bold;
          text-align: center;
          background-color: #fff;
          padding: 15px;
          border: 2px solid #007bff;
          border-radius: 8px;
          margin: 20px 0;
          letter-spacing: 2px;
        }
        .details-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;
          margin: 20px 0;
        }
        .detail-item {
          margin-bottom: 10px;
        }
        .detail-label {
          font-weight: bold;
          color: #555;
        }
        .detail-value {
          margin-left: 10px;
        }
        .section-badge {
          display: inline-block;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: bold;
          color: white;
          background-color: #007bff;
        }
        .status-badge {
          display: inline-block;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: bold;
          color: white;
        }
        .status-active { background-color: #28a745; }
        .status-pending { background-color: #ffc107; color: #000; }
        .status-completed { background-color: #17a2b8; }
        .status-scanned { background-color: #007bff; }
        .footer {
          margin-top: 40px;
          padding-top: 20px;
          border-top: 1px solid #ddd;
          text-align: center;
          font-size: 12px;
          color: #666;
        }
        .barcode-section {
          text-align: center;
          margin: 30px 0;
          padding: 20px;
          background-color: #f8f9fa;
          border-radius: 8px;
        }
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="company-logo">APTIV - Twisting Monitoring Tool</div>
        <div class="report-title">Rapport de Scan de Code-Barres</div>
        <div>Date: ${currentDate}</div>
      </div>

      <div class="scan-info">
        <h3>Informations du Code Scanné</h3>

        <div class="code-display">
          ${result.code}
        </div>

        <div class="details-grid">
          <div>
            <div class="detail-item">
              <span class="detail-label">Nom:</span>
              <span class="detail-value">${result.name || 'N/A'}</span>
            </div>

            <div class="detail-item">
              <span class="detail-label">Format:</span>
              <span class="detail-value">${result.format}</span>
            </div>

            <div class="detail-item">
              <span class="detail-label">Catégorie:</span>
              <span class="detail-value">${result.category || 'N/A'}</span>
            </div>

            <div class="detail-item">
              <span class="detail-label">Section:</span>
              <span class="detail-value">
                <span class="section-badge">${sectionInfo.name}</span>
              </span>
            </div>

            ${result.supplier ? `
            <div class="detail-item">
              <span class="detail-label">Fournisseur:</span>
              <span class="detail-value">${result.supplier}</span>
            </div>
            ` : ''}

            ${result.location || result.machine ? `
            <div class="detail-item">
              <span class="detail-label">Emplacement:</span>
              <span class="detail-value">${result.location || result.machine || 'N/A'}</span>
            </div>
            ` : ''}
          </div>

          <div>
            ${result.price ? `
            <div class="detail-item">
              <span class="detail-label">Prix:</span>
              <span class="detail-value">${result.price}€</span>
            </div>
            ` : ''}

            ${result.stock || result.totalStock ? `
            <div class="detail-item">
              <span class="detail-label">Stock:</span>
              <span class="detail-value">${result.stock || result.totalStock}</span>
            </div>
            ` : ''}

            ${result.minStock || result.quantityPerBox ? `
            <div class="detail-item">
              <span class="detail-label">Stock minimum / Qt par boîte:</span>
              <span class="detail-value">${result.minStock || result.quantityPerBox}</span>
            </div>
            ` : ''}

            ${result.order || result.ordre ? `
            <div class="detail-item">
              <span class="detail-label">Ordre:</span>
              <span class="detail-value">${result.order || result.ordre}</span>
            </div>
            ` : ''}

            ${result.remaining ? `
            <div class="detail-item">
              <span class="detail-label">Reste:</span>
              <span class="detail-value">${result.remaining}</span>
            </div>
            ` : ''}

            ${result.apnCable ? `
            <div class="detail-item">
              <span class="detail-label">APN-Cable:</span>
              <span class="detail-value">${result.apnCable}</span>
            </div>
            ` : ''}

            <div class="detail-item">
              <span class="detail-label">Statut:</span>
              <span class="detail-value">
                <span class="status-badge ${getStatusClass(result.status)}">${result.status || 'N/A'}</span>
              </span>
            </div>
          </div>
        </div>

        ${result.description ? `
        <div style="margin-top: 20px;">
          <div class="detail-label">Description:</div>
          <div style="margin-top: 5px; padding: 10px; background-color: white; border-radius: 4px;">
            ${result.description}
          </div>
        </div>
        ` : ''}

        ${result.availableActions ? `
        <div style="margin-top: 20px;">
          <div class="detail-label">Actions disponibles:</div>
          <div style="margin-top: 5px;">
            ${result.availableActions}
          </div>
        </div>
        ` : ''}
      </div>

      <div class="barcode-section">
        <h4>Code-Barres</h4>
        <div style="font-family: 'Courier New', monospace; font-size: 18px; letter-spacing: 3px; margin: 10px 0;">
          ${result.code}
        </div>
        <div style="font-size: 12px; color: #666;">
          Format: ${result.format}
        </div>
      </div>

      <div class="footer">
        <p>Rapport généré le ${currentDate}</p>
        <p>APTIV - Twisting Monitoring Tool - Scanner de Code-Barres</p>
        <p>Dernière mise à jour des données: ${result.lastUpdated ? new Date(result.lastUpdated).toLocaleString('fr-FR') : 'N/A'}</p>
      </div>
    </body>
    </html>
  `;
}

function getStatusClass(status) {
  const statusMap = {
    'Active': 'status-active',
    'Pending': 'status-pending',
    'Completed': 'status-completed',
    'Scanned': 'status-scanned'
  };
  return statusMap[status] || 'status-active';
}

function printAllRecentScans() {
  const scans = getScanHistory();
  if (scans.length === 0) {
    alert('Aucun scan récent à imprimer.');
    return;
  }

  const printContent = generateRecentScansPrintContent(scans);

  const printWindow = window.open('', '_blank', 'width=800,height=600');
  if (!printWindow) {
    alert('Impossible d\'ouvrir la fenêtre d\'impression. Vérifiez que les pop-ups sont autorisés.');
    return;
  }

  printWindow.document.write(printContent);
  printWindow.document.close();

  printWindow.onload = function () {
    printWindow.focus();
    printWindow.print();
    setTimeout(() => {
      printWindow.close();
    }, 1000);
  };
}

function generateRecentScansPrintContent(scans) {
  const currentDate = new Date().toLocaleString('fr-FR');

  const scansHtml = scans.slice(-20).reverse().map(scan => `
    <tr>
      <td>${scan.code}</td>
      <td>${scan.action}</td>
      <td>${new Date(scan.timestamp).toLocaleString('fr-FR')}</td>
      <td>${scan.user}</td>
      <td>${scan.location}</td>
    </tr>
  `).join('');

  return `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <title>Historique des Scans</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
      </style>
    </head>
    <body>
      <div class="header">
        <h2>APTIV - Historique des Scans</h2>
        <p>Rapport généré le ${currentDate}</p>
      </div>

      <table>
        <thead>
          <tr>
            <th>Code</th>
            <th>Action</th>
            <th>Date/Heure</th>
            <th>Utilisateur</th>
            <th>Emplacement</th>
          </tr>
        </thead>
        <tbody>
          ${scansHtml}
        </tbody>
      </table>

      <div class="footer">
        <p>Total des scans: ${scans.length}</p>
        <p>APTIV - Twisting Monitoring Tool</p>
      </div>
    </body>
    </html>
  `;
}
