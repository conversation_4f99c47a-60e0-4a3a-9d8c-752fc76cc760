// Configuration globale
const CONFIG = {
  colors: {
    success: '#20c997',
    warning: '#ffc107',
    danger: '#dc3545',
    primary: '#4e73df',
    secondary: '#6c757d'
  },
  productionTarget: 1000,
  endShiftTarget: 1200
};

// Configuration des sons
const SOUND_CONFIG = {
  enabled: true,
  volume: 0.3
};

// Sons spécifiques pour différentes actions
const SOUNDS = {
  click: () => {
    try { createBeepSound(600, 80, 'sine'); } catch (e) { console.log('Sound error:', e); }
  },
  hover: () => {
    try { createBeepSound(400, 50, 'triangle'); } catch (e) { console.log('Sound error:', e); }
  },
  success: () => {
    try {
      createBeepSound(523, 100, 'sine'); // Do
      setTimeout(() => createBeepSound(659, 100, 'sine'), 100); // Mi
      setTimeout(() => createBeepSound(784, 150, 'sine'), 200); // Sol
    } catch (e) { console.log('Sound error:', e); }
  },
  error: () => {
    try {
      createBeepSound(300, 100, 'sawtooth');
      setTimeout(() => createBeepSound(250, 100, 'sawtooth'), 100);
    } catch (e) { console.log('Sound error:', e); }
  },
  navigation: () => {
    try { createBeepSound(500, 60, 'square'); } catch (e) { console.log('Sound error:', e); }
  }
};

// Éléments DOM fréquemment utilisés
const DOM = {
  yearElement: document.getElementById('current-year'),
  productionCanvas: document.getElementById('productionChart'),
  hourlyProductionCanvas: document.getElementById('hourlyProductionChart'),
  machineInfoBody: document.getElementById('machineInfoBody')
};

// Initialisation de l'application
document.addEventListener('DOMContentLoaded', function () {
  // Attendre que Chart.js soit chargé
  if (typeof Chart === 'undefined') {
    console.error('Chart.js not loaded');
    return;
  }

  initApp();
});

function initApp() {
  try {
    // Initialiser l'année dans le footer
    if (DOM.yearElement) {
      DOM.yearElement.textContent = new Date().getFullYear();
    }

    // Initialiser les tooltips
    initTooltips();

    // Initialiser les graphiques avec vérification
    if (DOM.productionCanvas && typeof Chart !== 'undefined') {
      initProductionChart();
    }

    if (DOM.hourlyProductionCanvas && typeof Chart !== 'undefined') {
      initHourlyProductionChart();
    }

    // Initialiser la table des machines
    if (DOM.machineInfoBody) {
      displayMachines();
    }

    // Initialiser les fonctionnalités interactives
    initInteractiveFeatures();

    // Initialiser les écouteurs d'événements
    initEventListeners();

    console.log('Application initialized successfully');
  } catch (error) {
    console.error('Error initializing application:', error);
  }
}

function initTooltips() {
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.forEach(tooltipTriggerEl => {
    new bootstrap.Tooltip(tooltipTriggerEl);
  });
}

function initEventListeners() {
  try {
    // Écouteur pour la sélection multiple avec Ctrl+clic
    document.addEventListener('click', function (event) {
      if (event.ctrlKey && event.target.closest('#productionChart')) {
        event.preventDefault();
      }
    });

    // Écouteur pour les raccourcis clavier
    document.addEventListener('keydown', function (event) {
      handleKeyboardShortcuts(event);
    });

    console.log('Event listeners initialized');
  } catch (error) {
    console.error('Error initializing event listeners:', error);
  }
}

function initInteractiveFeatures() {
  try {
    // Initialiser les favoris
    initializeFavorites();

    // Initialiser les raccourcis clavier
    initializeKeyboardShortcuts();

    // Charger les préférences sonores
    loadSoundPreferences();

    // Mettre à jour le bouton de contrôle des sons
    updateSoundControlButton();

    console.log('Interactive features initialized');
  } catch (error) {
    console.error('Error initializing interactive features:', error);
  }
}

// Gestionnaire des raccourcis clavier
function handleKeyboardShortcuts(event) {
  try {
    // Éviter les conflits avec les champs de saisie
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
      return;
    }

    switch (event.key) {
      case 'ArrowLeft':
        navigateChart('left');
        event.preventDefault();
        break;
      case 'ArrowRight':
        navigateChart('right');
        event.preventDefault();
        break;
      case 'Enter':
      case ' ':
        if (selectedMachineIndex >= 0) {
          const machineName = allMachineData.labels[selectedMachineIndex];
          showMachineDetails(machineName);
          SOUNDS.click();
          hapticFeedback('medium');
        }
        event.preventDefault();
        break;
      case 'f':
      case 'F':
        if (event.ctrlKey && selectedMachineIndex >= 0) {
          const machineName = allMachineData.labels[selectedMachineIndex];
          toggleFavoriteMachine(machineName);
          event.preventDefault();
        }
        break;
      case 's':
      case 'S':
        if (event.ctrlKey) {
          toggleSounds();
          event.preventDefault();
        }
        break;
    }
  } catch (error) {
    console.error('Error handling keyboard shortcuts:', error);
  }
}

// Navigation du graphique au clavier
function navigateChart(direction) {
  try {
    if (!allMachineData.labels.length) return;

    if (direction === 'left') {
      selectedMachineIndex = selectedMachineIndex <= 0 ?
        allMachineData.labels.length - 1 : selectedMachineIndex - 1;
    } else if (direction === 'right') {
      selectedMachineIndex = selectedMachineIndex >= allMachineData.labels.length - 1 ?
        0 : selectedMachineIndex + 1;
    }

    highlightSelectedMachine(selectedMachineIndex);
    SOUNDS.navigation();
    hapticFeedback('light');
  } catch (error) {
    console.error('Error navigating chart:', error);
  }
}

// Données de production par machine (globales pour le filtrage)
const allMachineData = {
  labels: ['T10 ', 'T11 ', 'T12 ', 'T13 ', 'T14 ', 'T15 ', 'T16 ', 'T17 '],
  production: [950, 1100, 800, 1200, 650, 900, 1000, 1300],
  details: {
    'T10 ': {
      production: 950, target: 1000, efficiency: 95, status: 'En service',
      shift: getCurrentShift(), operator: 'Jean Dupont', shiftStart: '06:00', shiftEnd: '14:00'
    },
    'T-11 ': {
      production: 1100, target: 1000, efficiency: 110, status: 'En service',
      shift: getCurrentShift(), operator: 'Marie Martin', shiftStart: '06:00', shiftEnd: '14:00'
    },
    'T-12 ': {
      production: 800, target: 1000, efficiency: 80, status: 'Maintenance',
      shift: 'Nuit', operator: 'Pierre Durand', shiftStart: '22:00', shiftEnd: '06:00'
    },
    'T-13 ': {
      production: 1200, target: 1000, efficiency: 120, status: 'En service',
      shift: getCurrentShift(), operator: 'Sophie Leroy', shiftStart: '06:00', shiftEnd: '14:00'
    },
    'T-14 ': {
      production: 650, target: 1000, efficiency: 65, status: 'En service',
      shift: 'Nuit', operator: 'Michel Bernard', shiftStart: '22:00', shiftEnd: '06:00'
    },
    'T-15 ': {
      production: 900, target: 1000, efficiency: 90, status: 'En service',
      shift: getCurrentShift(), operator: 'Anne Moreau', shiftStart: '06:00', shiftEnd: '14:00'
    },
    'T16 ': {
      production: 1000, target: 1000, efficiency: 100, status: 'En service',
      shift: 'Nuit', operator: 'Laurent Petit', shiftStart: '22:00', shiftEnd: '06:00'
    },
    'T17 ': {
      production: 1300, target: 1000, efficiency: 130, status: 'En service',
      shift: getCurrentShift(), operator: 'Isabelle Roux', shiftStart: '06:00', shiftEnd: '14:00'
    }
  }
};

let productionChart = null;

function getCurrentShift() {
  const now = new Date();
  const currentHour = now.getHours();

  if (currentHour >= 6 && currentHour < 14) {
    return 'Jour';
  } else if (currentHour >= 14 && currentHour < 22) {
    return 'Après-midi';
  } else {
    return 'Nuit';
  }
}

function initProductionChart(selectedMachine = null) {
  try {
    const productionCtx = DOM.productionCanvas.getContext('2d');

    // Détruire le graphique existant s'il existe
    if (productionChart) {
      productionChart.destroy();
    }

    let machineLabels, productionData;

    if (selectedMachine && selectedMachine !== 'all') {
      // Afficher seulement la machine sélectionnée
      machineLabels = [selectedMachine];
      const machineIndex = allMachineData.labels.indexOf(selectedMachine);
      productionData = [allMachineData.production[machineIndex]];
    } else {
      // Afficher toutes les machines
      machineLabels = allMachineData.labels;
      productionData = allMachineData.production;
    }

    // Calcul des couleurs en fonction du pourcentage par rapport à la cible
    const backgroundColors = productionData.map(quantity => {
      const percentage = (quantity / CONFIG.productionTarget) * 100;
      if (percentage >= 100) return CONFIG.colors.success;  // Vert si objectif atteint
      if (percentage >= 80) return CONFIG.colors.warning;   // Jaune si proche de l'objectif
      return CONFIG.colors.danger;                          // Rouge si en dessous de 80%
    });

    // Couleurs de survol pour les barres
    const hoverBackgroundColors = backgroundColors.map(color => {
      // Augmenter la luminosité pour l'effet hover
      if (color === CONFIG.colors.success) return '#28a745';
      if (color === CONFIG.colors.warning) return '#e0a800';
      if (color === CONFIG.colors.danger) return '#c82333';
      return color;
    });

    // Création du graphique
    productionChart = new Chart(productionCtx, {
      type: 'bar',
      data: {
        labels: machineLabels,
        datasets: [{
          label: 'Production',
          data: productionData,
          backgroundColor: backgroundColors,
          borderColor: backgroundColors.map(color => {
            // Create solid border colors
            if (color === CONFIG.colors.success) return '#1e7e34';
            if (color === CONFIG.colors.warning) return '#d39e00';
            if (color === CONFIG.colors.danger) return '#bd2130';
            return color.replace('0.6', '1');
          }),
          borderWidth: 2,
          borderRadius: {
            topLeft: 6,
            topRight: 6,
            bottomLeft: 2,
            bottomRight: 2
          },
          borderSkipped: false,
          hoverBackgroundColor: hoverBackgroundColors,
          hoverBorderColor: hoverBackgroundColors.map(color => {
            // Enhanced hover border colors
            if (color === '#28a745') return '#1e7e34';
            if (color === '#e0a800') return '#d39e00';
            if (color === '#c82333') return '#bd2130';
            return color;
          }),
          hoverBorderWidth: 4,
          barPercentage: 0.75,
          categoryPercentage: 0.85,
          // Add shadow effect
          shadowOffsetX: 2,
          shadowOffsetY: 2,
          shadowBlur: 4,
          shadowColor: 'rgba(0, 0, 0, 0.2)'
        }]
      },
      options: {
        ...getChartOptions(),
        onClick: (event, elements) => {
          if (elements.length > 0) {
            const elementIndex = elements[0].index;
            const clickedMachine = machineLabels[elementIndex];

            // Vérifier si Ctrl est pressé pour la sélection multiple
            if (event.native && event.native.ctrlKey) {
              toggleMachineSelection(clickedMachine);
              showAlert(`${clickedMachine} ${selectedMachines.includes(clickedMachine) ? 'ajouté à' : 'retiré de'} la sélection`, 'info');
            } else {
              // Clic normal - afficher les détails
              showMachineDetails(clickedMachine);

              // Ajouter un effet visuel de sélection
              highlightSelectedMachine(elementIndex);

              // Effets sonores et haptiques
              SOUNDS.click();
              hapticFeedback('medium');
            }
          }
        },
        onHover: (event, elements) => {
          // Changer le curseur lors du survol
          event.native.target.style.cursor = elements.length > 0 ? 'pointer' : 'default';

          // Afficher tooltip personnalisé et effets visuels
          if (elements.length > 0) {
            const elementIndex = elements[0].index;
            const machineName = machineLabels[elementIndex];
            const production = productionData[elementIndex];

            // Afficher tooltip personnalisé
            showCustomTooltip(event, machineName, production);

            // Ajouter effet de survol à la barre
            addBarHoverEffect(elementIndex, true);

            // Son de survol
            SOUNDS.hover();
            hapticFeedback('light');
          } else {
            hideCustomTooltip();
            // Retirer tous les effets de survol
            removeAllBarHoverEffects();
          }
        }
      }
    });

    // Afficher les détails si une machine spécifique est sélectionnée
    if (selectedMachine && selectedMachine !== 'all') {
      showMachineDetails(selectedMachine);
    }

    return productionChart;
  } catch (error) {
    console.error('Erreur lors de l\'initialisation du graphique de production:', error);
    return null;
  }
}

function getChartOptions() {
  return {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'nearest',
      intersect: true
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Quantité Produite',
          font: { weight: 'bold', size: 14 }
        },
        grid: {
          drawBorder: false,
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          callback: value => value.toLocaleString(),
          font: { size: 12 }
        }
      },
      x: {
        grid: { display: false },
        ticks: {
          font: { size: 12, weight: 'bold' }
        }
      }
    },
    plugins: {
      legend: {
        display: false // Masquer la légende par défaut car nous avons une légende personnalisée
      },
      tooltip: {
        enabled: false // Désactiver les tooltips par défaut car nous utilisons des tooltips personnalisés
      }
    },
    animation: {
      duration: 1000,
      easing: 'easeInOutQuart',
      delay: (context) => {
        // Stagger animation for each bar
        return context.dataIndex * 100;
      },
      onComplete: function () {
        // Animation terminée, ajouter des indicateurs visuels
        try {
          addMachineStatusIndicators();
          addValueLabelsToChart();
        } catch (error) {
          console.log('Error adding status indicators:', error);
        }
      },
      onProgress: function (animation) {
        // Add subtle glow effect during animation
        const chart = animation.chart;
        if (chart && chart.canvas) {
          chart.canvas.style.filter = `brightness(${1 + animation.currentStep * 0.1})`;
        }
      }
    },
    elements: {
      bar: {
        borderRadius: {
          topLeft: 6,
          topRight: 6,
          bottomLeft: 2,
          bottomRight: 2
        },
        borderSkipped: false
      }
    },
    // Enhanced responsive design
    responsive: true,
    maintainAspectRatio: false,
    layout: {
      padding: {
        top: 40,
        bottom: 10,
        left: 10,
        right: 10
      }
    }
  };
}

// Fonction pour initialiser les tooltips
function initTooltips() {
  try {
    // Initialiser les tooltips Bootstrap si disponible
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
      const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
      tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
      });
    }
  } catch (error) {
    console.log('Error initializing tooltips:', error);
  }
}

// Fonction pour afficher les machines dans le tableau
function displayMachines() {
  try {
    if (!DOM.machineInfoBody) return;

    DOM.machineInfoBody.innerHTML = '';

    allMachineData.labels.forEach((machineName) => {
      const machineInfo = allMachineData.details[machineName];
      if (!machineInfo) return;

      const row = document.createElement('tr');
      row.innerHTML = `
        <td><strong>${machineName}</strong></td>
        <td>${machineInfo.production.toLocaleString()}</td>
        <td>${machineInfo.target.toLocaleString()}</td>
        <td><span class="badge ${getStatusBadgeClass(machineInfo.status)}">${machineInfo.status}</span></td>
        <td><span class="${getEfficiencyClass(machineInfo.efficiency)}">${machineInfo.efficiency}%</span></td>
        <td>
          <button class="btn btn-sm btn-outline-primary" onclick="showMachineDetails('${machineName}')">
            <i class="fas fa-eye"></i> Détails
          </button>
        </td>
      `;
      DOM.machineInfoBody.appendChild(row);
    });
  } catch (error) {
    console.error('Error displaying machines:', error);
  }
}

// Fonction pour initialiser le graphique de production horaire
function initHourlyProductionChart() {
  try {
    const hourlyCtx = DOM.hourlyProductionCanvas.getContext('2d');

    // Données d'exemple pour la production horaire
    const hourlyData = {
      labels: ['8h', '9h', '10h', '11h', '12h', '13h', '14h', '15h', '16h', '17h'],
      datasets: [{
        label: 'Production Horaire',
        data: [120, 150, 180, 160, 140, 170, 190, 200, 180, 160],
        backgroundColor: 'rgba(78, 115, 223, 0.6)',
        borderColor: 'rgba(78, 115, 223, 1)',
        borderWidth: 2,
        fill: true
      }]
    };

    new Chart(hourlyCtx, {
      type: 'line',
      data: hourlyData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Production (unités/heure)'
            }
          },
          x: {
            title: {
              display: true,
              text: 'Heure'
            }
          }
        },
        plugins: {
          legend: {
            display: true,
            position: 'top'
          }
        }
      }
    });
  } catch (error) {
    console.error('Erreur lors de l\'initialisation du graphique horaire:', error);
  }
}

// Fonction pour ajouter des étiquettes de valeur personnalisées au graphique
function addValueLabelsToChart() {
  if (!productionChart) return;

  try {
    const canvas = document.getElementById('productionChart');
    if (!canvas) return;

    // Créer un overlay pour les étiquettes de valeur
    let valueOverlay = document.getElementById('chart-value-overlay');
    if (!valueOverlay) {
      valueOverlay = document.createElement('div');
      valueOverlay.id = 'chart-value-overlay';
      valueOverlay.className = 'chart-value-overlay';
      valueOverlay.style.position = 'absolute';
      valueOverlay.style.top = '0';
      valueOverlay.style.left = '0';
      valueOverlay.style.width = '100%';
      valueOverlay.style.height = '100%';
      valueOverlay.style.pointerEvents = 'none';
      valueOverlay.style.zIndex = '10';
      canvas.parentNode.style.position = 'relative';
      canvas.parentNode.appendChild(valueOverlay);
    }

    // Vider l'overlay existant
    valueOverlay.innerHTML = '';

    // Ajouter des étiquettes pour chaque barre
    const meta = productionChart.getDatasetMeta(0);

    meta.data.forEach((bar, index) => {
      const value = productionChart.data.datasets[0].data[index];
      const percentage = (value / CONFIG.productionTarget) * 100;

      // Créer l'étiquette de valeur
      const valueLabel = document.createElement('div');
      valueLabel.className = 'chart-value-label';
      valueLabel.style.position = 'absolute';
      valueLabel.style.left = (bar.x - 30) + 'px';
      valueLabel.style.top = (bar.y - 25) + 'px';
      valueLabel.style.width = '60px';
      valueLabel.style.textAlign = 'center';
      valueLabel.style.fontSize = '12px';
      valueLabel.style.fontWeight = 'bold';
      valueLabel.style.padding = '4px 6px';
      valueLabel.style.borderRadius = '4px';
      valueLabel.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
      valueLabel.style.border = '1px solid';
      valueLabel.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
      valueLabel.style.transition = 'all 0.3s ease';

      // Couleur basée sur la performance
      if (percentage >= 100) {
        valueLabel.style.color = '#1e7e34';
        valueLabel.style.borderColor = '#28a745';
      } else if (percentage >= 80) {
        valueLabel.style.color = '#d39e00';
        valueLabel.style.borderColor = '#ffc107';
      } else {
        valueLabel.style.color = '#bd2130';
        valueLabel.style.borderColor = '#dc3545';
      }

      valueLabel.textContent = value.toLocaleString();
      valueLabel.title = `${percentage.toFixed(1)}% de l'objectif`;

      // Animation d'apparition
      valueLabel.style.opacity = '0';
      valueLabel.style.transform = 'translateY(10px)';

      valueOverlay.appendChild(valueLabel);

      // Animer l'apparition avec un délai
      setTimeout(() => {
        valueLabel.style.opacity = '1';
        valueLabel.style.transform = 'translateY(0)';
      }, index * 100);
    });

  } catch (error) {
    console.log('Error adding value labels:', error);
  }
}

// Fonction pour ajouter un effet de survol à une barre spécifique
function addBarHoverEffect(barIndex, isHovering) {
  if (!productionChart) return;

  try {
    const canvas = document.getElementById('productionChart');
    if (!canvas) return;

    // Créer ou récupérer l'overlay d'effet de survol
    let hoverOverlay = document.getElementById('chart-hover-overlay');
    if (!hoverOverlay) {
      hoverOverlay = document.createElement('div');
      hoverOverlay.id = 'chart-hover-overlay';
      hoverOverlay.className = 'chart-hover-overlay';
      hoverOverlay.style.position = 'absolute';
      hoverOverlay.style.top = '0';
      hoverOverlay.style.left = '0';
      hoverOverlay.style.width = '100%';
      hoverOverlay.style.height = '100%';
      hoverOverlay.style.pointerEvents = 'none';
      hoverOverlay.style.zIndex = '5';
      canvas.parentNode.appendChild(hoverOverlay);
    }

    if (isHovering) {
      const meta = productionChart.getDatasetMeta(0);
      const bar = meta.data[barIndex];

      if (bar) {
        // Créer l'effet de survol
        const hoverEffect = document.createElement('div');
        hoverEffect.className = 'bar-hover-effect';
        hoverEffect.id = `hover-effect-${barIndex}`;
        hoverEffect.style.position = 'absolute';
        hoverEffect.style.left = (bar.x - bar.width / 2 - 5) + 'px';
        hoverEffect.style.top = bar.y + 'px';
        hoverEffect.style.width = (bar.width + 10) + 'px';
        hoverEffect.style.height = (bar.height + 5) + 'px';
        hoverEffect.style.background = 'rgba(0, 123, 255, 0.1)';
        hoverEffect.style.border = '2px solid rgba(0, 123, 255, 0.3)';
        hoverEffect.style.borderRadius = '8px';
        hoverEffect.style.boxShadow = '0 4px 20px rgba(0, 123, 255, 0.2)';
        hoverEffect.style.transition = 'all 0.3s ease';
        hoverEffect.style.transform = 'scale(1.05)';

        hoverOverlay.appendChild(hoverEffect);

        // Animation d'apparition
        setTimeout(() => {
          hoverEffect.style.opacity = '1';
        }, 10);
      }
    } else {
      // Retirer l'effet de survol
      const existingEffect = document.getElementById(`hover-effect-${barIndex}`);
      if (existingEffect) {
        existingEffect.style.opacity = '0';
        setTimeout(() => {
          if (existingEffect.parentNode) {
            existingEffect.remove();
          }
        }, 300);
      }
    }
  } catch (error) {
    console.log('Error adding bar hover effect:', error);
  }
}

// Fonction pour retirer tous les effets de survol
function removeAllBarHoverEffects() {
  try {
    const hoverOverlay = document.getElementById('chart-hover-overlay');
    if (hoverOverlay) {
      const effects = hoverOverlay.querySelectorAll('.bar-hover-effect');
      effects.forEach(effect => {
        effect.style.opacity = '0';
        setTimeout(() => {
          if (effect.parentNode) {
            effect.remove();
          }
        }, 300);
      });
    }
  } catch (error) {
    console.log('Error removing hover effects:', error);
  }
}

// ===== FONCTIONNALITÉ START PRODUCTION PAR HEURE =====

// Variables globales pour la production par heure
let hourlyProductionSessions = [];
let currentHourlySession = null;

// Initialisation de la fonctionnalité Start Production par Heure
function initHourlyProductionControls() {
  const startBtn = document.getElementById('startHourlyProductionBtn');
  const confirmBtn = document.getElementById('confirmStartHourlyProduction');

  if (startBtn) {
    startBtn.addEventListener('click', function () {
      // Pré-remplir l'heure actuelle
      const now = new Date();
      const currentTime = now.toTimeString().slice(0, 5);
      document.getElementById('hourlyStartTime').value = currentTime;

      // Pré-sélectionner l'équipe actuelle
      const currentShift = getCurrentShift();
      document.getElementById('hourlyShiftSelect').value = currentShift;

      updateHourlySummary();
    });
  }

  if (confirmBtn) {
    confirmBtn.addEventListener('click', startHourlyProduction);
  }

  // Écouteurs pour mise à jour du résumé en temps réel
  const formInputs = [
    'hourlyMachineSelect', 'hourlyShiftSelect', 'hourlyTargetQuantity',
    'hourlyDuration', 'hourlyStartTime'
  ];

  formInputs.forEach(inputId => {
    const input = document.getElementById(inputId);
    if (input) {
      input.addEventListener('change', updateHourlySummary);
      input.addEventListener('input', updateHourlySummary);
    }
  });

  // Charger les sessions existantes
  loadHourlyProductionSessions();
}

// Fonction pour démarrer une session de production par heure
function startHourlyProduction() {
  const form = document.getElementById('startHourlyProductionForm');

  // Validation des champs
  const machine = document.getElementById('hourlyMachineSelect').value;
  const shift = document.getElementById('hourlyShiftSelect').value;
  const orderNumber = document.getElementById('hourlyOrderNumber').value;
  const targetQuantity = parseInt(document.getElementById('hourlyTargetQuantity').value);
  const startTime = document.getElementById('hourlyStartTime').value;
  const duration = parseInt(document.getElementById('hourlyDuration').value);
  const operator = document.getElementById('hourlyOperator').value;
  const notes = document.getElementById('hourlyNotes').value;

  if (!machine || !shift || !orderNumber || !targetQuantity || !startTime || !duration || !operator) {
    showAlert('Veuillez remplir tous les champs obligatoires', 'warning');
    return;
  }

  // Créer une nouvelle session
  const session = {
    id: generateSessionId(),
    machine: machine,
    shift: shift,
    orderNumber: orderNumber,
    targetQuantity: targetQuantity,
    startTime: startTime,
    duration: duration,
    operator: operator,
    notes: notes,
    startDate: new Date().toISOString(),
    status: 'active',
    actualProduction: [],
    totalProduced: 0
  };

  // Ajouter la session
  hourlyProductionSessions.push(session);
  currentHourlySession = session;

  // Sauvegarder dans localStorage
  saveHourlyProductionSessions();

  // Démarrer le suivi de production
  startProductionTracking(session);

  // Fermer la modal
  const modal = bootstrap.Modal.getInstance(document.getElementById('startHourlyProductionModal'));
  modal.hide();

  // Réinitialiser le formulaire
  form.reset();

  // Afficher confirmation
  showAlert(`Production démarrée pour ${machine} - Ordre: ${orderNumber}`, 'success');

  // Mettre à jour l'affichage
  updateHourlyProductionDisplay();
}

// Fonction pour générer un ID de session unique
function generateSessionId() {
  return 'session_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
}

// Fonction pour démarrer le suivi de production
function startProductionTracking(session) {
  // Simuler la production en temps réel
  const trackingInterval = setInterval(() => {
    if (session.status !== 'active') {
      clearInterval(trackingInterval);
      return;
    }

    // Simuler une production aléatoire
    const currentHour = new Date().getHours();
    const randomProduction = Math.floor(Math.random() * (session.targetQuantity * 0.3)) +
      Math.floor(session.targetQuantity * 0.7);

    // Ajouter à la production actuelle
    session.actualProduction.push({
      hour: currentHour,
      quantity: randomProduction,
      timestamp: new Date().toISOString()
    });

    session.totalProduced += randomProduction;

    // Sauvegarder
    saveHourlyProductionSessions();

    // Mettre à jour l'affichage
    updateHourlyProductionDisplay();

    // Vérifier si la session est terminée
    const elapsedHours = (Date.now() - new Date(session.startDate).getTime()) / (1000 * 60 * 60);
    if (elapsedHours >= session.duration) {
      session.status = 'completed';
      clearInterval(trackingInterval);
      showAlert(`Session terminée pour ${session.machine} - Total produit: ${session.totalProduced}`, 'info');
    }

  }, 60000); // Mise à jour chaque minute (pour la démo, en production ce serait plus fréquent)
}

// Fonction pour mettre à jour le résumé de configuration
function updateHourlySummary() {
  const machine = document.getElementById('hourlyMachineSelect').value;
  const shift = document.getElementById('hourlyShiftSelect').value;
  const targetQuantity = document.getElementById('hourlyTargetQuantity').value;
  const duration = document.getElementById('hourlyDuration').value;

  // Mettre à jour les éléments du résumé
  document.getElementById('summaryMachine').textContent = machine || '-';
  document.getElementById('summaryShift').textContent = shift || '-';
  document.getElementById('summaryTarget').textContent = targetQuantity || '-';
  document.getElementById('summaryDuration').textContent = duration ? `${duration} heure(s)` : '-';

  // Calculer la production totale prévue
  if (targetQuantity && duration) {
    const totalExpected = parseInt(targetQuantity) * parseInt(duration);
    document.getElementById('summaryTotal').textContent = totalExpected.toLocaleString();
  } else {
    document.getElementById('summaryTotal').textContent = '-';
  }
}

// Fonction pour sauvegarder les sessions dans localStorage
function saveHourlyProductionSessions() {
  try {
    localStorage.setItem('hourlyProductionSessions', JSON.stringify(hourlyProductionSessions));
  } catch (error) {
    console.error('Erreur lors de la sauvegarde des sessions:', error);
  }
}

// Fonction pour charger les sessions depuis localStorage
function loadHourlyProductionSessions() {
  try {
    const saved = localStorage.getItem('hourlyProductionSessions');
    if (saved) {
      hourlyProductionSessions = JSON.parse(saved);

      // Reprendre les sessions actives
      hourlyProductionSessions.forEach(session => {
        if (session.status === 'active') {
          startProductionTracking(session);
        }
      });
    }
  } catch (error) {
    console.error('Erreur lors du chargement des sessions:', error);
    hourlyProductionSessions = [];
  }
}

// Fonction pour mettre à jour l'affichage de la production par heure
function updateHourlyProductionDisplay() {
  // Cette fonction peut être étendue pour mettre à jour le graphique
  // avec les données de production en temps réel
  if (currentHourlySession && currentHourlySession.actualProduction.length > 0) {
    console.log('Session active:', currentHourlySession.machine,
      'Total produit:', currentHourlySession.totalProduced);
  }
}

// Fonction pour afficher des alertes
function showAlert(message, type = 'info') {
  // Créer une alerte Bootstrap
  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
  alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';

  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
  `;

  document.body.appendChild(alertDiv);

  // Supprimer automatiquement après 5 secondes
  setTimeout(() => {
    if (alertDiv.parentElement) {
      alertDiv.remove();
    }
  }, 5000);
}

// Ajouter l'initialisation à la fonction initApp
function initEventListeners() {
  // Initialiser les contrôles de production par heure
  initHourlyProductionControls();

  console.log('Event listeners initialized');

}

// ===== FONCTIONNALITÉ AFFICHAGE DÉTAILS MACHINE =====

// Fonction pour afficher les détails d'une machine spécifique
function showMachineDetails(machineName) {
  const machineInfo = allMachineData.details[machineName];
  if (!machineInfo) {
    console.error('Machine non trouvée:', machineName);
    return;
  }

  // Créer ou mettre à jour la section des détails
  let detailsSection = document.getElementById('machineDetailsSection');
  if (!detailsSection) {
    detailsSection = document.createElement('div');
    detailsSection.id = 'machineDetailsSection';
    detailsSection.className = 'mt-4';

    // Insérer après le graphique de production
    const chartContainer = document.querySelector('#productionChart').closest('.card');
    chartContainer.parentNode.insertBefore(detailsSection, chartContainer.nextSibling);
  }

  // Calculer des métriques supplémentaires
  const efficiency = machineInfo.efficiency;
  const productionGap = machineInfo.production - machineInfo.target;
  const statusClass = getStatusClass(machineInfo.status);
  const efficiencyClass = efficiency >= 100 ? 'text-success' : efficiency >= 80 ? 'text-warning' : 'text-danger';

  detailsSection.innerHTML = `
    <div class="card border-primary">
      <div class="card-header bg-primary text-white">
        <div class="d-flex justify-content-between align-items-center">
          <h5 class="mb-0">
            <i class="fas fa-cog me-2"></i>
            Détails de la Machine: ${machineName}
          </h5>
          <button class="btn btn-sm btn-outline-light" onclick="closeMachineDetails()">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
      <div class="card-body">
        <!-- Métriques principales -->
        <div class="row mb-4">
          <div class="col-md-3">
            <div class="text-center p-3 bg-light rounded">
              <h3 class="text-primary mb-1">${machineInfo.production.toLocaleString()}</h3>
              <p class="text-muted mb-0 small">Production Actuelle</p>
            </div>
          </div>
          <div class="col-md-3">
            <div class="text-center p-3 bg-light rounded">
              <h3 class="text-secondary mb-1">${machineInfo.target.toLocaleString()}</h3>
              <p class="text-muted mb-0 small">Objectif</p>
            </div>
          </div>
          <div class="col-md-3">
            <div class="text-center p-3 bg-light rounded">
              <h3 class="${efficiencyClass} mb-1">${efficiency}%</h3>
              <p class="text-muted mb-0 small">Efficacité</p>
            </div>
          </div>
          <div class="col-md-3">
            <div class="text-center p-3 bg-light rounded">
              <span class="badge ${statusClass} fs-6">${machineInfo.status}</span>
              <p class="text-muted mb-0 small mt-1">Statut</p>
            </div>
          </div>
        </div>

        <!-- Barre de progression -->
        <div class="mb-4">
          <div class="d-flex justify-content-between align-items-center mb-2">
            <span class="fw-bold">Progression vers l'objectif</span>
            <span class="text-muted">${machineInfo.production} / ${machineInfo.target}</span>
          </div>
          <div class="progress" style="height: 20px;">
            <div class="progress-bar ${efficiency >= 100 ? 'bg-success' : efficiency >= 80 ? 'bg-warning' : 'bg-danger'}"
                 role="progressbar"
                 style="width: ${Math.min(efficiency, 100)}%"
                 aria-valuenow="${efficiency}"
                 aria-valuemin="0"
                 aria-valuemax="100">
              ${efficiency}%
            </div>
          </div>
          <small class="text-muted">
            Écart: ${productionGap > 0 ? '+' : ''}${productionGap} unités
            ${productionGap > 0 ? '(Au-dessus de l\'objectif)' : '(En dessous de l\'objectif)'}
          </small>
        </div>

        <!-- Informations détaillées -->
        <div class="row">
          <div class="col-md-6">
            <h6 class="fw-bold mb-3">
              <i class="fas fa-info-circle me-2 text-primary"></i>
              Informations Générales
            </h6>
            <table class="table table-sm">
              <tr>
                <td class="fw-bold">Équipe:</td>
                <td>
                  <span class="badge ${machineInfo.shift === 'Jour' ? 'bg-warning' : machineInfo.shift === 'Après-midi' ? 'bg-info' : 'bg-dark'} text-dark">
                    ${machineInfo.shift}
                  </span>
                  <small class="text-muted ms-2">${getShiftTime(machineInfo.shift)}</small>
                </td>
              </tr>
              <tr>
                <td class="fw-bold">Opérateur:</td>
                <td>
                  <i class="fas fa-user me-1 text-primary"></i>
                  ${machineInfo.operator || 'Non assigné'}
                </td>
              </tr>
              <tr>
                <td class="fw-bold">Prochaine équipe:</td>
                <td>
                  ${getNextShift(machineInfo.shift)} dans ${getTimeToNextShift(machineInfo.shift)}
                </td>
              </tr>
              <tr>
                <td class="fw-bold">Dernière mise à jour:</td>
                <td>
                  <i class="fas fa-clock me-1 text-muted"></i>
                  ${new Date().toLocaleString('fr-FR')}
                </td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <h6 class="fw-bold mb-3">
              <i class="fas fa-chart-line me-2 text-success"></i>
              Performance par Équipe
            </h6>
            <div class="row">
              <div class="col-4">
                <div class="card bg-light text-center">
                  <div class="card-body py-2">
                    <small class="text-muted">Jour</small>
                    <div class="fw-bold text-warning">${getShiftPerformance(machineName, 'Jour')}%</div>
                    <small class="text-muted">06:00-14:00</small>
                  </div>
                </div>
              </div>
              <div class="col-4">
                <div class="card bg-light text-center">
                  <div class="card-body py-2">
                    <small class="text-muted">Après-midi</small>
                    <div class="fw-bold text-info">${getShiftPerformance(machineName, 'Après-midi')}%</div>
                    <small class="text-muted">14:00-22:00</small>
                  </div>
                </div>
              </div>
              <div class="col-4">
                <div class="card bg-light text-center">
                  <div class="card-body py-2">
                    <small class="text-muted">Nuit</small>
                    <div class="fw-bold text-dark">${getShiftPerformance(machineName, 'Nuit')}%</div>
                    <small class="text-muted">22:00-06:00</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="mt-4 pt-3 border-top">
          <div class="d-flex flex-wrap gap-2">
            <button class="btn btn-primary btn-sm" onclick="filterByMachine('all')">
              <i class="fas fa-list me-1"></i> Voir toutes les machines
            </button>
            <button class="btn btn-outline-success btn-sm" onclick="refreshMachineData('${machineName}')">
              <i class="fas fa-sync-alt me-1"></i> Actualiser
            </button>
            <button class="btn btn-outline-info btn-sm" onclick="showShiftHistory('${machineName}')">
              <i class="fas fa-history me-1"></i> Historique équipes
            </button>
            <button class="btn btn-outline-warning btn-sm" onclick="scheduleMaintenance('${machineName}')">
              <i class="fas fa-tools me-1"></i> Planifier maintenance
            </button>
            <button class="favorite-btn btn btn-outline-warning btn-sm" data-machine="${machineName}" onclick="toggleFavoriteMachine('${machineName}')" title="Ajouter/Retirer des favoris">
              ${favoriteMachines.includes(machineName) ?
      '<i class="fas fa-star text-warning"></i> Favori' :
      '<i class="far fa-star text-muted"></i> Ajouter aux favoris'
    }
            </button>
          </div>
        </div>

        <!-- Informations contextuelles -->
        <div class="mt-3">
          <small class="text-muted">
            <i class="fas fa-info-circle me-1"></i>
            Équipe actuelle: <strong>${getCurrentShift()}</strong> |
            Prochaine relève dans: <strong>${getTimeToNextShift(getCurrentShift())}</strong>
          </small>
        </div>
      </div>
    </div>
  `;

  // Faire défiler vers la section des détails
  detailsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
}

// Fonctions utilitaires pour les détails des machines
function getStatusClass(status) {
  switch (status.toLowerCase()) {
    case 'en service':
      return 'bg-success';
    case 'maintenance':
      return 'bg-warning text-dark';
    case 'hors service':
      return 'bg-danger';
    default:
      return 'bg-secondary';
  }
}

function getShiftTime(shift) {
  const shiftTimes = {
    'Jour': '06:00 - 14:00',
    'Après-midi': '14:00 - 22:00',
    'Nuit': '22:00 - 06:00'
  };
  return shiftTimes[shift] || '06:00 - 14:00';
}

function getNextShift(currentShift) {
  const shiftSequence = {
    'Jour': 'Après-midi',
    'Après-midi': 'Nuit',
    'Nuit': 'Jour'
  };
  return shiftSequence[currentShift] || 'Après-midi';
}

function getTimeToNextShift(currentShift) {
  const now = new Date();
  const currentHour = now.getHours();

  let hoursToNext;
  switch (currentShift) {
    case 'Jour':
      hoursToNext = currentHour < 14 ? 14 - currentHour : 24 - currentHour + 14;
      break;
    case 'Après-midi':
      hoursToNext = currentHour < 22 ? 22 - currentHour : 24 - currentHour + 22;
      break;
    case 'Nuit':
      hoursToNext = currentHour < 6 ? 6 - currentHour : 24 - currentHour + 6;
      break;
    default:
      hoursToNext = 2;
  }

  return hoursToNext === 1 ? '1 heure' : `${hoursToNext} heures`;
}

// Données de performance par shift (simulées)
const shiftPerformanceData = {
  'T10 ': { 'Jour': 95, 'Après-midi': 88, 'Nuit': 82 },
  'T11 ': { 'Jour': 110, 'Après-midi': 105, 'Nuit': 98 },
  'T12 ': { 'Jour': 80, 'Après-midi': 75, 'Nuit': 70 },
  'T13 ': { 'Jour': 120, 'Après-midi': 115, 'Nuit': 108 },
  'T14 ': { 'Jour': 65, 'Après-midi': 70, 'Nuit': 60 },
  'T15 ': { 'Jour': 90, 'Après-midi': 85, 'Nuit': 88 },
  'T16 ': { 'Jour': 100, 'Après-midi': 95, 'Nuit': 92 },
  'T17 ': { 'Jour': 130, 'Après-midi': 125, 'Nuit': 120 }
};

function getShiftPerformance(machineName, shift) {
  return shiftPerformanceData[machineName]?.[shift] || 85;
}

// Fonctions d'action pour les boutons
function closeMachineDetails() {
  const detailsSection = document.getElementById('machineDetailsSection');
  if (detailsSection) {
    detailsSection.remove();
  }
}

function filterByMachine(machineName) {
  // Mettre à jour le sélecteur
  const machineFilter = document.getElementById('machineFilter');
  if (machineFilter) {
    machineFilter.value = machineName;
  }

  // Réinitialiser le graphique
  initProductionChart(machineName);

  if (machineName === 'all') {
    // Supprimer la section des détails si elle existe
    closeMachineDetails();
  }
}

function refreshMachineData(machineName) {
  // Simuler une actualisation des données
  const machineInfo = allMachineData.details[machineName];
  if (machineInfo) {
    // Effet visuel de chargement
    const canvas = document.getElementById('productionChart');
    if (canvas) {
      canvas.classList.add('chart-interaction-active');
      setTimeout(() => {
        canvas.classList.remove('chart-interaction-active');
      }, 300);
    }

    // Simuler de nouvelles données (dans un vrai système, ceci viendrait d'une API)
    const variation = Math.floor(Math.random() * 100) - 50; // Variation de -50 à +50
    const newProduction = Math.max(0, machineInfo.production + variation);

    // Mettre à jour les données
    allMachineData.details[machineName].production = newProduction;
    allMachineData.details[machineName].efficiency = Math.round((newProduction / machineInfo.target) * 100);

    // Mettre à jour l'array de production principal
    const machineIndex = allMachineData.labels.indexOf(machineName);
    if (machineIndex !== -1) {
      allMachineData.production[machineIndex] = newProduction;
    }

    // Rafraîchir l'affichage
    initProductionChart(machineName);
    showMachineDetails(machineName);

    // Effets sonores et visuels
    SOUNDS.success();
    hapticFeedback('medium');

    // Effet de particules bleues pour l'actualisation
    const refreshBtn = document.querySelector(`button[onclick="refreshMachineData('${machineName}')"]`);
    if (refreshBtn) {
      const rect = refreshBtn.getBoundingClientRect();
      createParticleEffect(
        rect.left + rect.width / 2,
        rect.top + rect.height / 2,
        '#17a2b8',
        10
      );
    }

    // Afficher un message de confirmation
    showAlert(`Données de ${machineName} actualisées: ${newProduction.toLocaleString()} unités produites`, 'success');
  }
}

function showShiftHistory(machineName) {
  showAlert(`Historique des équipes pour ${machineName} - Fonctionnalité en développement`, 'info');
}

function scheduleMaintenance(machineName) {
  showAlert(`Planification de maintenance pour ${machineName} - Fonctionnalité en développement`, 'info');
}

// ===== FONCTIONNALITÉS INTERACTIVES AVANCÉES =====

// Variables pour les tooltips et effets visuels
let customTooltip = null;
let selectedMachineIndex = -1;

// Fonction pour afficher un tooltip personnalisé
function showCustomTooltip(event, machineName, production) {
  // Supprimer le tooltip existant
  hideCustomTooltip();

  // Obtenir les informations de la machine
  const machineInfo = allMachineData.details[machineName];
  if (!machineInfo) return;

  // Créer le tooltip
  customTooltip = document.createElement('div');
  customTooltip.className = 'custom-chart-tooltip';
  customTooltip.innerHTML = `
    <div class="tooltip-header">
      <strong>${machineName}</strong>
      <span class="badge ${getStatusBadgeClass(machineInfo.status)}">${machineInfo.status}</span>
    </div>
    <div class="tooltip-body">
      <div class="tooltip-metric">
        <span class="metric-label">Production:</span>
        <span class="metric-value">${production.toLocaleString()}</span>
      </div>
      <div class="tooltip-metric">
        <span class="metric-label">Objectif:</span>
        <span class="metric-value">${machineInfo.target.toLocaleString()}</span>
      </div>
      <div class="tooltip-metric">
        <span class="metric-label">Efficacité:</span>
        <span class="metric-value ${getEfficiencyClass(machineInfo.efficiency)}">${machineInfo.efficiency}%</span>
      </div>
      <div class="tooltip-metric">
        <span class="metric-label">Opérateur:</span>
        <span class="metric-value">${machineInfo.operator}</span>
      </div>
    </div>
    <div class="tooltip-footer">
      <small><i class="fas fa-mouse-pointer me-1"></i>Cliquez pour plus de détails</small>
    </div>
  `;

  // Positionner le tooltip
  customTooltip.style.position = 'fixed';
  customTooltip.style.left = (event.native.clientX + 10) + 'px';
  customTooltip.style.top = (event.native.clientY - 10) + 'px';
  customTooltip.style.zIndex = '9999';

  document.body.appendChild(customTooltip);

  // Animation d'apparition
  setTimeout(() => {
    if (customTooltip) {
      customTooltip.classList.add('show');
    }
  }, 10);
}

// Fonction pour masquer le tooltip personnalisé
function hideCustomTooltip() {
  if (customTooltip) {
    customTooltip.remove();
    customTooltip = null;
  }
}

// Fonction pour mettre en évidence la machine sélectionnée
function highlightSelectedMachine(elementIndex) {
  selectedMachineIndex = elementIndex;

  // Ajouter un effet visuel temporaire avec animation de pulsation
  const canvas = document.getElementById('productionChart');
  if (canvas) {
    canvas.style.filter = 'brightness(1.1) drop-shadow(0 0 10px rgba(0, 123, 255, 0.5))';
    canvas.style.transform = 'scale(1.02)';
    canvas.style.transition = 'all 0.3s ease';

    setTimeout(() => {
      canvas.style.filter = 'none';
      canvas.style.transform = 'scale(1)';
    }, 300);
  }

  // Ajouter un effet de ripple au point de clic
  addRippleEffect(elementIndex);
}

// Fonction pour ajouter un effet de ripple
function addRippleEffect(elementIndex) {
  if (!productionChart) return;

  try {
    const meta = productionChart.getDatasetMeta(0);
    if (!meta || !meta.data) return;

    const bar = meta.data[elementIndex];

    if (bar) {
      const canvas = document.getElementById('productionChart');
      const rect = canvas.getBoundingClientRect();

      // Créer l'élément ripple
      const ripple = document.createElement('div');
      ripple.className = 'chart-ripple-effect';
      ripple.style.position = 'absolute';
      ripple.style.left = (bar.x + rect.left - 25) + 'px';
      ripple.style.top = (bar.y + rect.top - 25) + 'px';
      ripple.style.width = '50px';
      ripple.style.height = '50px';
      ripple.style.borderRadius = '50%';
      ripple.style.background = 'rgba(0, 123, 255, 0.3)';
      ripple.style.pointerEvents = 'none';
      ripple.style.zIndex = '1000';
      ripple.style.animation = 'rippleExpand 0.6s ease-out forwards';

      document.body.appendChild(ripple);

      // Supprimer après l'animation
      setTimeout(() => {
        if (ripple.parentNode) {
          ripple.remove();
        }
      }, 600);
    }
  } catch (error) {
    console.log('Error creating ripple effect:', error);
  }
}

// Fonctions utilitaires pour les classes CSS
function getStatusBadgeClass(status) {
  switch (status.toLowerCase()) {
    case 'en service':
      return 'bg-success';
    case 'maintenance':
      return 'bg-warning';
    case 'hors service':
      return 'bg-danger';
    default:
      return 'bg-secondary';
  }
}

function getEfficiencyClass(efficiency) {
  if (efficiency >= 100) return 'text-success fw-bold';
  if (efficiency >= 80) return 'text-warning fw-bold';
  return 'text-danger fw-bold';
}

// Fonction pour ajouter des indicateurs de statut sur le graphique
function addMachineStatusIndicators() {
  const canvas = document.getElementById('productionChart');
  if (!canvas || !productionChart) return;

  // Créer un overlay pour les indicateurs
  let overlay = document.getElementById('chart-status-overlay');
  if (!overlay) {
    overlay = document.createElement('div');
    overlay.id = 'chart-status-overlay';
    overlay.className = 'chart-status-overlay';
    canvas.parentNode.appendChild(overlay);
  }

  // Vider l'overlay existant
  overlay.innerHTML = '';

  // Ajouter des indicateurs pour chaque machine
  const chartArea = productionChart.chartArea;
  const meta = productionChart.getDatasetMeta(0);

  meta.data.forEach((bar, index) => {
    const machineName = productionChart.data.labels[index];
    const machineInfo = allMachineData.details[machineName];

    if (machineInfo) {
      // Conteneur principal pour tous les indicateurs de cette machine
      const indicatorContainer = document.createElement('div');
      indicatorContainer.className = 'machine-indicator-container';
      indicatorContainer.style.position = 'absolute';
      indicatorContainer.style.left = (bar.x - 15) + 'px';
      indicatorContainer.style.top = (chartArea.top - 45) + 'px';
      indicatorContainer.style.width = '30px';
      indicatorContainer.style.textAlign = 'center';

      // Indicateur de statut principal
      const statusIndicator = document.createElement('div');
      statusIndicator.className = 'status-indicator';

      // Définir l'icône et la couleur selon le statut
      let iconClass, colorClass, pulseClass = '';
      switch (machineInfo.status.toLowerCase()) {
        case 'en service':
          iconClass = 'fas fa-check-circle';
          colorClass = 'text-success';
          pulseClass = 'pulse-success';
          break;
        case 'maintenance':
          iconClass = 'fas fa-tools';
          colorClass = 'text-warning';
          pulseClass = 'pulse-warning';
          break;
        case 'hors service':
          iconClass = 'fas fa-exclamation-triangle';
          colorClass = 'text-danger';
          pulseClass = 'pulse-danger';
          break;
        default:
          iconClass = 'fas fa-question-circle';
          colorClass = 'text-secondary';
      }

      statusIndicator.innerHTML = `<i class="${iconClass} ${colorClass} ${pulseClass}"></i>`;
      statusIndicator.title = `${machineName}: ${machineInfo.status}`;

      // Indicateur d'efficacité (mini barre)
      const efficiencyIndicator = document.createElement('div');
      efficiencyIndicator.className = 'efficiency-mini-bar';
      efficiencyIndicator.style.width = '20px';
      efficiencyIndicator.style.height = '3px';
      efficiencyIndicator.style.marginTop = '2px';
      efficiencyIndicator.style.borderRadius = '2px';
      efficiencyIndicator.style.position = 'relative';

      const efficiency = machineInfo.efficiency;
      let efficiencyColor;
      if (efficiency >= 100) efficiencyColor = '#28a745';
      else if (efficiency >= 80) efficiencyColor = '#ffc107';
      else efficiencyColor = '#dc3545';

      efficiencyIndicator.style.background = `linear-gradient(to right, ${efficiencyColor} ${Math.min(efficiency, 100)}%, #e9ecef ${Math.min(efficiency, 100)}%)`;
      efficiencyIndicator.title = `Efficacité: ${efficiency}%`;

      // Indicateur de tendance (flèche)
      const trendIndicator = document.createElement('div');
      trendIndicator.className = 'trend-indicator';
      trendIndicator.style.fontSize = '8px';
      trendIndicator.style.marginTop = '1px';

      // Simuler une tendance basée sur l'efficacité
      const trend = efficiency >= 95 ? 'up' : efficiency <= 70 ? 'down' : 'stable';
      let trendIcon, trendColor;
      switch (trend) {
        case 'up':
          trendIcon = 'fas fa-arrow-up';
          trendColor = 'text-success';
          break;
        case 'down':
          trendIcon = 'fas fa-arrow-down';
          trendColor = 'text-danger';
          break;
        default:
          trendIcon = 'fas fa-minus';
          trendColor = 'text-muted';
      }

      trendIndicator.innerHTML = `<i class="${trendIcon} ${trendColor}"></i>`;
      trendIndicator.title = `Tendance: ${trend === 'up' ? 'En hausse' : trend === 'down' ? 'En baisse' : 'Stable'}`;

      // Assembler tous les indicateurs
      indicatorContainer.appendChild(statusIndicator);
      indicatorContainer.appendChild(efficiencyIndicator);
      indicatorContainer.appendChild(trendIndicator);

      // Ajouter un effet de survol pour le conteneur
      indicatorContainer.addEventListener('mouseenter', function () {
        this.style.transform = 'scale(1.2)';
        this.style.transition = 'transform 0.2s ease';
        SOUNDS.hover();
        hapticFeedback('light');
      });

      indicatorContainer.addEventListener('mouseleave', function () {
        this.style.transform = 'scale(1)';
      });

      // Clic sur l'indicateur pour afficher les détails
      indicatorContainer.addEventListener('click', function () {
        showMachineDetails(machineName);
        SOUNDS.click();
        hapticFeedback('medium');
      });

      overlay.appendChild(indicatorContainer);
    }
  });
}

// Système de favoris pour les machines
let favoriteMachines = JSON.parse(localStorage.getItem('favoriteMachines') || '[]');

function toggleFavoriteMachine(machineName) {
  const index = favoriteMachines.indexOf(machineName);

  if (index > -1) {
    favoriteMachines.splice(index, 1);
    showAlert(`${machineName} retiré des favoris`, 'info');
    SOUNDS.error();
    hapticFeedback('light');
  } else {
    favoriteMachines.push(machineName);
    showAlert(`${machineName} ajouté aux favoris`, 'success');
    SOUNDS.success();
    hapticFeedback('success');

    // Effet de particules dorées pour l'ajout aux favoris
    const favoriteBtn = document.querySelector(`[data-machine="${machineName}"]`);
    if (favoriteBtn) {
      const rect = favoriteBtn.getBoundingClientRect();
      createParticleEffect(
        rect.left + rect.width / 2,
        rect.top + rect.height / 2,
        '#ffc107',
        12
      );
    }
  }

  localStorage.setItem('favoriteMachines', JSON.stringify(favoriteMachines));
  updateFavoritesList();
  updateFavoriteIndicators();
}

function updateFavoriteIndicators() {
  // Mettre à jour les indicateurs de favoris dans l'interface
  const favoriteButtons = document.querySelectorAll('.favorite-btn');
  favoriteButtons.forEach(btn => {
    const machineName = btn.dataset.machine;
    const isFavorite = favoriteMachines.includes(machineName);
    btn.innerHTML = isFavorite ?
      '<i class="fas fa-star text-warning"></i>' :
      '<i class="far fa-star text-muted"></i>';
  });
}

// Fonction pour la sélection multiple de machines
let selectedMachines = [];

function toggleMachineSelection(machineName) {
  const index = selectedMachines.indexOf(machineName);
  if (index > -1) {
    selectedMachines.splice(index, 1);
  } else {
    selectedMachines.push(machineName);
  }

  updateSelectionDisplay();

  if (selectedMachines.length > 1) {
    showMachineComparison();
  }
}

function updateSelectionDisplay() {
  // Mettre à jour l'affichage des machines sélectionnées
  const selectionInfo = document.getElementById('machine-selection-info');
  if (selectionInfo) {
    if (selectedMachines.length > 0) {
      selectionInfo.innerHTML = `
        <div class="alert alert-info">
          <strong>Machines sélectionnées:</strong> ${selectedMachines.join(', ')}
          <button class="btn btn-sm btn-outline-primary ms-2" onclick="clearMachineSelection()">
            <i class="fas fa-times"></i> Effacer
          </button>
        </div>
      `;
      selectionInfo.style.display = 'block';
    } else {
      selectionInfo.style.display = 'none';
    }
  }
}

function clearMachineSelection() {
  selectedMachines = [];
  updateSelectionDisplay();
  closeMachineComparison();
}

function showMachineComparison() {
  // Afficher une comparaison des machines sélectionnées
  if (selectedMachines.length < 2) return;

  // Cette fonction sera implémentée pour afficher une comparaison détaillée
  showAlert(`Comparaison de ${selectedMachines.length} machines - Fonctionnalité en développement`, 'info');
}

function closeMachineComparison() {
  const comparisonSection = document.getElementById('machineComparisonSection');
  if (comparisonSection) {
    comparisonSection.remove();
  }
}

// ===== GESTION DES FAVORIS ET RACCOURCIS CLAVIER =====

// Initialiser la liste des favoris au chargement
function initializeFavorites() {
  updateFavoritesList();
  updateFavoriteIndicators();
}

function updateFavoritesList() {
  const favoritesList = document.getElementById('favoritesList');
  if (!favoritesList) return;

  favoritesList.innerHTML = '';

  if (favoriteMachines.length === 0) {
    favoritesList.innerHTML = '<li><span class="dropdown-item-text text-muted">Aucun favori</span></li>';
  } else {
    favoriteMachines.forEach(machineName => {
      const li = document.createElement('li');
      li.innerHTML = `
        <a class="dropdown-item d-flex justify-content-between align-items-center" href="#" onclick="showMachineDetails('${machineName}')">
          <span>
            <i class="fas fa-cog me-2 text-primary"></i>
            ${machineName}
          </span>
          <button class="btn btn-sm btn-outline-danger" onclick="event.stopPropagation(); toggleFavoriteMachine('${machineName}')" title="Retirer des favoris">
            <i class="fas fa-times"></i>
          </button>
        </a>
      `;
      favoritesList.appendChild(li);
    });

    // Ajouter un séparateur et une option pour tout effacer
    const separator = document.createElement('li');
    separator.innerHTML = '<hr class="dropdown-divider">';
    favoritesList.appendChild(separator);

    const clearAll = document.createElement('li');
    clearAll.innerHTML = `
      <a class="dropdown-item text-danger" href="#" onclick="clearAllFavorites()">
        <i class="fas fa-trash me-2"></i>
        Effacer tous les favoris
      </a>
    `;
    favoritesList.appendChild(clearAll);
  }
}

function clearAllFavorites() {
  favoriteMachines = [];
  localStorage.setItem('favoriteMachines', JSON.stringify(favoriteMachines));
  updateFavoritesList();
  updateFavoriteIndicators();
  showAlert('Tous les favoris ont été supprimés', 'info');
}

// Gestion des raccourcis clavier
let currentMachineIndex = 0;
const machineNames = ['T10 ', 'T11 ', 'T12 ', 'T13 ', 'T14 ', 'T15 ', 'T16 ', 'T17 '];

function initializeKeyboardShortcuts() {
  // Cette fonction est maintenant gérée par handleKeyboardShortcuts dans initEventListeners
  console.log('Keyboard shortcuts initialized');
}

function navigateToNextMachine() {
  currentMachineIndex = (currentMachineIndex + 1) % machineNames.length;
  highlightCurrentMachine();
  showQuickPreview(machineNames[currentMachineIndex]);
}

function navigateToPreviousMachine() {
  currentMachineIndex = (currentMachineIndex - 1 + machineNames.length) % machineNames.length;
  highlightCurrentMachine();
  showQuickPreview(machineNames[currentMachineIndex]);
}

function highlightCurrentMachine() {
  // Mettre en évidence la machine actuelle dans le graphique
  if (productionChart) {
    // Réinitialiser toutes les couleurs
    const backgroundColors = productionChart.data.datasets[0].backgroundColor;
    const originalColors = [...backgroundColors];

    // Mettre en évidence la machine actuelle
    backgroundColors[currentMachineIndex] = '#007bff'; // Bleu pour la sélection

    productionChart.update('none');

    // Restaurer les couleurs après un délai
    setTimeout(() => {
      productionChart.data.datasets[0].backgroundColor = originalColors;
      productionChart.update('none');
    }, 1000);
  }
}

function showQuickPreview(machineName) {
  const machineInfo = allMachineData.details[machineName];
  if (!machineInfo) return;

  // Afficher une notification rapide avec les informations de base
  showAlert(`${machineName}: ${machineInfo.production.toLocaleString()} unités (${machineInfo.efficiency}%)`, 'info');
}

// Fonction pour fermer les détails de machine
function closeMachineDetails() {
  const detailsSection = document.getElementById('machine-details');
  if (detailsSection) {
    detailsSection.style.display = 'none';
  }
}

// Fonction pour effacer la sélection de machine
function clearMachineSelection() {
  selectedMachineIndex = -1;
  // Retirer tous les effets de survol
  removeAllBarHoverEffects();
}

// Fonction pour afficher le guide des raccourcis clavier
function showKeyboardShortcuts() {
  const modal = document.createElement('div');
  modal.className = 'modal fade';
  modal.id = 'keyboardShortcutsModal';
  modal.innerHTML = `
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header bg-primary text-white">
          <h5 class="modal-title">
            <i class="fas fa-keyboard me-2"></i>
            Guide des Raccourcis Clavier
          </h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6">
              <h6 class="fw-bold text-primary mb-3">
                <i class="fas fa-mouse me-2"></i>Navigation
              </h6>
              <table class="table table-sm">
                <tr>
                  <td><kbd>←</kbd> <kbd>↑</kbd></td>
                  <td>Machine précédente</td>
                </tr>
                <tr>
                  <td><kbd>→</kbd> <kbd>↓</kbd></td>
                  <td>Machine suivante</td>
                </tr>
                <tr>
                  <td><kbd>Entrée</kbd> <kbd>Espace</kbd></td>
                  <td>Afficher détails machine</td>
                </tr>
                <tr>
                  <td><kbd>Échap</kbd></td>
                  <td>Fermer détails/sélection</td>
                </tr>
              </table>

              <h6 class="fw-bold text-success mb-3 mt-4">
                <i class="fas fa-cogs me-2"></i>Actions Rapides
              </h6>
              <table class="table table-sm">
                <tr>
                  <td><kbd>Ctrl</kbd> + <kbd>F</kbd></td>
                  <td>Ajouter/retirer des favoris</td>
                </tr>
                <tr>
                  <td><kbd>Ctrl</kbd> + <kbd>R</kbd></td>
                  <td>Actualiser données machine</td>
                </tr>
                <tr>
                  <td><kbd>Ctrl</kbd> + <kbd>A</kbd></td>
                  <td>Voir toutes les machines</td>
                </tr>
              </table>
            </div>

            <div class="col-md-6">
              <h6 class="fw-bold text-warning mb-3">
                <i class="fas fa-list-ol me-2"></i>Accès Direct
              </h6>
              <table class="table table-sm">
                <tr>
                  <td><kbd>Ctrl</kbd> + <kbd>1</kbd></td>
                  <td>Machine T10</td>
                </tr>
                <tr>
                  <td><kbd>Ctrl</kbd> + <kbd>2</kbd></td>
                  <td>Machine T11</td>
                </tr>
                <tr>
                  <td><kbd>Ctrl</kbd> + <kbd>3</kbd></td>
                  <td>Machine T12</td>
                </tr>
                <tr>
                  <td><kbd>Ctrl</kbd> + <kbd>4</kbd></td>
                  <td>Machine T13</td>
                </tr>
                <tr>
                  <td><kbd>Ctrl</kbd> + <kbd>5</kbd></td>
                  <td>Machine T14</td>
                </tr>
                <tr>
                  <td><kbd>Ctrl</kbd> + <kbd>6</kbd></td>
                  <td>Machine T15</td>
                </tr>
                <tr>
                  <td><kbd>Ctrl</kbd> + <kbd>7</kbd></td>
                  <td>Machine T16</td>
                </tr>
                <tr>
                  <td><kbd>Ctrl</kbd> + <kbd>8</kbd></td>
                  <td>Machine T17</td>
                </tr>
              </table>

              <h6 class="fw-bold text-info mb-3 mt-4">
                <i class="fas fa-mouse-pointer me-2"></i>Interactions Souris
              </h6>
              <table class="table table-sm">
                <tr>
                  <td><i class="fas fa-mouse-pointer"></i> Survol</td>
                  <td>Tooltip détaillé</td>
                </tr>
                <tr>
                  <td><i class="fas fa-mouse-pointer"></i> Clic</td>
                  <td>Détails complets</td>
                </tr>
                <tr>
                  <td><kbd>Ctrl</kbd> + <i class="fas fa-mouse-pointer"></i> Clic</td>
                  <td>Sélection multiple</td>
                </tr>
              </table>
            </div>
          </div>

          <div class="alert alert-info mt-4">
            <i class="fas fa-lightbulb me-2"></i>
            <strong>Astuce:</strong> Les raccourcis ne fonctionnent que lorsque vous n'êtes pas en train de taper dans un champ de saisie.
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  const bootstrapModal = new bootstrap.Modal(modal);
  bootstrapModal.show();

  // Supprimer le modal du DOM quand il est fermé
  modal.addEventListener('hidden.bs.modal', function () {
    modal.remove();
  });
}

// ===== EFFETS SONORES ET FEEDBACK HAPTIQUE =====

// Créer des sons synthétiques
function createBeepSound(frequency = 800, duration = 100, type = 'sine') {
  if (!SOUND_CONFIG.enabled) return;

  try {
    // Create audio context - modern browsers support AudioContext
    const audioContext = new (window.AudioContext || function () {
      console.log('Web Audio API not supported');
      return null;
    })();

    if (!audioContext) return;
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.value = frequency;
    oscillator.type = type;

    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(SOUND_CONFIG.volume, audioContext.currentTime + 0.01);
    gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration / 1000);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration / 1000);
  } catch (error) {
    console.log('Audio not supported');
  }
}

// Feedback haptique (vibration sur mobile)
function hapticFeedback(type = 'light') {
  try {
    if (navigator.vibrate) {
      switch (type) {
        case 'light':
          navigator.vibrate(10);
          break;
        case 'medium':
          navigator.vibrate(20);
          break;
        case 'heavy':
          navigator.vibrate([30, 10, 30]);
          break;
        case 'success':
          navigator.vibrate([50, 25, 50]);
          break;
        case 'error':
          navigator.vibrate([100, 50, 100, 50, 100]);
          break;
      }
    }
  } catch (error) {
    console.log('Haptic feedback not supported:', error);
  }
}

// Fonction pour basculer les sons
function toggleSounds() {
  SOUND_CONFIG.enabled = !SOUND_CONFIG.enabled;
  const message = SOUND_CONFIG.enabled ? 'Sons activés' : 'Sons désactivés';
  showAlert(message, 'info');

  if (SOUND_CONFIG.enabled) {
    SOUNDS.success();
    hapticFeedback('success');
  }

  // Sauvegarder la préférence
  localStorage.setItem('soundEnabled', SOUND_CONFIG.enabled);
}

// Charger les préférences sonores
function loadSoundPreferences() {
  const savedPreference = localStorage.getItem('soundEnabled');
  if (savedPreference !== null) {
    SOUND_CONFIG.enabled = savedPreference === 'true';
  }
}

// Mettre à jour le bouton de contrôle des sons
function updateSoundControlButton() {
  const soundBtn = document.getElementById('soundControlBtn');
  if (soundBtn) {
    if (SOUND_CONFIG.enabled) {
      soundBtn.className = 'btn sound-control-btn sound-enabled';
      soundBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
      soundBtn.title = 'Désactiver les sons';
    } else {
      soundBtn.className = 'btn sound-control-btn sound-disabled';
      soundBtn.innerHTML = '<i class="fas fa-volume-mute"></i>';
      soundBtn.title = 'Activer les sons';
    }
  }
}

// Fonction améliorée pour basculer les sons
function toggleSounds() {
  SOUND_CONFIG.enabled = !SOUND_CONFIG.enabled;
  const message = SOUND_CONFIG.enabled ? 'Sons activés' : 'Sons désactivés';
  showAlert(message, 'info');

  if (SOUND_CONFIG.enabled) {
    SOUNDS.success();
    hapticFeedback('success');
  }

  // Sauvegarder la préférence
  localStorage.setItem('soundEnabled', SOUND_CONFIG.enabled);
  updateSoundControlButton();

  // Ajouter un effet visuel au bouton
  const soundBtn = document.getElementById('soundControlBtn');
  if (soundBtn) {
    soundBtn.style.transform = 'scale(1.2)';
    setTimeout(() => {
      soundBtn.style.transform = 'scale(1)';
    }, 200);
  }
}

// ===== EFFETS DE PARTICULES =====

// Créer un effet de particules pour les actions importantes
function createParticleEffect(x, y, color = '#007bff', count = 8) {
  try {
    for (let i = 0; i < count; i++) {
      const particle = document.createElement('div');
      particle.className = 'particle-effect';
      particle.style.position = 'fixed';
      particle.style.left = x + 'px';
      particle.style.top = y + 'px';
      particle.style.width = '4px';
      particle.style.height = '4px';
      particle.style.backgroundColor = color;
      particle.style.borderRadius = '50%';
      particle.style.pointerEvents = 'none';
      particle.style.zIndex = '9999';

      // Direction aléatoire
      const angle = (i / count) * Math.PI * 2;
      const distance = 30 + Math.random() * 20;
      const finalX = x + Math.cos(angle) * distance;
      const finalY = y + Math.sin(angle) * distance;

      particle.style.setProperty('--final-x', finalX + 'px');
      particle.style.setProperty('--final-y', finalY + 'px');

      // Animation personnalisée
      particle.style.animation = `particleFloat 1s ease-out forwards`;

      document.body.appendChild(particle);

      // Supprimer après l'animation
      setTimeout(() => {
        if (particle.parentNode) {
          particle.remove();
        }
      }, 1000);
    }
  } catch (error) {
    console.log('Error creating particle effect:', error);
  }
}
