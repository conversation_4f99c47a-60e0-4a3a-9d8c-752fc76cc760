import React, { createContext, useContext, useState } from 'react';
import { Customer, Supplier, Product, Transaction } from '../types';

interface DataContextType {
  customers: Customer[];
  suppliers: Supplier[];
  products: Product[];
  transactions: Transaction[];
  addCustomer: (customer: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateCustomer: (id: string, customer: Partial<Customer>) => void;
  addSupplier: (supplier: Omit<Supplier, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateSupplier: (id: string, supplier: Partial<Supplier>) => void;
  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateProduct: (id: string, product: Partial<Product>) => void;
  addTransaction: (transaction: Omit<Transaction, 'id'>) => void;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

// Mock data for demonstration
const mockCustomers: Customer[] = [
  {
    id: '1',
    name: 'Renault Group',
    email: '<EMAIL>',
    phone: '+33 1 76 84 04 04',
    address: '13-15 Quai Alphonse le Gallo, 92100 Boulogne-Billancourt',
    contactPerson: 'Laurent Dubois',
    balance: 45000.00,
    creditLimit: 100000.00,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-12-01'),
  },
  {
    id: '2',
    name: 'Stellantis',
    email: '<EMAIL>',
    phone: '+33 1 56 44 20 00',
    address: '2-10 Boulevard de l\'Europe, 78300 Poissy',
    contactPerson: 'Sophie Moreau',
    balance: 78500.00,
    creditLimit: 150000.00,
    createdAt: new Date('2024-02-20'),
    updatedAt: new Date('2024-11-28'),
  },
];

const mockSuppliers: Supplier[] = [
  {
    id: '1',
    name: 'Nexans France',
    email: '<EMAIL>',
    phone: '+33 1 73 23 84 00',
    address: '4 Allée de l\'Arche, 92400 Courbevoie',
    contactPerson: 'Michel Garnier',
    balance: -25000.00,
    paymentTerms: '30 jours',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-11-30'),
  },
  {
    id: '2',
    name: 'Prysmian Group',
    email: '<EMAIL>',
    phone: '+33 1 30 68 70 00',
    address: '155 Avenue Pierre Brossolette, 92120 Montrouge',
    contactPerson: 'Catherine Leroy',
    balance: -18750.00,
    paymentTerms: '45 jours',
    createdAt: new Date('2024-01-25'),
    updatedAt: new Date('2024-12-02'),
  },
];

const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Câble Haute Tension 16AWG',
    sku: 'CBL-HT-16AWG-001',
    description: 'Câble haute tension pour systèmes automobiles, 16AWG, isolation renforcée',
    category: 'Câbles Haute Tension',
    unitPrice: 12.50,
    stockQuantity: 2450,
    minStockLevel: 500,
    supplierId: '1',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-12-01'),
  },
  {
    id: '2',
    name: 'Connecteur Étanche IP67',
    sku: 'CON-IP67-004',
    description: 'Connecteur étanche IP67 pour environnement automobile',
    category: 'Connecteurs',
    unitPrice: 8.75,
    stockQuantity: 1250,
    minStockLevel: 200,
    supplierId: '1',
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-11-28'),
  },
  {
    id: '3',
    name: 'Faisceau de Câbles ECU',
    sku: 'FCX-ECU-MAIN-007',
    description: 'Faisceau principal pour unité de contrôle électronique',
    category: 'Faisceaux',
    unitPrice: 45.00,
    stockQuantity: 875,
    minStockLevel: 100,
    supplierId: '2',
    createdAt: new Date('2024-02-15'),
    updatedAt: new Date('2024-12-02'),
  },
];

const mockTransactions: Transaction[] = [
  {
    id: '1',
    type: 'sale',
    customerId: '1',
    amount: 15000.00,
    description: 'Livraison câbles HT - Commande #REN-2024-001',
    date: new Date('2024-11-28'),
    status: 'completed',
  },
  {
    id: '2',
    type: 'purchase',
    supplierId: '1',
    amount: 8500.00,
    description: 'Achat connecteurs IP67 - Lot #NEX-11-2024',
    date: new Date('2024-11-25'),
    status: 'completed',
  },
];

export const DataProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [customers, setCustomers] = useState<Customer[]>(mockCustomers);
  const [suppliers, setSuppliers] = useState<Supplier[]>(mockSuppliers);
  const [products, setProducts] = useState<Product[]>(mockProducts);
  const [transactions, setTransactions] = useState<Transaction[]>(mockTransactions);

  const addCustomer = (customerData: Omit<Customer, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newCustomer: Customer = {
      ...customerData,
      id: Date.now().toString(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setCustomers(prev => [...prev, newCustomer]);
  };

  const updateCustomer = (id: string, customerData: Partial<Customer>) => {
    setCustomers(prev => prev.map(customer => 
      customer.id === id 
        ? { ...customer, ...customerData, updatedAt: new Date() }
        : customer
    ));
  };

  const addSupplier = (supplierData: Omit<Supplier, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newSupplier: Supplier = {
      ...supplierData,
      id: Date.now().toString(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setSuppliers(prev => [...prev, newSupplier]);
  };

  const updateSupplier = (id: string, supplierData: Partial<Supplier>) => {
    setSuppliers(prev => prev.map(supplier => 
      supplier.id === id 
        ? { ...supplier, ...supplierData, updatedAt: new Date() }
        : supplier
    ));
  };

  const addProduct = (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newProduct: Product = {
      ...productData,
      id: Date.now().toString(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setProducts(prev => [...prev, newProduct]);
  };

  const updateProduct = (id: string, productData: Partial<Product>) => {
    setProducts(prev => prev.map(product => 
      product.id === id 
        ? { ...product, ...productData, updatedAt: new Date() }
        : product
    ));
  };

  const addTransaction = (transactionData: Omit<Transaction, 'id'>) => {
    const newTransaction: Transaction = {
      ...transactionData,
      id: Date.now().toString(),
    };
    setTransactions(prev => [...prev, newTransaction]);
  };

  return (
    <DataContext.Provider value={{
      customers,
      suppliers,
      products,
      transactions,
      addCustomer,
      updateCustomer,
      addSupplier,
      updateSupplier,
      addProduct,
      updateProduct,
      addTransaction,
    }}>
      {children}
    </DataContext.Provider>
  );
};

export const useData = () => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};