<!DOCTYPE html>
<html lang="en" data-bs-theme="light">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="APTIV eCutting Monitoring Tool - Real-time production monitoring dashboard">
  <title>eCutting Monitoring Tool | APTIV</title>
  <!-- Preconnect to CDNs for better performance -->
  <link rel="preconnect" href="https://cdnjs.cloudflare.com">
  <link rel="preconnect" href="https://cdn.jsdelivr.net">

  <!-- External CSS with fallbacks -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
    integrity="sha512-Fo3rlrZj/k7ujTnHg4CGR2D7kSs0v4LLanw2qksYuRlEzO+tcaEPQogQ0KaoGN26/zrn20ImR1DfuLWnOo7aBA=="
    crossorigin="anonymous" referrerpolicy="no-referrer" onerror="console.error('Failed to load Font Awesome');" />

  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet"
    integrity="sha384-9ndCyUaIbzAi2FUVXJi0CjmCapSmO7SnpJef0486qhLnuZ2cdeRhO02iuK6FUUVM" crossorigin="anonymous"
    onerror="console.error('Failed to load Bootstrap CSS');">

  <!-- Local CSS -->
  <link rel="stylesheet" href="css/style.css">

  <!-- Favicon -->
  <link rel="icon"
    href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📊</text></svg>">

  <!-- Preload critical resources -->
  <link rel="preload" href="js/script.js" as="script">
</head>

<body class="d-flex flex-column min-vh100">
  <!-- Header Section with ARIA labels for accessibility -->
  <header class="app-header" role="banner" aria-label="Main header">
    <div class="container-fluid">
      <div class="header-content d-flex justify-content-between align-items-center py-0">
        <div class="d-flex align-items-center">
          <div class="logo-container me-3" aria-label="APTIV Logo">
            <span class="logo fw-bold fs-">APTIV</span>
            <div class="logo-dots d-inline-flex flex-column ms-1" aria-hidden="true">
              <span class="dot bg-danger"></span>
              <span class="dot bg-danger mt-1"></span>
            </div>
          </div>
          <h1 class="header-title fs-5 mb-0 d-none d-md-block" aria-label="Application Title">Twisting
            Monitoring Tool</h1>
        </div>

        <div class="user-info d-flex align-items-center" aria-label="User information">
          <div class="d-flex align-items-center">
            <i class="fas fa-user-circle fs-4 me-2" aria-hidden="true"></i>
            <span class="username me-3">APTIV\kyljxl</span>
          </div>
          <div class="date bg-dark bg-opacity-25 px-3 py-1 rounded">
            <i class="far fa-calendar-alt me-2" aria-hidden="true"></i>
            <time class="current-date" datetime="2025-07-10">7/11/2025</time>
          </div>
          <img src="images/Capture2.PNG" alt="User Image" class="user-image ms-3"
            style="width: 40px; height: 40px; border-radius: 50%;">
        </div>
      </div>
    </div>
  </header>


  <!-- Navigation Bar with improved accessibility -->
  <nav class="navbar navbar-expand-lg navbar-dark bg-dark" aria-label="Breadcrumb navigation">
    <div class="container-fluid">
      <nav aria-label="breadcrumb">
        <ol class="breadcrumb mb-0 py-2">
          <li class="breadcrumb-item"><a href="#" class="text-decoration-none">Home</a></li>
          <li class="breadcrumb-item"><a href="#" class="text-decoration-none">Operational</a></li>
          <li class="breadcrumb-item active" aria-current="page">Meknes - Level 3 - Production</li>
        </ol>
      </nav>
      <div class="ms-auto">
        <div class="dropdown">
          <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton"
            data-bs-toggle="dropdown" aria-expanded="false">
            Menu
          </button>
          <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton">
            <li><a class="dropdown-item" href="#" id="dashboard-link">Tableau de bord</a></li>
            <li><a class="dropdown-item" href="#" id="reports-link">Rapports</a></li>
            <li><a class="dropdown-item" href="rapport twist.ods" download>Télécharger le rapport</a></li>
            <li><a class="dropdown-item" href="#">Paramètres</a></li>
          </ul>
        </div>
      </div>
    </div>

  </nav>

  <!-- Report Generation Modal -->
  <div class="modal fade" id="reportModal" tabindex="-1" aria-labelledby="reportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header bg-primary text-white">
          <h5 class="modal-title" id="reportModalLabel">Generate Report</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="reportForm">
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="reportType" class="form-label">Report Type</label>
                <select class="form-select" id="reportType" required>
                  <option value="">Select report type</option>
                  <option value="daily">Daily Production Report</option>
                  <option value="shift">Shift Performance Report</option>
                  <option value="machine">Machine Performance Report</option>
                  <option value="downtime">Downtime Analysis Report</option>
                </select>
              </div>
              <div class="col-md-6">
                <label for="reportFormat" class="form-label">Format</label>
                <select class="form-select" id="reportFormat" required>
                  <option value="pdf">PDF</option>
                  <option value="excel">Excel</option>
                  <option value="csv">CSV</option>
                </select>
              </div>
            </div>
            <div class="row mb-3">
              <div class="col-md-6">
                <label for="reportStartDate" class="form-label">Start Date</label>
                <input type="date" class="form-control" id="reportStartDate" required>
              </div>
              <div class="col-md-6">
                <label for="reportEndDate" class="form-label">End Date</label>
                <input type="date" class="form-control" id="reportEndDate" required>
              </div>
            </div>
            <div class="mb-3">
              <label for="reportMachines" class="form-label">Machines (Optional)</label>
              <select class="form-select" id="reportMachines" multiple>
                <option value="all">All Machines</option>
                <optgroup label="Twisting Machines 1-15">
                  <option value="T01">T01 - Twisting Machine 1</option>
                  <option value="T02">T02 - Twisting Machine 2</option>
                  <option value="T03">T03 - Twisting Machine 3</option>
                  <option value="T04">T04 - Twisting Machine 4</option>
                  <option value="T05">T05 - Twisting Machine 5</option>
                  <option value="T06">T06 - Twisting Machine 6</option>
                  <option value="T07">T07 - Twisting Machine 7</option>
                  <option value="T08">T08 - Twisting Machine 8</option>
                  <option value="T09">T09 - Twisting Machine 9</option>
                  <option value="T10">T10 - Twisting Machine 10</option>
                  <option value="T11">T11 - Twisting Machine 11</option>
                  <option value="T12">T12 - Twisting Machine 12</option>
                  <option value="T13">T13 - Twisting Machine 13</option>
                  <option value="T14">T14 - Twisting Machine 14</option>
                  <option value="T15">T15 - Twisting Machine 15</option>
                </optgroup>
                <optgroup label="Twisting Machines 24-31">
                  <option value="T24">T24 - Twisting Machine 24</option>
                  <option value="T25">T25 - Twisting Machine 25</option>
                  <option value="T26">T26 - Twisting Machine 26</option>
                  <option value="T27">T27 - Twisting Machine 27</option>
                  <option value="T28">T28 - Twisting Machine 28</option>
                  <option value="T29">T29 - Twisting Machine 29</option>
                  <option value="T30">T30 - Twisting Machine 30</option>
                  <option value="T31">T31 - Twisting Machine 31</option>
                </optgroup>
              </select>
              <div class="form-text">Hold Ctrl/Cmd to select multiple machines</div>
            </div>
            <div class="form-check mb-3">
              <input class="form-check-input" type="checkbox" id="includeCharts" checked>
              <label class="form-check-label" for="includeCharts">
                Include charts in report
              </label>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary" id="generateReportBtn">
            <i class="fas fa-file-export me-1"></i> Generate Report
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <main class="container-fluid">
    <!-- Filter Section -->
    <section class="filter-section">
      <div class="row g-3 align-items-end">
        <div class="col-md-2">
          <label class="form-label">Date</label>
          <div class="input-group">
            <input type="date" class="form-control" id="filterDate" value="2025-07-10" aria-label="Select date">
            <span class="input-group-text"><i class="far fa-calendar-alt"></i></span>
          </div>
        </div>
        <div class="col-md-2">
          <label class="form-label">Shift</label>
          <select class="form-select">
            <option selected>shift matin</option>
            <option>shift soir</option>
            <option>shift nuit</option>
          </select>
        </div>
        <div class="col-md-2">
          <label class="form-label">Machine</label>
          <select class="form-select" id="machineSelect" aria-label="Select machine">
            <option value="">All Machines</option>
            <!-- Machine Group 1: T1-T15 -->
            <optgroup label="Twisting Machines 01-31">
              <option value="T01">T01 - Twisting Machine 01</option>
              <option value="T02">T02 - Twisting Machine 02</option>
              <option value="T03">T03 - Twisting Machine 03</option>
              <option value="T04">T04 - Twisting Machine 04</option>
              <option value="T05">T05 - Twisting Machine 05</option>
              <option value="T06">T06 - Twisting Machine 06</option>
              <option value="T07">T07 - Twisting Machine 07</option>
              <option value="T08">T08 - Twisting Machine 08</option>
              <option value="T09">T09 - Twisting Machine 09</option>
              <option value="T10">T10 - Twisting Machine 10</option>
              <option value="T11">T11 - Twisting Machine 11</option>
              <option value="T12">T12 - Twisting Machine 12</option>
              <option value="T13">T13 - Twisting Machine 13</option>
              <option value="T14">T14 - Twisting Machine 14</option>
              <option value="T15">T15 - Twisting Machine 15</option>
              <option value="T16">T16 - Twisting Machine 16</option>
              <option value="T17">T17 - Twisting Machine 17</option>
              <option value="T18">T18 - Twisting Machine 18</option>
              <option value="T19">T19 - Twisting Machine 19</option>
              <option value="T20">T20 - Twisting Machine 20</option>
              <option value="T21">T21 - Twisting Machine 21</option>
              <option value="T22">T22 - Twisting Machine 22</option>
              <option value="T23">T23 - Twisting Machine 23</option>
              <option value="T24">T24 - Twisting Machine 24</option>
              <option value="T25">T25 - Twisting Machine 25</option>
              <option value="T26">T26 - Twisting Machine 26</option>
              <option value="T27">T27 - Twisting Machine 27</option>
              <option value="T28">T28 - Twisting Machine 28</option>
              <option value="T30">T30 - Twisting Machine 30</option>
              <option value="T31">T31 - Twisting Machine 31</option>
            </optgroup>
          </select>
        </div>
        <div class="col-md-2">
          <label class="form-label">Machine Group</label>
          <select class="form-select" id="machineGroupSelect" aria-label="Select machine group">
            <option>All Groups</option>
            <!-- Additional group options would go here -->
          </select>
        </div>
        <div class="col-md-2 d-flex align-items-end gap-2">
          <button class="btn btn-primary search-btn flex-grow-1">
            <i class="fas fa-search me-1"></i> Search
          </button>
          <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#reportModal"
            title="Generate Report">
            <i class="fas fa-file-export"></i>
          </button>
        </div>
      </div>
      <div class="filter-info">
        <small>Target apply for Plant only, when filtering disregard the targets</small><br>
        <small>Filters: Date: 2025-07-10.</small>
      </div>
    </section>

    <!-- Barcode Generator Section -->
    <section class="barcode-generator-section mb-4">
      <div class="card">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">
            <i class="fas fa-barcode me-2"></i>





            <!-- Barcode Scanner Section -->
            <section class="barcode-scanner-section mb-4">
              <div class="card">
                <div class="card-header bg-success text-white">
                  <h5 class="mb-0">
                    <i class="fas fa-search me-2"></i>
                    Scanner de Code-Barres
                  </h5>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-8 mx-auto">
                      <div class="input-group mb-3">
                        <input type="text" id="scannerInput" class="form-control form-control-lg"
                          placeholder="Scannez ou tapez un code-barres (ex: EK9-HAB-TAB3U)" aria-label="Scanner input"
                          autocomplete="off">
                        <button class="btn btn-success btn-lg" type="button" onclick="searchBarcode()">
                          <i class="fas fa-search me-1"></i> Rechercher
                        </button>
                      </div>

                      <!-- Scanner Result Display -->
                      <div id="scannerResult" class="scanner-result" style="display: none;">
                        <!-- Results will be displayed here -->
                      </div>

                      <!-- Recent Scans -->
                      <div class="mt-4">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                          <h6 class="mb-0">Scans récents:</h6>
                          <button class="btn btn-outline-info btn-sm" onclick="loadMoreDataTwistScans()"
                            title="Charger plus de scans Data Twist">
                            <i class="fas fa-sync-alt me-1"></i> Actualiser
                          </button>
                        </div>
                        <div id="recentScans" class="recent-scans">
                          <p class="text-muted">Chargement des scans récents...</p>
                        </div>
                        <div class="mt-2">
                          <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Inclut les scans locaux et les données de production Data Twist
                          </small>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            <!-- Main Dashboard -->
            <section class="dashboard">
              <!-- Production by Machine Chart -->
              <div class="card mb-4">
                <div class="card-header">
                  <div class="d-flex justify-content-between align-items-center mb-3">
                    <div class="d-flex align-items-center">
                      <h5 class="mb-0">Production by Machine</h5>
                      <button type="button" class="btn btn-success btn-sm ms-3" id="startProductionBtn">
                        <i class="fas fa-play me-1"></i> Start Production
                      </button>
                    </div>
                    <div class="btn-group" role="group" aria-label="Chart view options">
                      <input type="radio" class="btn-check" name="btnradio" id="btnradio1" autocomplete="off" checked>
                      <label class="btn btn-outline-primary" for="btnradio1">Daily</label>

                      <input type="radio" class="btn-check" name="btnradio" id="btnradio2" autocomplete="off">
                      <label class="btn btn-outline-primary" for="btnradio2">Weekly</label>

                      <input type="radio" class="btn-check" name="btnradio" id="btnradio3" autocomplete="off">
                      <label class="btn btn-outline-primary" for="btnradio3">Monthly</label>
                    </div>
                  </div>

                  <!-- Machine Filter -->
                  <div class="d-flex align-items-center">
                    <label for="machineFilter" class="form-label me-2 mb-0">Filtrer par machine:</label>
                    <select id="machineFilter" class="form-select form-select-sm me-3" style="width: auto;"
                      onchange="filterByMachine(this.value)">
                      <option value="all">Toutes les machines</option>
                      <option value="T-10 CXC">T-10 CXC</option>
                      <option value="T-11 CXC">T-11 CXC</option>
                      <option value="T-12 CXC">T-12 CXC</option>
                      <option value="T-13 CXC">T-13 CXC</option>
                      <option value="T-14 CXC">T-14 CXC</option>
                      <option value="T-15 CXC">T-15 CXC</option>
                      <option value="T-16 CXC">T-16 CXC</option>
                      <option value="T-17 CXC">T-17 CXC</option>
                    </select>
                    <small class="text-muted">Cliquez sur une barre du graphique pour voir les détails</small>
                  </div>
                </div>
                <div class="card-body">
                  <div class="chart-container" style="position: relative; height: 650px;">
                    <canvas id="productionChart"></canvas>
                  </div>
                  <div class="chart-legend d-flex justify-content-center mt-3">
                    <div class="me-4">
                      <span class="legend-color"
                        style="background-color: #0051ff; width: 15px; height: 15px; display: inline-block; margin-right: 5px;"></span>
                      <span>On Target</span>
                    </div>
                    <div class="me-4">
                      <span class="legend-color"
                        style="background-color: #ffc107; width: 15px; height: 15px; display: inline-block; margin-right: 5px;"></span>
                      <span>Warning</span>
                    </div>
                    <div>
                      <span class="legend-color"
                        style="background-color: #ff4356; width: 15px; height: 15px; display: inline-block; margin-right: 5px;"></span>
                      <span>Off Target</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- General Averages Table -->
              <div class="card">
                <div class="card-header">
                  <h5>General Averages</h5>
                </div>
                <div class="card-body table-responsive">
                  <table class="table table-bordered table-hover">
                    <thead class="table-light">
                      <tr>
                        <th></th>
                        <th>Output</th>
                        <th>Uptime</th>
                        <th>Unplanned DT</th>
                        <th>Planned DT</th>
                        <th>Setup Time</th>
                        <th>Setup Time (Min)</th>
                        <th>PMH</th>
                        <th>FTQ</th>
                        <th>Throughput</th>
                        <th>Avg Target Setup</th>
                        <th>Compliance Setup</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td><strong>Targets</strong></td>
                        <td><span class="status-badge bg-danger"></span> 546,400.00</td>
                        <td><span class="status-badge bg-success"></span> 62.70%</td>
                        <td><span class="status-badge bg-danger"></span> 14.00%</td>
                        <td><span class="status-badge bg-danger"></span> 1.70%</td>
                        <td><span class="status-badge bg-danger"></span> 14.00%</td>
                        <td>00:30:00</td>
                        <td>0.00</td>
                        <td>100.00%</td>
                        <td>0.00</td>
                        <td>00:30:00</td>
                        <td>0.00%</td>
                      </tr>
                      <!-- Additional rows would go here -->
                    </tbody>
                  </table>
                </div>
              </div>



              <!-- Machine Information Section -->
              <div class="card mt-4">
                <div class="card-header">
                  <h5 class="mb-0">Informations des Machines</h5>
                </div>
                <div class="card-body">
                  <div class="table-responsive">
                    <table class="table table-striped table-hover">
                      <thead class="table-dark">
                        <tr>
                          <th>Machine</th>
                          <th>Modèle</th>
                          <th>Année</th>
                          <th>Statut</th>
                          <th>Dernière Maintenance</th>
                          <th>Prochaine Maintenance</th>
                          <th>Heures de Fonctionnement</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody id="machineInfoBody">
                        <!-- Les données seront chargées dynamiquement via JavaScript -->
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              <!-- Production par Heure Section -->
              <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                  <h5 class="mb-0">Production par Heure</h5>
                  <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-primary active"
                      data-period="today">Aujourd'hui</button>
                    <button type="button" class="btn btn-sm btn-outline-primary" data-period="yesterday">Hier</button>
                    <button type="button" class="btn btn-sm btn-outline-primary" data-period="week">Cette
                      Semaine</button>
                  </div>
                </div>
                <div class="card-body">
                  <div class="chart-container" style="position: relative; height: 400px;">
                    <canvas id="hourlyProductionChart"></canvas>
                  </div>
                  <div class="chart-legend d-flex justify-content-center mt-3">
                    <div class="me-4">
                      <span class="legend-color"
                        style="background-color: #0040ff; width: 15px; height: 15px; display: inline-block; margin-right: 5px;"></span>
                      <span>Production Réelle</span>
                    </div>
                    <div>
                      <span class="legend-color"
                        style="background-color: #e74a3b; width: 15px; height: 15px; display: inline-block; margin-right: 5px;"></span>
                      <span>Objectif</span>
                    </div>
                  </div>
                </div>
              </div>
            </section>
  </main>


  <!-- Footer -->
  <!-- Footer with semantic HTML and ARIA -->
  <footer class="app-footer mt-auto py-3 bg-light border-top" role="contentinfo">
    <div class="container-fluid">
      <div class="row align-items-center">
        <div class="col-md-6 text-center text-md-start">
          <p class="mb-0">© <span id="current-year">2025</span> - APTIV. All rights reserved.</p>
        </div>
        <div class="col-md-6 text-center text-md-end mt-2 mt-md-0">
          <div class="d-flex justify-content-center justify-content-md-end gap-3">
            <a href="#" class="text-decoration-none text-muted small" aria-label="Privacy Policy">Privacy</a>
            <span class="text-muted">|</span>
            <a href="#" class="text-decoration-none text-muted small" aria-label="Terms of Service">Terms</a>
            <span class="text-muted">|</span>
            <a href="#" class="text-decoration-none text-muted small" aria-label="Help and Support">Help</a>
          </div>
        </div>
      </div>
    </div>
  </footer>

  <!-- Start Production Modal -->
  <div class="modal fade" id="startProductionModal" tabindex="-1" aria-labelledby="startProductionModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header bg-success text-white">
          <h5 class="modal-title" id="startProductionModalLabel">Start Production</h5>
          <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="startProductionForm">
            <div class="mb-3">
              <label for="machineSelectModal" class="form-label">Select Machine</label>
              <select class="form-select" id="machineSelectModal" required>
                <option value="">-- Select a machine --</option>
                <!-- Options will be populated by JavaScript -->
              </select>
            </div>
            <div class="mb-3">
              <label for="orderNumber" class="form-label">Order Number</label>
              <input type="text" class="form-control" id="orderNumber" required>
            </div>
            <div class="mb-3">
              <label for="quantity" class="form-label">Quantity</label>
              <input type="number" class="form-control" id="quantity" min="1" value="1" required>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-success" id="confirmStartProduction">
            <i class="fas fa-play me-1"></i> Start Production
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Scripts with async/defer and error handling -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js" crossorigin="anonymous" referrerpolicy="no-referrer"
    onerror="console.error('Failed to load Chart.js');">
    </script>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"
    integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz" crossorigin="anonymous"
    onerror="console.error('Failed to load Bootstrap JS');">
    </script>
  <script src="js/data-loader.js" type="module"></script>

  <!-- JsBarcode CDN for barcode generation -->
  <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>

  <!-- Barcode Generator Functions -->
  <script>
    // Sample data for different categories
    const sampleData = {
      aptiv: [
        { code: 'APTIV-CBL-HT-001', format: 'CODE128', name: 'Câble Haute Tension 16AWG' },
        { code: 'APTIV-CON-IP67-004', format: 'CODE128', name: 'Connecteur Étanche IP67' },
        { code: 'APTIV-SEN-TEMP-012', format: 'CODE128', name: 'Capteur de Température' },
        { code: 'APTIV-REL-PWR-008', format: 'CODE128', name: 'Relais de Puissance 40A' }
      ],
      standard: [
        { code: '3760123456789', format: 'EAN13', name: 'Kit de Câblage Automobile Premium' },
        { code: '3760234567890', format: 'EAN13', name: 'Connecteur Multi-Pin 24 Voies' },
        { code: '012345678905', format: 'UPC', name: 'Automotive Harness Kit Pro' }
      ],
      internal: [
        { code: 'LOT-2024-001', format: 'CODE39', name: 'Lot de production Janvier 2024' },
        { code: 'QC-PASS-2024-A', format: 'CODE39', name: 'Contrôle qualité réussi - Série A' },
        { code: 'SHIP-FR-001', format: 'CODE39', name: 'Expédition France - Lot 001' }
      ]
    };

    function generateBarcode() {
      const input = document.getElementById('barcodeInput').value;
      const format = document.querySelector('input[name="barcodeFormat"]:checked').value;

      if (input.trim() === "") {
        alert("Veuillez entrer une valeur.");
        return;
      }

      try {
        JsBarcode("#barcodeSvg", input, {
          format: format,
          lineColor: "#000",
          width: 2,
          height: 100,
          displayValue: true,
          margin: 10,
          background: "#ffffff"
        });

        // Show barcode info
        showBarcodeInfo(input, format);

        // Show download button
        document.getElementById('downloadBtn').style.display = 'inline-block';
      } catch (error) {
        alert('Erreur lors de la génération du code-barres: ' + error.message);
      }
    }

    function showBarcodeInfo(code, format) {
      const infoDiv = document.getElementById('barcodeInfo');
      infoDiv.innerHTML = `
        <strong>Code:</strong> ${code}<br>
        <strong>Format:</strong> ${format}<br>
        <strong>Généré le:</strong> ${new Date().toLocaleString('fr-FR')}
      `;
      infoDiv.style.display = 'block';
    }

    function loadSampleAptiv() {
      loadSampleFromCategory('aptiv');
    }

    function loadSampleStandard() {
      loadSampleFromCategory('standard');
    }

    function loadSampleInternal() {
      loadSampleFromCategory('internal');
    }

    function loadRandomSample() {
      const categories = Object.keys(sampleData);
      const randomCategory = categories[Math.floor(Math.random() * categories.length)];
      loadSampleFromCategory(randomCategory);
    }

    function loadSampleFromCategory(category) {
      const samples = sampleData[category];
      const randomSample = samples[Math.floor(Math.random() * samples.length)];

      document.getElementById('barcodeInput').value = randomSample.code;
      document.querySelector(`input[value="${randomSample.format}"]`).checked = true;

      generateBarcode();
    }

    function downloadBarcode() {
      const svg = document.getElementById('barcodeSvg');
      if (!svg.innerHTML) {
        alert('Veuillez d\'abord générer un code-barres.');
        return;
      }

      try {
        const svgData = new XMLSerializer().serializeToString(svg);
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();

        img.onload = function () {
          canvas.width = img.naturalWidth;
          canvas.height = img.naturalHeight;
          ctx.drawImage(img, 0, 0);

          const link = document.createElement('a');
          link.download = `barcode_${Date.now()}.png`;
          link.href = canvas.toDataURL('image/png');
          link.click();
        };

        img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
      } catch (error) {
        alert('Erreur lors du téléchargement: ' + error.message);
      }
    }

    // Auto-generate on Enter key
    document.addEventListener('DOMContentLoaded', function () {
      const input = document.getElementById('barcodeInput');
      if (input) {
        input.addEventListener('keypress', function (e) {
          if (e.key === 'Enter') {
            generateBarcode();
          }
        });
      }
    });
  </script>

  <!-- Load data loader script -->
  <script src="js/data-loader.js" onerror="console.error('Failed to load data loader script');"></script>

  <!-- Load our script with fallback -->
  <script src="js/script.js" type="module" onerror="console.error('Failed to load application script');">
  </script>
</body>

</html>