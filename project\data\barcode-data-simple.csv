Code,Format,Name,Category,Description,Price,Stock,MinStock,Supplier,Location,Status
APTIV-CBL-HT-001,CODE128,Cable Haute Tension 16AWG,Cables,Cable haute tension pour systemes automobiles,12.5,2450.0,500.0,Nexans France,A1-B2-C3,Active
APTIV-CON-IP67-004,CODE128,Connecteur Etanche IP67,Connecteurs,Connecteur etanche IP67 pour environnement automobile,8.75,1250.0,200.0,Prysmian Group,B2-C3-D4,Active
APTIV-FCX-ECU-007,CODE128,Faisceau de Cables ECU,Faisceaux,Faisceau principal pour unite de controle electronique,45.0,875.0,100.0,Aptiv Internal,C3-D4-E5,Active
APTIV-SEN-TEMP-012,CODE128,Capteur de Temperature,Capteurs,Capteur de temperature haute precision pour moteurs,25.3,650.0,150.0,<PERSON><PERSON>,D4-E5-F6,Active
APTIV-REL-PWR-008,CODE128,Re<PERSON>s de Puissance 40A,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON> de puissance pour systemes electriques automobiles,18.9,320.0,80.0,TE Connectivity,E5-F6-G7,Active
APTIV-FUS-MINI-025,CODE128,Fusible Mini 25A,Fusibles,Fusible mini format 25A pour protection circuits,2.15,5000.0,1000.0,Littelfuse,F6-G7-H8,Active
APTIV-LED-IND-003,CODE128,LED Indicateur Rouge,Indicateurs,LED indicateur rouge 12V pour tableau de bord,3.25,2800.0,500.0,Osram,G7-H8-I9,Active
APTIV-SW-PUSH-019,CODE128,Bouton Poussoir Etanche,Commutateurs,Bouton poussoir etanche IP65 pour applications exterieures,12.8,450.0,100.0,C&K Components,H8-I9-J10,Active
APTIV-RES-1K-001,CODE128,Resistance 1kOhm 1/4W,Resistances,Resistance carbone 1kOhm 1/4W 5% tolerance,0.05,15000.0,3000.0,Vishay,I9-J10-K11,Active
APTIV-CAP-100UF-002,CODE128,Condensateur 100uF 25V,Condensateurs,Condensateur electrolytique 100uF 25V radial,0.35,8500.0,1500.0,Panasonic,J10-K11-L12,Active
APTIV-DIO-1N4007-003,CODE128,Diode 1N4007,Diodes,Diode de redressement 1N4007 1A 1000V,0.08,12000.0,2500.0,ON Semiconductor,K11-L12-M13,Active
APTIV-IC-555-004,CODE128,Circuit Integre NE555,Circuits Integres,Timer NE555 DIP-8 multifonction,0.45,3500.0,700.0,Texas Instruments,L12-M13-N14,Active
APTIV-TRN-2N2222-005,CODE128,Transistor 2N2222,Transistors,Transistor NPN 2N2222 TO-92 40V 600mA,0.12,6800.0,1200.0,Fairchild,M13-N14-O15,Active
APTIV-PCB-MAIN-006,CODE128,Circuit Imprime Principal,PCB,Circuit imprime principal double face FR4,15.6,450.0,90.0,Aptiv Internal,N14-O15-P16,Active
APTIV-BOX-PLAS-007,CODE128,Boitier Plastique,Boitiers,Boitier plastique ABS noir IP54,4.2,1800.0,350.0,Hammond,O15-P16-Q17,Active
3760123456789,EAN13,Kit de Cablage Automobile Premium,Kits,Kit complet de cablage pour vehicules premium,89.99,125.0,25.0,Aptiv France,P16-Q17-R18,Active
3760234567890,EAN13,Connecteur Multi-Pin 24 Voies,Connecteurs,Connecteur haute performance 24 voies,34.5,280.0,50.0,Aptiv France,Q17-R18-S19,Active
3760345678901,EAN13,Capteur de Position Intelligent,Capteurs,Capteur de position avec intelligence embarquee,67.8,95.0,20.0,Aptiv France,R18-S19-T20,Active
3760456789012,EAN13,Module de Controle ECU,Modules,Module de controle electronique avance,245.0,45.0,10.0,Aptiv France,S19-T20-U21,Active
3760567890123,EAN13,Faisceau de Cables Hybride,Faisceaux,Faisceau specialise pour vehicules hybrides,156.75,78.0,15.0,Aptiv France,T20-U21-V22,Active
012345678905,UPC,Automotive Harness Kit Pro,Harnesses,Professional grade automotive harness kit,125.99,65.0,15.0,Aptiv USA,U21-V22-W23,Active
023456789016,UPC,High Voltage Cable Assembly,Cables,High voltage cable assembly for electric vehicles,89.5,120.0,25.0,Aptiv USA,V22-W23-X24,Active
034567890127,UPC,Smart Sensor Module V2,Sensors,Next generation smart sensor module,199.99,35.0,8.0,Aptiv USA,W23-X24-Y25,Active
LOT-2024-001,CODE39,Lot de production Janvier 2024,Lots,Lot de production Janvier 2024,,,,,Production Line 1,Active
LOT-2024-002,CODE39,Lot de production Fevrier 2024,Lots,Lot de production Fevrier 2024,,,,,Production Line 1,Active
LOT-2024-003,CODE39,Lot de production Mars 2024,Lots,Lot de production Mars 2024,,,,,Production Line 1,Active
QC-PASS-2024-A,CODE39,Controle qualite reussi - Serie A,Quality Control,Controle qualite reussi - Serie A,,,,,QC Station 1,Certified
QC-PASS-2024-B,CODE39,Controle qualite reussi - Serie B,Quality Control,Controle qualite reussi - Serie B,,,,,QC Station 2,Certified
QC-PASS-2024-C,CODE39,Controle qualite reussi - Serie C,Quality Control,Controle qualite reussi - Serie C,,,,,QC Station 3,Certified
QC-FAIL-2024-A,CODE39,Controle qualite echoue - Serie A,Quality Control,Controle qualite echoue - Serie A,,,,,QC Station 1,Failed
SHIP-FR-001,CODE39,Expedition France - Lot 001,Shipping,Expedition France - Lot 001,,,,,Shipping Dock,Dispatched
SHIP-FR-002,CODE39,Expedition France - Lot 002,Shipping,Expedition France - Lot 002,,,,,Shipping Dock,Dispatched
SHIP-DE-001,CODE39,Expedition Allemagne - Lot 001,Shipping,Expedition Allemagne - Lot 001,,,,,Shipping Dock,Dispatched
SHIP-ES-001,CODE39,Expedition Espagne - Lot 001,Shipping,Expedition Espagne - Lot 001,,,,,Shipping Dock,Dispatched
MAINT-EQ-001,CODE39,Equipement de maintenance 001,Maintenance,Equipement de maintenance 001,,,,,Atelier A,Operational
MAINT-EQ-002,CODE39,Equipement de maintenance 002,Maintenance,Equipement de maintenance 002,,,,,Atelier B,Operational
MAINT-EQ-003,CODE39,Equipement de maintenance 003,Maintenance,Equipement de maintenance 003,,,,,Atelier C,Maintenance Required
RMA-2024-0001,CODE39,Retour client - Defaut mineur,Returns,Retour client - Defaut mineur,,,,,Returns Dept,Processing
RMA-2024-0002,CODE39,Retour client - Defaut majeur,Returns,Retour client - Defaut majeur,,,,,Returns Dept,Processing
RMA-2024-0003,CODE39,Retour client - Produit defectueux,Returns,Retour client - Produit defectueux,,,,,Returns Dept,Rejected
01234567890128,ITF14,Caisse de 50 connecteurs IP67,Packaging,Caisse de 50 connecteurs IP67,,,,,Warehouse A,Active
02345678901239,ITF14,Palette de 20 caisses connecteurs,Packaging,Palette de 20 caisses connecteurs,,,,,Warehouse A,Active
03456789012340,ITF14,Conteneur de 100 palettes,Packaging,Conteneur de 100 palettes,,,,,Warehouse A,Active
TEST-BASIC-001,CODE128,Code de test basique,Test,Code de test basique pour developpement,,,,,Test Lab,Active
TEST-ADVANCED-002,CODE128,Code de test avance,Test,Code de test avance pour validation,,,,,Test Lab,Active
1234567890123,EAN13,EAN-13 de test standard,Test,EAN-13 de test standard,,,,,Test Lab,Active
12345678,EAN8,EAN-8 de test compact,Test,EAN-8 de test compact,,,,,Test Lab,Active
HELLO WORLD,CODE39,Texte simple Code 39,Test,Texte simple Code 39 pour test,,,,,Test Lab,Active
123456789012,UPC,UPC-A de test,Test,UPC-A de test pour marche US,,,,,Test Lab,Active
APTIV-TEST-999,CODE128,Code de test Aptiv specifique,Test,Code de test Aptiv specifique,,,,,Test Lab,Active
APTIV-WIRE-AWG12-001,CODE128,Fil electrique AWG12 Rouge,Fils,Fil electrique AWG12 rouge isole PVC,0.85,5500.0,1000.0,Nexans,A2-B3-C4,Active
APTIV-WIRE-AWG14-002,CODE128,Fil electrique AWG14 Bleu,Fils,Fil electrique AWG14 bleu isole PVC,0.65,6200.0,1200.0,Nexans,A3-B4-C5,Active
APTIV-WIRE-AWG16-003,CODE128,Fil electrique AWG16 Noir,Fils,Fil electrique AWG16 noir isole PVC,0.45,7800.0,1500.0,Nexans,A4-B5-C6,Active
APTIV-TERM-RING-004,CODE128,Cosse a sertir anneau,Cosses,Cosse a sertir anneau M6 cuivre etame,0.15,12500.0,2500.0,TE Connectivity,B5-C6-D7,Active
APTIV-TERM-FORK-005,CODE128,Cosse a sertir fourche,Cosses,Cosse a sertir fourche M5 cuivre etame,0.12,15000.0,3000.0,TE Connectivity,B6-C7-D8,Active
APTIV-HEAT-SHRINK-006,CODE128,Gaine thermoretractable,Gaines,Gaine thermoretractable 6mm noir,0.25,8900.0,1800.0,3M,C7-D8-E9,Active
APTIV-TAPE-ELEC-007,CODE128,Ruban isolant electrique,Rubans,Ruban isolant electrique PVC noir 19mm,1.85,2400.0,500.0,3M,D8-E9-F10,Active
APTIV-CLAMP-CABLE-008,CODE128,Collier de serrage cable,Colliers,Collier de serrage cable nylon noir 200mm,0.08,25000.0,5000.0,Hellermann Tyton,E9-F10-G11,Active
APTIV-GROMMET-RUB-009,CODE128,Passe-fil caoutchouc,Passe-fils,Passe-fil caoutchouc etanche 12mm,0.35,4500.0,900.0,Aptiv Internal,F10-G11-H12,Active
APTIV-SPLICE-CONN-010,CODE128,Connecteur d'epissure,Connecteurs,Connecteur d'epissure etanche 2 voies,2.45,1850.0,350.0,Deutsch,G11-H12-I13,Active
APTIV-TOOL-CRIMP-011,CODE128,Pince a sertir,Outils,Pince a sertir professionnelle isolee,45.8,25.0,5.0,Knipex,H12-I13-J14,Active
APTIV-TOOL-STRIP-012,CODE128,Pince a denuder,Outils,Pince a denuder automatique 0.5-6mm2,32.5,18.0,4.0,Weidmuller,I13-J14-K15,Active
APTIV-MULTIM-DIGI-013,CODE128,Multimetre numerique,Instruments,Multimetre numerique True RMS,125.0,12.0,3.0,Fluke,J14-K15-L16,Active
APTIV-OSC-SCOPE-014,CODE128,Oscilloscope portable,Instruments,Oscilloscope portable 2 voies 100MHz,850.0,3.0,1.0,Tektronix,K15-L16-M17,Active
APTIV-PWR-SUPPLY-015,CODE128,Alimentation de laboratoire,Instruments,Alimentation de laboratoire 0-30V 5A,245.0,8.0,2.0,Keysight,L16-M17-N18,Active
APTIV-GEN-FUNC-016,CODE128,Generateur de fonctions,Instruments,Generateur de fonctions 25MHz,420.0,5.0,1.0,Rigol,M17-N18-O19,Active
APTIV-LOAD-ELEC-017,CODE128,Charge electronique,Instruments,Charge electronique programmable 300W,680.0,4.0,1.0,BK Precision,N18-O19-P20,Active
APTIV-TEMP-PROBE-018,CODE128,Sonde de temperature,Sondes,Sonde de temperature K-type -40C a +200C,25.6,45.0,10.0,Omega,O19-P20-Q21,Active
APTIV-PRESS-SENS-019,CODE128,Capteur de pression,Capteurs,Capteur de pression 0-10 bar 4-20mA,85.4,28.0,6.0,Honeywell,P20-Q21-R22,Active
APTIV-FLOW-METER-020,CODE128,Debitmetre electronique,Debitmetres,Debitmetre electronique 1-100 L/min,156.8,15.0,3.0,Endress+Hauser,Q21-R22-S23,Active
APTIV-VALVE-SOLE-021,CODE128,Electrovanne 2/2 voies,Electrovannes,Electrovanne 2/2 voies NO 24VDC,42.3,65.0,12.0,Festo,R22-S23-T24,Active
APTIV-MOTOR-STEP-022,CODE128,Moteur pas a pas,Moteurs,Moteur pas a pas NEMA 17 1.8 degres 4.2V,28.9,35.0,8.0,Oriental Motor,S23-T24-U25,Active
APTIV-ENCOD-ROT-023,CODE128,Encodeur rotatif,Encodeurs,Encodeur rotatif incremental 1000 ppr,95.6,22.0,5.0,Heidenhain,T24-U25-V26,Active
APTIV-PROX-INDUC-024,CODE128,Capteur de proximite inductif,Capteurs,Capteur de proximite inductif M18 PNP,35.8,85.0,18.0,Pepperl+Fuchs,U25-V26-W27,Active
APTIV-PHOTO-BEAM-025,CODE128,Barriere photoelectrique,Capteurs,Barriere photoelectrique 10m PNP,125.4,12.0,3.0,Sick,V26-W27-X28,Active
APTIV-LIMIT-SWITCH-026,CODE128,Fin de course mecanique,Capteurs,Fin de course mecanique 1NO+1NF,18.5,125.0,25.0,Schneider Electric,W27-X28-Y29,Active
APTIV-RELAY-TIMER-027,CODE128,Relais temporise,Relais,Relais temporise multifonction 24VDC,65.2,45.0,10.0,Phoenix Contact,X28-Y29-Z30,Active
APTIV-CONT-MOTOR-028,CODE128,Contacteur moteur,Contacteurs,Contacteur moteur 3P 25A 24VDC,85.6,28.0,6.0,ABB,Y29-Z30-A31,Active
APTIV-THERM-PROT-029,CODE128,Protection thermique moteur,Protections,Protection thermique moteur 4-6.3A,42.8,55.0,12.0,Siemens,Z30-A31-B32,Active
APTIV-SOFT-START-030,CODE128,Demarreur progressif,Demarreurs,Demarreur progressif 7.5kW 400V,285.0,8.0,2.0,Schneider Electric,A31-B32-C33,Active
EK9-HAB-TAB3U,CODE128,Composant Twist - EK9-HAB-TAB3U,Twist,"Machine: T01, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T01,Pending
EK9-HAB-TAB3Y,CODE128,Composant Twist - EK9-HAB-TAB3Y,Twist,"Machine: T01, Ordre: 200.0, Reste: 200.0, Actions: Valider Reviser Annuler",,200.0,,Aptiv Internal,T01,Pending
EK9-HAB-TPB2LA,CODE128,Composant Twist - EK9-HAB-TPB2LA,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
EK9-PPL-TPC3I,CODE128,Composant Twist - EK9-PPL-TPC3I,Twist,"Machine: T01, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400.0,,Aptiv Internal,T01,Pending
EK9-PPL-TPC3J,CODE128,Composant Twist - EK9-PPL-TPC3J,Twist,"Machine: T01, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400.0,,Aptiv Internal,T01,Pending
EK9-PPL-TPC3L,CODE128,Composant Twist - EK9-PPL-TPC3L,Twist,"Machine: T01, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400.0,,Aptiv Internal,T01,Pending
EK9-PPL-TRC2O-MCA,CODE128,Composant Twist - EK9-PPL-TRC2O-MCA,Twist,"Machine: T01, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400.0,,Aptiv Internal,T01,Pending
EK9-PPL-TRC2P-MCA,CODE128,Composant Twist - EK9-PPL-TRC2P-MCA,Twist,"Machine: T01, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400.0,,Aptiv Internal,T01,Pending
EK9-PPL-TRC2Q,CODE128,Composant Twist - EK9-PPL-TRC2Q,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
EK9-PPL-TRC2R,CODE128,Composant Twist - EK9-PPL-TRC2R,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
EK9-PPL-TRC2R-MCA,CODE128,Composant Twist - EK9-PPL-TRC2R-MCA,Twist,"Machine: T01, Ordre: 200.0, Reste: 200.0, Actions: Valider Reviser Annuler",,200.0,,Aptiv Internal,T01,Pending
EK9-PPL-TRC2S-MCA,CODE128,Composant Twist - EK9-PPL-TRC2S-MCA,Twist,"Machine: T01, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T01,Pending
EK9-PPL-TRC2T,CODE128,Composant Twist - EK9-PPL-TRC2T,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
EK9-PPL-TRC2V,CODE128,Composant Twist - EK9-PPL-TRC2V,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
K9-HAB-TAB3E,CODE128,Composant Twist - K9-HAB-TAB3E,Twist,"Machine: T01, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T01,Pending
K9-HAB-TPB1K,CODE128,Composant Twist - K9-HAB-TPB1K,Twist,"Machine: T01, Ordre: 200.0, Reste: 200.0, Actions: Valider Reviser Annuler",,200.0,,Aptiv Internal,T01,Pending
K9-HAB-TPB1L,CODE128,Composant Twist - K9-HAB-TPB1L,Twist,"Machine: T01, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T01,Pending
K9-HAB-TPB1Q,CODE128,Composant Twist - K9-HAB-TPB1Q,Twist,"Machine: T01, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T01,Pending
K9-HAB-TPB1S,CODE128,Composant Twist - K9-HAB-TPB1S,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
K9-HAB-TPB2B,CODE128,Composant Twist - K9-HAB-TPB2B,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
K9-HAB-TPB2K,CODE128,Composant Twist - K9-HAB-TPB2K,Twist,"Machine: T01, Ordre: 500.0, Reste: 500.0, Actions: Valider Reviser Annuler",,500.0,,Aptiv Internal,T01,Pending
K9-PDB/G-TAB3X-MCA,CODE128,Composant Twist - K9-PDB/G-TAB3X-MCA,Twist,"Machine: T28, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T28,Pending
K9-PPL-TPC3C,CODE128,Composant Twist - K9-PPL-TPC3C,Twist,"Machine: T01, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T01,Pending
K9-PPL-TPC3D,CODE128,Composant Twist - K9-PPL-TPC3D,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
K9-PPL-TPC3E,CODE128,Composant Twist - K9-PPL-TPC3E,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
K9-PPL-TPC3G,CODE128,Composant Twist - K9-PPL-TPC3G,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
K9-PPL-TPC3H,CODE128,Composant Twist - K9-PPL-TPC3H,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
K9-PPL-TPC3U,CODE128,Composant Twist - K9-PPL-TPC3U,Twist,"Machine: T01, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T01,Pending
K9-PPL-TPC3V,CODE128,Composant Twist - K9-PPL-TPC3V,Twist,"Machine: T01, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T01,Pending
K9-PRAVD-TPB2N,CODE128,Composant Twist - K9-PRAVD-TPB2N,Twist,"Machine: T26, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T26,Pending
K9-PRAVG-TPB2M,CODE128,Composant Twist - K9-PRAVG-TPB2M,Twist,"Machine: T26, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T26,Pending
R8-HAB-TPB1B,CODE128,Composant Twist - R8-HAB-TPB1B,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
R8-HAB-TPB1U,CODE128,Composant Twist - R8-HAB-TPB1U,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
R8-HAB-TPB3C,CODE128,Composant Twist - R8-HAB-TPB3C,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
R8-HAB-TPB3D,CODE128,Composant Twist - R8-HAB-TPB3D,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
R8-HAB-TPB3G,CODE128,Composant Twist - R8-HAB-TPB3G,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
R8-HAB-TPB3H,CODE128,Composant Twist - R8-HAB-TPB3H,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
R8-HAB-TPB3M,CODE128,Composant Twist - R8-HAB-TPB3M,Twist,"Machine: T01, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T01,Pending
R8-HAB-TPB3U-MV,CODE128,Composant Twist - R8-HAB-TPB3U-MV,Twist,"Machine: T01, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T01,Pending
R8-HAB-TPB5C-MV,CODE128,Composant Twist - R8-HAB-TPB5C-MV,Twist,"Machine: T01, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T01,Pending
R8-HAB-TPB5D-MV,CODE128,Composant Twist - R8-HAB-TPB5D-MV,Twist,"Machine: T01, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T01,Pending
R8-HAB-TPB5E-MV,CODE128,Composant Twist - R8-HAB-TPB5E-MV,Twist,"Machine: T01, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T01,Pending
R8-HAB-TPB5F-MV,CODE128,Composant Twist - R8-HAB-TPB5F-MV,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
R8-HAB-TPB6C,CODE128,Composant Twist - R8-HAB-TPB6C,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
R8-HAB-TPB6D,CODE128,Composant Twist - R8-HAB-TPB6D,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
R8-HAB-TPB6E,CODE128,Composant Twist - R8-HAB-TPB6E,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
R8-PPLPHEV-TPC2S,CODE128,Composant Twist - R8-PPLPHEV-TPC2S,Twist,"Machine: T01, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T01,Pending
R8-PPL-TPC2E,CODE128,Composant Twist - R8-PPL-TPC2E,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
X74-HAB-TAB3R-MV,CODE128,Composant Twist - X74-HAB-TAB3R-MV,Twist,"Machine: T01, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T01,Pending
X74-HAB-TAB3X-MV,CODE128,Composant Twist - X74-HAB-TAB3X-MV,Twist,"Machine: T01, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T01,Pending
X74-HAB-TAB4Z-MV,CODE128,Composant Twist - X74-HAB-TAB4Z-MV,Twist,"Machine: T01, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T01,Pending
X74-HAB-TPB1B,CODE128,Composant Twist - X74-HAB-TPB1B,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
X74-HAB-TPB2AU,CODE128,Composant Twist - X74-HAB-TPB2AU,Twist,"Machine: T01, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T01,Pending
X74-HAB-TPB3A,CODE128,Composant Twist - X74-HAB-TPB3A,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
X74-HAB-TPB3B,CODE128,Composant Twist - X74-HAB-TPB3B,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
X74-HAB-TPB3D,CODE128,Composant Twist - X74-HAB-TPB3D,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
X74-HAB-TPB3E,CODE128,Composant Twist - X74-HAB-TPB3E,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
X74-HAB-TPB3F,CODE128,Composant Twist - X74-HAB-TPB3F,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
X74-HAB-TPB3H,CODE128,Composant Twist - X74-HAB-TPB3H,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
X74-HAB-TPB3X-MV,CODE128,Composant Twist - X74-HAB-TPB3X-MV,Twist,"Machine: T01, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T01,Pending
X74-HAB-TPB5W-MV,CODE128,Composant Twist - X74-HAB-TPB5W-MV,Twist,"Machine: T01, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T01,Pending
X74-HAB-TPB5Y-MV,CODE128,Composant Twist - X74-HAB-TPB5Y-MV,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
X74-HAB-TPB5Z-MV,CODE128,Composant Twist - X74-HAB-TPB5Z-MV,Twist,"Machine: T01, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T01,Pending
X74-HAB-TPB6D,CODE128,Composant Twist - X74-HAB-TPB6D,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
X74-HAB-TPB6E,CODE128,Composant Twist - X74-HAB-TPB6E,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T01,Pending
EK9-HAB-TPB1GZ,CODE128,Composant Twist - EK9-HAB-TPB1GZ,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
EK9-HAB-TPB1W,CODE128,Composant Twist - EK9-HAB-TPB1W,Twist,"Machine: T02, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T02,Pending
EK9-HAB-TPB2JZ,CODE128,Composant Twist - EK9-HAB-TPB2JZ,Twist,"Machine: T02, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T02,Pending
EK9-HAB-TPB2SA,CODE128,Composant Twist - EK9-HAB-TPB2SA,Twist,"Machine: T02, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T02,Pending
EK9-PPL-TPC3K,CODE128,Composant Twist - EK9-PPL-TPC3K,Twist,"Machine: T02, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400.0,,Aptiv Internal,T02,Pending
K9-HAB-TAB3F,CODE128,Composant Twist - K9-HAB-TAB3F,Twist,"Machine: T02, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T02,Pending
K9-HAB-TPB1AB,CODE128,Composant Twist - K9-HAB-TPB1AB,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
K9-HAB-TPB1AE,CODE128,Composant Twist - K9-HAB-TPB1AE,Twist,"Machine: T02, Ordre: 200.0, Reste: 200.0, Actions: Valider Reviser Annuler",,200.0,,Aptiv Internal,T02,Pending
K9-HAB-TPB1G,CODE128,Composant Twist - K9-HAB-TPB1G,Twist,"Machine: T02, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T02,Pending
K9-HAB-TPB1M,CODE128,Composant Twist - K9-HAB-TPB1M,Twist,"Machine: T02, Ordre: 500.0, Reste: 500.0, Actions: Valider Reviser Annuler",,500.0,,Aptiv Internal,T02,Pending
K9-PDB/DGM-TPA1R,CODE128,Composant Twist - K9-PDB/DGM-TPA1R,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
K9-PPL-TPC3A,CODE128,Composant Twist - K9-PPL-TPC3A,Twist,"Machine: T02, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T02,Pending
K9-PPL-TPC3B,CODE128,Composant Twist - K9-PPL-TPC3B,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
K9-PPL-TPC3F,CODE128,Composant Twist - K9-PPL-TPC3F,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
K9-PPL-TPC3T,CODE128,Composant Twist - K9-PPL-TPC3T,Twist,"Machine: T02, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T02,Pending
K9-PPL-TPC3W,CODE128,Composant Twist - K9-PPL-TPC3W,Twist,"Machine: T02, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T02,Pending
R8-HAB-TAB3C,CODE128,Composant Twist - R8-HAB-TAB3C,Twist,"Machine: T02, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T02,Pending
R8-HAB-TAB3L,CODE128,Composant Twist - R8-HAB-TAB3L,Twist,"Machine: T02, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T02,Pending
R8-HAB-TPB1M,CODE128,Composant Twist - R8-HAB-TPB1M,Twist,"Machine: T02, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T02,Pending
R8-HAB-TPB1NX,CODE128,Composant Twist - R8-HAB-TPB1NX,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
R8-HAB-TPB1T,CODE128,Composant Twist - R8-HAB-TPB1T,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
R8-HAB-TPB2AU,CODE128,Composant Twist - R8-HAB-TPB2AU,Twist,"Machine: T02, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T02,Pending
R8-HAB-TPB2BL,CODE128,Composant Twist - R8-HAB-TPB2BL,Twist,"Machine: T02, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T02,Pending
R8-HAB-TPB2H,CODE128,Composant Twist - R8-HAB-TPB2H,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
R8-HAB-TPB3A,CODE128,Composant Twist - R8-HAB-TPB3A,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
R8-HAB-TPB3B,CODE128,Composant Twist - R8-HAB-TPB3B,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
R8-HAB-TPB3E,CODE128,Composant Twist - R8-HAB-TPB3E,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
R8-HAB-TPB3F,CODE128,Composant Twist - R8-HAB-TPB3F,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
R8-HAB-TPB3L,CODE128,Composant Twist - R8-HAB-TPB3L,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
R8-HAB-TPB3S,CODE128,Composant Twist - R8-HAB-TPB3S,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
R8-HAB-TPB3T,CODE128,Composant Twist - R8-HAB-TPB3T,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
R8-HAB-TPB5B,CODE128,Composant Twist - R8-HAB-TPB5B,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
R8-PPLPHEV-TPA1J,CODE128,Composant Twist - R8-PPLPHEV-TPA1J,Twist,"Machine: T02, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T02,Pending
R8-PPLPHEV-TPC2T,CODE128,Composant Twist - R8-PPLPHEV-TPC2T,Twist,"Machine: T02, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T02,Pending
R8-PPL-TPC2D,CODE128,Composant Twist - R8-PPL-TPC2D,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
X74-HAB-m-TPB3R-m,CODE128,Composant Twist - X74-HAB-m-TPB3R-m,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
X74-HAB-m-TPB3T-m,CODE128,Composant Twist - X74-HAB-m-TPB3T-m,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
X74-HAB-TAC1A-MV,CODE128,Composant Twist - X74-HAB-TAC1A-MV,Twist,"Machine: T02, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T02,Pending
X74-HAB-TPB2N,CODE128,Composant Twist - X74-HAB-TPB2N,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
X74-HAB-TPB3C,CODE128,Composant Twist - X74-HAB-TPB3C,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
X74-HAB-TPB3J-MV,CODE128,Composant Twist - X74-HAB-TPB3J-MV,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
X74-HAB-TPB5A,CODE128,Composant Twist - X74-HAB-TPB5A,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
X74-HAB-TPB6C,CODE128,Composant Twist - X74-HAB-TPB6C,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
X74-HAB-TPC1AB-MV,CODE128,Composant Twist - X74-HAB-TPC1AB-MV,Twist,"Machine: T02, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T02,Pending
X74-HAB-TPC1C-MV,CODE128,Composant Twist - X74-HAB-TPC1C-MV,Twist,"Machine: T02, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T02,Pending
X74-HAB-TPC1D-MV,CODE128,Composant Twist - X74-HAB-TPC1D-MV,Twist,"Machine: T02, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T02,Pending
X74-PPL-TPC2I,CODE128,Composant Twist - X74-PPL-TPC2I,Twist,"Machine: T02, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T02,Pending
EK9-PDB/DGM-TPB1G,CODE128,Composant Twist - EK9-PDB/DGM-TPB1G,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
EK9-PDB/DGM-TPB1H,CODE128,Composant Twist - EK9-PDB/DGM-TPB1H,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
EK9-PDB/DPSA-TPB1FF,CODE128,Composant Twist - EK9-PDB/DPSA-TPB1FF,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-HAB-TAB3A,CODE128,Composant Twist - K9-HAB-TAB3A,Twist,"Machine: T03, Ordre: 2000.0, Reste: 2000.0, Actions: Valider Reviser Annuler",,2000.0,,Aptiv Internal,T03,Pending
K9-HAB-TAB3B,CODE128,Composant Twist - K9-HAB-TAB3B,Twist,"Machine: T03, Ordre: 900.0, Reste: 900.0, Actions: Valider Reviser Annuler",,900.0,,Aptiv Internal,T03,Pending
K9-HAB-TPB1AF,CODE128,Composant Twist - K9-HAB-TPB1AF,Twist,"Machine: T03, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T03,Pending
K9-HAB-TPB1E,CODE128,Composant Twist - K9-HAB-TPB1E,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-HAB-TPB1T,CODE128,Composant Twist - K9-HAB-TPB1T,Twist,"Machine: T03, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T03,Pending
K9-HAB-TPB1V,CODE128,Composant Twist - K9-HAB-TPB1V,Twist,"Machine: T03, Ordre: 900.0, Reste: 900.0, Actions: Valider Reviser Annuler",,900.0,,Aptiv Internal,T03,Pending
K9-HAB-TPB2A,CODE128,Composant Twist - K9-HAB-TPB2A,Twist,"Machine: T03, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T03,Pending
K9-HAB-TPB2I-MCA,CODE128,Composant Twist - K9-HAB-TPB2I-MCA,Twist,"Machine: T03, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T03,Pending
K9-HAB-TPB2LC-MCA,CODE128,Composant Twist - K9-HAB-TPB2LC-MCA,Twist,"Machine: T03, Ordre: 500.0, Reste: 500.0, Actions: Valider Reviser Annuler",,500.0,,Aptiv Internal,T03,Pending
K9-HAB-TPB2N,CODE128,Composant Twist - K9-HAB-TPB2N,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-HAB-TPB2NA-MCA,CODE128,Composant Twist - K9-HAB-TPB2NA-MCA,Twist,"Machine: T03, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T03,Pending
K9-HAB-TRB2O-MCA,CODE128,Composant Twist - K9-HAB-TRB2O-MCA,Twist,"Machine: T03, Ordre: 700.0, Reste: 700.0, Actions: Valider Reviser Annuler",,700.0,,Aptiv Internal,T03,Pending
K9-Pavillon-TPB1J,CODE128,Composant Twist - K9-Pavillon-TPB1J,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-Pavillon-TPB1L,CODE128,Composant Twist - K9-Pavillon-TPB1L,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-PDB/DGM-TAB3DD,CODE128,Composant Twist - K9-PDB/DGM-TAB3DD,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-PDB/DGM-TAB3J,CODE128,Composant Twist - K9-PDB/DGM-TAB3J,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-PDB/DGM-TAB3Y,CODE128,Composant Twist - K9-PDB/DGM-TAB3Y,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-PDB/DGM-TAB3Z,CODE128,Composant Twist - K9-PDB/DGM-TAB3Z,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-PDB/DGM-TPB1I,CODE128,Composant Twist - K9-PDB/DGM-TPB1I,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-PDB/DGM-TPB1X,CODE128,Composant Twist - K9-PDB/DGM-TPB1X,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-PDB/DPSA-TAB3BB,CODE128,Composant Twist - K9-PDB/DPSA-TAB3BB,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-PDB/DPSA-TAB3DD,CODE128,Composant Twist - K9-PDB/DPSA-TAB3DD,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-PDB/DPSA-TAB3GG,CODE128,Composant Twist - K9-PDB/DPSA-TAB3GG,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-PDB/DPSA-TAB3II,CODE128,Composant Twist - K9-PDB/DPSA-TAB3II,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-PDB/DPSA-TPB1CC,CODE128,Composant Twist - K9-PDB/DPSA-TPB1CC,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-PDB/DPSA-TPB1HH,CODE128,Composant Twist - K9-PDB/DPSA-TPB1HH,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-PDB/DPSA-TPB1I,CODE128,Composant Twist - K9-PDB/DPSA-TPB1I,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-PDB/DPSA-TPB1WW,CODE128,Composant Twist - K9-PDB/DPSA-TPB1WW,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
EK9-HAB-TPB2RZ,CODE128,Composant Twist - EK9-HAB-TPB2RZ,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
K9-PDB/GGM-TPB1WW,CODE128,Composant Twist - K9-PDB/GGM-TPB1WW,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-PDB/GGM-TPB2L,CODE128,Composant Twist - K9-PDB/GGM-TPB2L,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-PDB/GPSA-TPB1R,CODE128,Composant Twist - K9-PDB/GPSA-TPB1R,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-PDB/GPSA-TPB1WW,CODE128,Composant Twist - K9-PDB/GPSA-TPB1WW,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-PDB/GPSA-TPB2L,CODE128,Composant Twist - K9-PDB/GPSA-TPB2L,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
K9-PPL-TRA1E,CODE128,Composant Twist - K9-PPL-TRA1E,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
R8-HAB-TPB2AS,CODE128,Composant Twist - R8-HAB-TPB2AS,Twist,"Machine: T03, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T03,Pending
R8-PC AR-TPC2B,CODE128,Composant Twist - R8-PC AR-TPC2B,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
R8-PC AV-TPB1B,CODE128,Composant Twist - R8-PC AV-TPB1B,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
R8-PDB/D-TPB1A,CODE128,Composant Twist - R8-PDB/D-TPB1A,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
R8-PDB/D-TPB1B,CODE128,Composant Twist - R8-PDB/D-TPB1B,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
R8-PDB/D-TPB1D,CODE128,Composant Twist - R8-PDB/D-TPB1D,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
R8-PDB/D-TPB1H,CODE128,Composant Twist - R8-PDB/D-TPB1H,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
R8-PDB/D-TPB1M,CODE128,Composant Twist - R8-PDB/D-TPB1M,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
R8-PPL-TPA1A,CODE128,Composant Twist - R8-PPL-TPA1A,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
R8-PPL-TPA1Y,CODE128,Composant Twist - R8-PPL-TPA1Y,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
R8-PPL-TPC2F,CODE128,Composant Twist - R8-PPL-TPC2F,Twist,"Machine: T03, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T03,Pending
R8-PPL-TRA1V,CODE128,Composant Twist - R8-PPL-TRA1V,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
R8-PPL-TRB1Z,CODE128,Composant Twist - R8-PPL-TRB1Z,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
R8-SS Caisse-TRA1A,CODE128,Composant Twist - R8-SS Caisse-TRA1A,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
X74-HAB-TPB1U,CODE128,Composant Twist - X74-HAB-TPB1U,Twist,"Machine: T03, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T03,Pending
X74-HAB-TPB2AS,CODE128,Composant Twist - X74-HAB-TPB2AS,Twist,"Machine: T03, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T03,Pending
X74-HAB-TPB2M,CODE128,Composant Twist - X74-HAB-TPB2M,Twist,"Machine: T03, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T03,Pending
X74-HAB-TPB3U-MV,CODE128,Composant Twist - X74-HAB-TPB3U-MV,Twist,"Machine: T03, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T03,Pending
X74-HAB-TPB3W-MV,CODE128,Composant Twist - X74-HAB-TPB3W-MV,Twist,"Machine: T03, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T03,Pending
X74-PDB-D-TAB2J,CODE128,Composant Twist - X74-PDB-D-TAB2J,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
X74-PDB-D-TAB2K,CODE128,Composant Twist - X74-PDB-D-TAB2K,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
X74-PDB-D-TAB2N,CODE128,Composant Twist - X74-PDB-D-TAB2N,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
X74-PDB-G-TRB1I,CODE128,Composant Twist - X74-PDB-G-TRB1I,Twist,"Machine: T03, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T03,Pending
X74-PHEVPPL-TPA1E,CODE128,Composant Twist - X74-PHEVPPL-TPA1E,Twist,"Machine: T03, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T03,Pending
X74-PHEVPPL-TPA1H,CODE128,Composant Twist - X74-PHEVPPL-TPA1H,Twist,"Machine: T03, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T03,Pending
X74-PHEVPPL-TPA1I,CODE128,Composant Twist - X74-PHEVPPL-TPA1I,Twist,"Machine: T03, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T03,Pending
X74-PHEVPPL-TPC2H,CODE128,Composant Twist - X74-PHEVPPL-TPC2H,Twist,"Machine: T03, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T03,Pending
X74-PHEVPPL-TPC2U,CODE128,Composant Twist - X74-PHEVPPL-TPC2U,Twist,"Machine: T03, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T03,Pending
X74-PHEVPPL-TPC2Y,CODE128,Composant Twist - X74-PHEVPPL-TPC2Y,Twist,"Machine: T03, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T03,Pending
X74-PHEVPPL-TRA1C,CODE128,Composant Twist - X74-PHEVPPL-TRA1C,Twist,"Machine: T03, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T03,Pending
X74-PPL-TPA1A,CODE128,Composant Twist - X74-PPL-TPA1A,Twist,"Machine: T03, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T03,Pending
X74-PPL-TPA1Y,CODE128,Composant Twist - X74-PPL-TPA1Y,Twist,"Machine: T03, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T03,Pending
X74-SouC-TRA1A,CODE128,Composant Twist - X74-SouC-TRA1A,Twist,"Machine: T03, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T03,Pending
EK9-HAB-TPB2X,CODE128,Composant Twist - EK9-HAB-TPB2X,Twist,"Machine: T04, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T04,Pending
EK9-PPL-TPC2D-MCA,CODE128,Composant Twist - EK9-PPL-TPC2D-MCA,Twist,"Machine: T04, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400.0,,Aptiv Internal,T04,Pending
K9-HAB-TPB1AK,CODE128,Composant Twist - K9-HAB-TPB1AK,Twist,"Machine: T04, Ordre: 700.0, Reste: 700.0, Actions: Valider Reviser Annuler",,700.0,,Aptiv Internal,T04,Pending
K9-HAB-TPB1J,CODE128,Composant Twist - K9-HAB-TPB1J,Twist,"Machine: T04, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T04,Pending
K9-HAB-TPB1U,CODE128,Composant Twist - K9-HAB-TPB1U,Twist,"Machine: T04, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T04,Pending
K9-HAB-TPB2C,CODE128,Composant Twist - K9-HAB-TPB2C,Twist,"Machine: T04, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T04,Pending
K9-HAB-TPB2CA-MCA,CODE128,Composant Twist - K9-HAB-TPB2CA-MCA,Twist,"Machine: T04, Ordre: 900.0, Reste: 900.0, Actions: Valider Reviser Annuler",,900.0,,Aptiv Internal,T04,Pending
K9-HAB-TPB2F,CODE128,Composant Twist - K9-HAB-TPB2F,Twist,"Machine: T04, Ordre: 200.0, Reste: 200.0, Actions: Valider Reviser Annuler",,200.0,,Aptiv Internal,T04,Pending
K9-HAB-TPB2G,CODE128,Composant Twist - K9-HAB-TPB2G,Twist,"Machine: T04, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T04,Pending
K9-HAB-TPB2J,CODE128,Composant Twist - K9-HAB-TPB2J,Twist,"Machine: T04, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T04,Pending
K9-HAB-TPB2Q,CODE128,Composant Twist - K9-HAB-TPB2Q,Twist,"Machine: T04, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T04,Pending
K9-HAB-TPB2V,CODE128,Composant Twist - K9-HAB-TPB2V,Twist,"Machine: T04, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T04,Pending
K9-HAB-TPB2XA-MCA,CODE128,Composant Twist - K9-HAB-TPB2XA-MCA,Twist,"Machine: T04, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400.0,,Aptiv Internal,T04,Pending
K9-HAB-TRB1D,CODE128,Composant Twist - K9-HAB-TRB1D,Twist,"Machine: T04, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T04,Pending
K9-HAB-TRB1E,CODE128,Composant Twist - K9-HAB-TRB1E,Twist,"Machine: T04, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400.0,,Aptiv Internal,T04,Pending
K9-HAB-TRB2B,CODE128,Composant Twist - K9-HAB-TRB2B,Twist,"Machine: T04, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T04,Pending
K9-HAB-TRB2C,CODE128,Composant Twist - K9-HAB-TRB2C,Twist,"Machine: T04, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T04,Pending
K9-HAB-TRB2D,CODE128,Composant Twist - K9-HAB-TRB2D,Twist,"Machine: T04, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T04,Pending
K9-HAB-TRB2E,CODE128,Composant Twist - K9-HAB-TRB2E,Twist,"Machine: T04, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T04,Pending
K9-HAB-TRB2F,CODE128,Composant Twist - K9-HAB-TRB2F,Twist,"Machine: T04, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T04,Pending
K9-HAB-TRB2N-MCA,CODE128,Composant Twist - K9-HAB-TRB2N-MCA,Twist,"Machine: T04, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T04,Pending
K9-HAB-TRB2R-MCA,CODE128,Composant Twist - K9-HAB-TRB2R-MCA,Twist,"Machine: T04, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T04,Pending
K9-HAB-TRB2S-MCA,CODE128,Composant Twist - K9-HAB-TRB2S-MCA,Twist,"Machine: T04, Ordre: 700.0, Reste: 700.0, Actions: Valider Reviser Annuler",,700.0,,Aptiv Internal,T04,Pending
K9-PDB/GGM-TAB3E,CODE128,Composant Twist - K9-PDB/GGM-TAB3E,Twist,"Machine: T04, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T04,Pending
K9-PDB/GGM-TPA1R,CODE128,Composant Twist - K9-PDB/GGM-TPA1R,Twist,"Machine: T04, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T04,Pending
K9-PPL-TRA1AE,CODE128,Composant Twist - K9-PPL-TRA1AE,Twist,"Machine: T04, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T04,Pending
K9-PRBAT-TPB2A,CODE128,Composant Twist - K9-PRBAT-TPB2A,Twist,"Machine: T04, Ordre: 700.0, Reste: 700.0, Actions: Valider Reviser Annuler",,700.0,,Aptiv Internal,T04,Pending
R8-HAB-TAB1AB,CODE128,Composant Twist - R8-HAB-TAB1AB,Twist,"Machine: T04, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T04,Pending
R8-HAB-TAB1C,CODE128,Composant Twist - R8-HAB-TAB1C,Twist,"Machine: T04, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T04,Pending
R8-HAB-TAB2EF,CODE128,Composant Twist - R8-HAB-TAB2EF,Twist,"Machine: T04, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T04,Pending
R8-HAB-TAB2G,CODE128,Composant Twist - R8-HAB-TAB2G,Twist,"Machine: T04, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T04,Pending
R8-HAB-TPB2AK,CODE128,Composant Twist - R8-HAB-TPB2AK,Twist,"Machine: T04, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T04,Pending
R8-HAB-TPB2C,CODE128,Composant Twist - R8-HAB-TPB2C,Twist,"Machine: T04, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T04,Pending
R8-HAB-TPB2F,CODE128,Composant Twist - R8-HAB-TPB2F,Twist,"Machine: T04, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T04,Pending
R8-HAB-TPB3G-MV,CODE128,Composant Twist - R8-HAB-TPB3G-MV,Twist,"Machine: T04, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T04,Pending
R8-HAB-TPB3H-MV,CODE128,Composant Twist - R8-HAB-TPB3H-MV,Twist,"Machine: T04, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T04,Pending
R8-HAB-TPB3I-MV,CODE128,Composant Twist - R8-HAB-TPB3I-MV,Twist,"Machine: T04, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T04,Pending
R8-HAB-TPB5G-MV,CODE128,Composant Twist - R8-HAB-TPB5G-MV,Twist,"Machine: T04, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T04,Pending
R8-PPLPHEV-TPA1H,CODE128,Composant Twist - R8-PPLPHEV-TPA1H,Twist,"Machine: T04, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T04,Pending
R8-PPLPHEV-TPB1B,CODE128,Composant Twist - R8-PPLPHEV-TPB1B,Twist,"Machine: T04, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T04,Pending
R8-PPLPHEV-TPC2P,CODE128,Composant Twist - R8-PPLPHEV-TPC2P,Twist,"Machine: T04, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T04,Pending
R8-PPL-TPA1K-MV,CODE128,Composant Twist - R8-PPL-TPA1K-MV,Twist,"Machine: T04, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T04,Pending
R8-PPL-TRA1QN,CODE128,Composant Twist - R8-PPL-TRA1QN,Twist,"Machine: T04, Ordre: 200.0, Reste: 200.0, Actions: Valider Reviser Annuler",,200.0,,Aptiv Internal,T04,Pending
R8-PPL-TRB1YN,CODE128,Composant Twist - R8-PPL-TRB1YN,Twist,"Machine: T04, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T04,Pending
X74-HAB-m-TPB1C-m,CODE128,Composant Twist - X74-HAB-m-TPB1C-m,Twist,"Machine: T04, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T04,Pending
X74-HAB-TAB1A,CODE128,Composant Twist - X74-HAB-TAB1A,Twist,"Machine: T04, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T04,Pending
X74-hab-TAB1AB,CODE128,Composant Twist - X74-hab-TAB1AB,Twist,"Machine: T04, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T04,Pending
X74-HAB-TAB2A-1,CODE128,Composant Twist - X74-HAB-TAB2A-1,Twist,"Machine: T04, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T04,Pending
X74-HAB-TAB2H,CODE128,Composant Twist - X74-HAB-TAB2H,Twist,"Machine: T04, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T04,Pending
X74-HAB-TAB2J-MV,CODE128,Composant Twist - X74-HAB-TAB2J-MV,Twist,"Machine: T04, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T04,Pending
X74-HAB-TAB3B,CODE128,Composant Twist - X74-HAB-TAB3B,Twist,"Machine: T04, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T04,Pending
X74-HAB-TAB3C,CODE128,Composant Twist - X74-HAB-TAB3C,Twist,"Machine: T04, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T04,Pending
X74-HAB-TAB4Q-MV,CODE128,Composant Twist - X74-HAB-TAB4Q-MV,Twist,"Machine: T04, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T04,Pending
X74-HAB-TAB4S-MV,CODE128,Composant Twist - X74-HAB-TAB4S-MV,Twist,"Machine: T04, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T04,Pending
X74-HAB-TAB4U-MV,CODE128,Composant Twist - X74-HAB-TAB4U-MV,Twist,"Machine: T04, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T04,Pending
X74-HAB-TAB5Z-MV,CODE128,Composant Twist - X74-HAB-TAB5Z-MV,Twist,"Machine: T04, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T04,Pending
X74-HAB-TPB2AB,CODE128,Composant Twist - X74-HAB-TPB2AB,Twist,"Machine: T04, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T04,Pending
X74-HAB-TPB5B,CODE128,Composant Twist - X74-HAB-TPB5B,Twist,"Machine: T04, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T04,Pending
X74-PPL-TRA1AA,CODE128,Composant Twist - X74-PPL-TRA1AA,Twist,"Machine: T04, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T04,Pending
X74-PPL-TRA1I,CODE128,Composant Twist - X74-PPL-TRA1I,Twist,"Machine: T04, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T04,Pending
X74-PPL-TRA1O,CODE128,Composant Twist - X74-PPL-TRA1O,Twist,"Machine: T04, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T04,Pending
EK9-HAB-TPB2SZ,CODE128,Composant Twist - EK9-HAB-TPB2SZ,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
EK9-HAB-TPB2ZY,CODE128,Composant Twist - EK9-HAB-TPB2ZY,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
K9-ATTRMQAPV-TRB1A,CODE128,Composant Twist - K9-ATTRMQAPV-TRB1A,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
K9-HAB-TAB3C,CODE128,Composant Twist - K9-HAB-TAB3C,Twist,"Machine: T05, Ordre: 900.0, Reste: 900.0, Actions: Valider Reviser Annuler",,900.0,,Aptiv Internal,T05,Pending
K9-HAB-TPB1AM-MCA,CODE128,Composant Twist - K9-HAB-TPB1AM-MCA,Twist,"Machine: T05, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T05,Pending
K9-HAB-TPB2E,CODE128,Composant Twist - K9-HAB-TPB2E,Twist,"Machine: T05, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T05,Pending
K9-HAB-TPB2H,CODE128,Composant Twist - K9-HAB-TPB2H,Twist,"Machine: T05, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T05,Pending
K9-HAB-TPB2P,CODE128,Composant Twist - K9-HAB-TPB2P,Twist,"Machine: T05, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T05,Pending
K9-HAB-TPB2S,CODE128,Composant Twist - K9-HAB-TPB2S,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
K9-HAB-TPB2Z,CODE128,Composant Twist - K9-HAB-TPB2Z,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
K9-HAB-TRB1C,CODE128,Composant Twist - K9-HAB-TRB1C,Twist,"Machine: T05, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400.0,,Aptiv Internal,T05,Pending
K9-HAB-TRB1G,CODE128,Composant Twist - K9-HAB-TRB1G,Twist,"Machine: T05, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T05,Pending
K9-HAB-TRB2A,CODE128,Composant Twist - K9-HAB-TRB2A,Twist,"Machine: T05, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T05,Pending
k9-pavillon-TPB1X-MCA,CODE128,Composant Twist - k9-pavillon-TPB1X-MCA,Twist,"Machine: T05, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T05,Pending
K9-PDB/DGM-TPB2L,CODE128,Composant Twist - K9-PDB/DGM-TPB2L,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
K9-PDB/DGM-TYB2A,CODE128,Composant Twist - K9-PDB/DGM-TYB2A,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
K9-PDB/DGM-TYB2C,CODE128,Composant Twist - K9-PDB/DGM-TYB2C,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
K9-PDB/DPSA-TPB2L,CODE128,Composant Twist - K9-PDB/DPSA-TPB2L,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
K9-PDB/D-TPC1A-MCA,CODE128,Composant Twist - K9-PDB/D-TPC1A-MCA,Twist,"Machine: T05, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T05,Pending
K9-PDB/GGM-TYB2M,CODE128,Composant Twist - K9-PDB/GGM-TYB2M,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
K9-PDB/GPSA-TAB1C,CODE128,Composant Twist - K9-PDB/GPSA-TAB1C,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
K9-PDB/G-TYB2N-MCA,CODE128,Composant Twist - K9-PDB/G-TYB2N-MCA,Twist,"Machine: T05, Ordre: 900.0, Reste: 900.0, Actions: Valider Reviser Annuler",,900.0,,Aptiv Internal,T05,Pending
K9-PPL-TRA1AB,CODE128,Composant Twist - K9-PPL-TRA1AB,Twist,"Machine: T05, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T05,Pending
R8-HAB-TAB1H,CODE128,Composant Twist - R8-HAB-TAB1H,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
R8-HAB-TAB1L,CODE128,Composant Twist - R8-HAB-TAB1L,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
R8-HAB-TAB2M,CODE128,Composant Twist - R8-HAB-TAB2M,Twist,"Machine: T05, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T05,Pending
R8-HAB-TPB1N,CODE128,Composant Twist - R8-HAB-TPB1N,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
R8-HAB-TPB3I,CODE128,Composant Twist - R8-HAB-TPB3I,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
R8-HAB-TPB5A,CODE128,Composant Twist - R8-HAB-TPB5A,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
R8-HAB-TU4,CODE128,Composant Twist - R8-HAB-TU4,Twist,"Machine: T05, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T05,Pending
R8-PPLPHEV-TPB2B,CODE128,Composant Twist - R8-PPLPHEV-TPB2B,Twist,"Machine: T05, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T05,Pending
R8-PPL-TRB1Q,CODE128,Composant Twist - R8-PPL-TRB1Q,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
R8-SSCaisse-TPC1B,CODE128,Composant Twist - R8-SSCaisse-TPC1B,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
R8-SSCaisse-TPC1E,CODE128,Composant Twist - R8-SSCaisse-TPC1E,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
R8-Volet R82-TPC2A,CODE128,Composant Twist - R8-Volet R82-TPC2A,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
X74-HAB-TAB1D,CODE128,Composant Twist - X74-HAB-TAB1D,Twist,"Machine: T05, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T05,Pending
X74-HAB-TAB2D,CODE128,Composant Twist - X74-HAB-TAB2D,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
X74-HAB-TAB2O,CODE128,Composant Twist - X74-HAB-TAB2O,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
X74-HAB-TPB1S,CODE128,Composant Twist - X74-HAB-TPB1S,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
X74-HAB-TPB2AE,CODE128,Composant Twist - X74-HAB-TPB2AE,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
X74-HAB-TPB2AJ,CODE128,Composant Twist - X74-HAB-TPB2AJ,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
X74-HAB-TPB2AY,CODE128,Composant Twist - X74-HAB-TPB2AY,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
X74-HAB-TPB3L-MV,CODE128,Composant Twist - X74-HAB-TPB3L-MV,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
X74-HAB-TPB3M,CODE128,Composant Twist - X74-HAB-TPB3M,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
X74-HAB-TPC1A-MV,CODE128,Composant Twist - X74-HAB-TPC1A-MV,Twist,"Machine: T05, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T05,Pending
X74-PHEVPPL-TRB1M-TRA1A,CODE128,Composant Twist - X74-PHEVPPL-TRB1M-TRA1A,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
X74-SouC-TPC1B,CODE128,Composant Twist - X74-SouC-TPC1B,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
X74-SouC-TPC1E,CODE128,Composant Twist - X74-SouC-TPC1E,Twist,"Machine: T05, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T05,Pending
X74-SSCPHEV-TPC2B,CODE128,Composant Twist - X74-SSCPHEV-TPC2B,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
X74-SSCPHEV-TPC2N,CODE128,Composant Twist - X74-SSCPHEV-TPC2N,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
X74-Volet-m-TPB1B,CODE128,Composant Twist - X74-Volet-m-TPB1B,Twist,"Machine: T05, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T05,Pending
X74-Volet-TAB1A,CODE128,Composant Twist - X74-Volet-TAB1A,Twist,"Machine: T05, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T05,Pending
EK9-HAB-TPB2CZ,CODE128,Composant Twist - EK9-HAB-TPB2CZ,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
EK9-HAB-TPB2YY,CODE128,Composant Twist - EK9-HAB-TPB2YY,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
EK9-HAB-TRB1V,CODE128,Composant Twist - EK9-HAB-TRB1V,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
K9-HAB-TAB3D,CODE128,Composant Twist - K9-HAB-TAB3D,Twist,"Machine: T06, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T06,Pending
K9-HAB-TPB1AA,CODE128,Composant Twist - K9-HAB-TPB1AA,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
K9-HAB-TPB1N,CODE128,Composant Twist - K9-HAB-TPB1N,Twist,"Machine: T06, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T06,Pending
K9-HAB-TPB1P,CODE128,Composant Twist - K9-HAB-TPB1P,Twist,"Machine: T06, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T06,Pending
K9-HAB-TPB2AU,CODE128,Composant Twist - K9-HAB-TPB2AU,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
K9-HAB-TPB2CB-MCA,CODE128,Composant Twist - K9-HAB-TPB2CB-MCA,Twist,"Machine: T06, Ordre: 200.0, Reste: 200.0, Actions: Valider Reviser Annuler",,200.0,,Aptiv Internal,T06,Pending
K9-HAB-TPB2D,CODE128,Composant Twist - K9-HAB-TPB2D,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
K9-HAB-TPB2MA-MCA,CODE128,Composant Twist - K9-HAB-TPB2MA-MCA,Twist,"Machine: T06, Ordre: 900.0, Reste: 900.0, Actions: Valider Reviser Annuler",,900.0,,Aptiv Internal,T06,Pending
K9-HAB-TPB2R,CODE128,Composant Twist - K9-HAB-TPB2R,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
K9-HAB-TPB2Y,CODE128,Composant Twist - K9-HAB-TPB2Y,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
k9-pavillon-TPB1C-MCA,CODE128,Composant Twist - k9-pavillon-TPB1C-MCA,Twist,"Machine: T06, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T06,Pending
k9-pavillon-TPB1R-MCA,CODE128,Composant Twist - k9-pavillon-TPB1R-MCA,Twist,"Machine: T06, Ordre: 200.0, Reste: 200.0, Actions: Valider Reviser Annuler",,200.0,,Aptiv Internal,T06,Pending
K9-PDB/DGM-TAB1C,CODE128,Composant Twist - K9-PDB/DGM-TAB1C,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
K9-PDB/DPSA-TAB1C,CODE128,Composant Twist - K9-PDB/DPSA-TAB1C,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
K9-PDB/DPSA-TYB2A,CODE128,Composant Twist - K9-PDB/DPSA-TYB2A,Twist,"Machine: T06, Ordre: 800.0, Reste: 800.0, Actions: Valider Reviser Annuler",,800.0,,Aptiv Internal,T06,Pending
K9-PDB/DPSA-TYB2C,CODE128,Composant Twist - K9-PDB/DPSA-TYB2C,Twist,"Machine: T06, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T06,Pending
K9-PDB/D-TAB1D-MCA,CODE128,Composant Twist - K9-PDB/D-TAB1D-MCA,Twist,"Machine: T06, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T06,Pending
K9-PDB/D-TPB2L-MCA,CODE128,Composant Twist - K9-PDB/D-TPB2L-MCA,Twist,"Machine: T06, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T06,Pending
K9-PDB/D-TYB2O-MCA,CODE128,Composant Twist - K9-PDB/D-TYB2O-MCA,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
K9-PDB/D-TYB2P-MCA,CODE128,Composant Twist - K9-PDB/D-TYB2P-MCA,Twist,"Machine: T06, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T06,Pending
K9-PDB/D-TYB2R-MCA,CODE128,Composant Twist - K9-PDB/D-TYB2R-MCA,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
K9-PDB/D-TYB2S-MCA,CODE128,Composant Twist - K9-PDB/D-TYB2S-MCA,Twist,"Machine: T06, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T06,Pending
K9-PDB/GGM-TAB3H,CODE128,Composant Twist - K9-PDB/GGM-TAB3H,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
K9-PDB/GGM-TPB2D,CODE128,Composant Twist - K9-PDB/GGM-TPB2D,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
K9-PDB/GPSA-TPB2D,CODE128,Composant Twist - K9-PDB/GPSA-TPB2D,Twist,"Machine: T06, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T06,Pending
K9-PDB/GPSA-TYB2M,CODE128,Composant Twist - K9-PDB/GPSA-TYB2M,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
K9-PDB/G-TYB2S-MCA,CODE128,Composant Twist - K9-PDB/G-TYB2S-MCA,Twist,"Machine: T06, Ordre: 900.0, Reste: 900.0, Actions: Valider Reviser Annuler",,900.0,,Aptiv Internal,T06,Pending
K9-PPL-TRA1AA,CODE128,Composant Twist - K9-PPL-TRA1AA,Twist,"Machine: T06, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T06,Pending
K9-PPL-TRA1AC,CODE128,Composant Twist - K9-PPL-TRA1AC,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
K9-PPL-TRA1AF,CODE128,Composant Twist - K9-PPL-TRA1AF,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
K9-PPL-TRA1AJ,CODE128,Composant Twist - K9-PPL-TRA1AJ,Twist,"Machine: T06, Ordre: 800.0, Reste: 800.0, Actions: Valider Reviser Annuler",,800.0,,Aptiv Internal,T06,Pending
K9-PPL-TRA1S,CODE128,Composant Twist - K9-PPL-TRA1S,Twist,"Machine: T06, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T06,Pending
K9-PRBAT-TPB1X-MCA,CODE128,Composant Twist - K9-PRBAT-TPB1X-MCA,Twist,"Machine: T06, Ordre: 700.0, Reste: 700.0, Actions: Valider Reviser Annuler",,700.0,,Aptiv Internal,T06,Pending
R8-HAB-TAB1F,CODE128,Composant Twist - R8-HAB-TAB1F,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
R8-HAB-TAB2D,CODE128,Composant Twist - R8-HAB-TAB2D,Twist,"Machine: T06, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T06,Pending
R8-HAB-TPB1Q,CODE128,Composant Twist - R8-HAB-TPB1Q,Twist,"Machine: T06, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T06,Pending
R8-HAB-TPB2AJ,CODE128,Composant Twist - R8-HAB-TPB2AJ,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
R8-HAB-TPB3Q,CODE128,Composant Twist - R8-HAB-TPB3Q,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
R8-PAD-TPB6F,CODE128,Composant Twist - R8-PAD-TPB6F,Twist,"Machine: T06, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T06,Pending
R8-PAG-TPB6E,CODE128,Composant Twist - R8-PAG-TPB6E,Twist,"Machine: T06, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T06,Pending
R8-PPLPHEV-TPB1A-TPB1U-X,CODE128,Composant Twist - R8-PPLPHEV-TPB1A-TPB1U-X,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
R8-PPL-TPA1M,CODE128,Composant Twist - R8-PPL-TPA1M,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
R8-PPL-TPA1O,CODE128,Composant Twist - R8-PPL-TPA1O,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
R8-PPL-TPA1P,CODE128,Composant Twist - R8-PPL-TPA1P,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
R8-PPL-TPA1W,CODE128,Composant Twist - R8-PPL-TPA1W,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
R8-PPL-TPB1D,CODE128,Composant Twist - R8-PPL-TPB1D,Twist,"Machine: T06, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T06,Pending
R8-PPL-TPC1O,CODE128,Composant Twist - R8-PPL-TPC1O,Twist,"Machine: T06, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T06,Pending
R8-PPL-TPC2H,CODE128,Composant Twist - R8-PPL-TPC2H,Twist,"Machine: T06, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T06,Pending
R8-SouCPHEV-TPC2B,CODE128,Composant Twist - R8-SouCPHEV-TPC2B,Twist,"Machine: T06, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T06,Pending
R8-SouCPHEV-TPC2E,CODE128,Composant Twist - R8-SouCPHEV-TPC2E,Twist,"Machine: T06, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T06,Pending
R8-SouCPHEV-TPC2L,CODE128,Composant Twist - R8-SouCPHEV-TPC2L,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
R8-SSCaisse-TPC1A,CODE128,Composant Twist - R8-SSCaisse-TPC1A,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
R8-SSCaisse-TPC1D,CODE128,Composant Twist - R8-SSCaisse-TPC1D,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
X74-HAB-TAB2B,CODE128,Composant Twist - X74-HAB-TAB2B,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
X74-HAB-TPB1R,CODE128,Composant Twist - X74-HAB-TPB1R,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
X74-HAB-TPB1T,CODE128,Composant Twist - X74-HAB-TPB1T,Twist,"Machine: T06, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T06,Pending
X74-HAB-TPB2AD,CODE128,Composant Twist - X74-HAB-TPB2AD,Twist,"Machine: T06, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T06,Pending
X74-HAB-TPB2AK,CODE128,Composant Twist - X74-HAB-TPB2AK,Twist,"Machine: T06, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T06,Pending
X74-HAB-TPB2Q,CODE128,Composant Twist - X74-HAB-TPB2Q,Twist,"Machine: T06, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T06,Pending
X74-HAB-TPB3G,CODE128,Composant Twist - X74-HAB-TPB3G,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
X74-HAB-TPB3L,CODE128,Composant Twist - X74-HAB-TPB3L,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
X74-HAB-TPB3N-MV,CODE128,Composant Twist - X74-HAB-TPB3N-MV,Twist,"Machine: T06, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T06,Pending
X74-HAB-TPB3Q-MV,CODE128,Composant Twist - X74-HAB-TPB3Q-MV,Twist,"Machine: T06, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T06,Pending
X74-HAB-TPB4D,CODE128,Composant Twist - X74-HAB-TPB4D,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
X74-HAB-TPB4E,CODE128,Composant Twist - X74-HAB-TPB4E,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
X74-HAB-TPB4F,CODE128,Composant Twist - X74-HAB-TPB4F,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
X74-HAB-TPB5E,CODE128,Composant Twist - X74-HAB-TPB5E,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
X74-Pavillon-TPB1A,CODE128,Composant Twist - X74-Pavillon-TPB1A,Twist,"Machine: T06, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T06,Pending
X74-Pavillon-TPB1B,CODE128,Composant Twist - X74-Pavillon-TPB1B,Twist,"Machine: T06, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T06,Pending
X74-PDB-D-TRB1I,CODE128,Composant Twist - X74-PDB-D-TRB1I,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
X74-PHEVPPL-TPB1C,CODE128,Composant Twist - X74-PHEVPPL-TPB1C,Twist,"Machine: T06, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T06,Pending
X74-PPL-TPA1N-MV,CODE128,Composant Twist - X74-PPL-TPA1N-MV,Twist,"Machine: T06, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T06,Pending
X74-PPL-TRB1Z-TRB2B,CODE128,Composant Twist - X74-PPL-TRB1Z-TRB2B,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
X74-SouC-TPC1D,CODE128,Composant Twist - X74-SouC-TPC1D,Twist,"Machine: T06, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T06,Pending
X74-SSCPHEV-TPC2A,CODE128,Composant Twist - X74-SSCPHEV-TPC2A,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
X74-SSCPHEV-TPC2Q,CODE128,Composant Twist - X74-SSCPHEV-TPC2Q,Twist,"Machine: T06, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T06,Pending
EK9-PPL-TRB1A-TRB1Y,CODE128,Composant Twist - EK9-PPL-TRB1A-TRB1Y,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
K9-PPL-TRA1D,CODE128,Composant Twist - K9-PPL-TRA1D,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
K9-PPL-TRB1AJ/TRB1E,CODE128,Composant Twist - K9-PPL-TRB1AJ/TRB1E,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
K9-PPL-TRB1AJ/TRB1EE-MCA,CODE128,Composant Twist - K9-PPL-TRB1AJ/TRB1EE-MCA,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
K9-PPL-TRB1AK-TRB1F,CODE128,Composant Twist - K9-PPL-TRB1AK-TRB1F,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
K9-PPL-TRC1A1-TRC1C,CODE128,Composant Twist - K9-PPL-TRC1A1-TRC1C,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
K9-PPL-TRC2A1-TRC2B1,CODE128,Composant Twist - K9-PPL-TRC2A1-TRC2B1,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
K9-PRAVD-TAB3B-TAB3C,CODE128,Composant Twist - K9-PRAVD-TAB3B-TAB3C,Twist,"Machine: T07, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T07,Pending
K9-PRAVG-TAB3A-TAB3B,CODE128,Composant Twist - K9-PRAVG-TAB3A-TAB3B,Twist,"Machine: T07, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T07,Pending
R8-HAB-TAB3A-TAB3H,CODE128,Composant Twist - R8-HAB-TAB3A-TAB3H,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
R8-HAB-TAB3I-TAB3J,CODE128,Composant Twist - R8-HAB-TAB3I-TAB3J,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
R8-PPLPHEV-TPC2M-TPC2R,CODE128,Composant Twist - R8-PPLPHEV-TPC2M-TPC2R,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
R8-PPL-TPB1U,CODE128,Composant Twist - R8-PPL-TPB1U,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
R8-PPL-TRA1B,CODE128,Composant Twist - R8-PPL-TRA1B,Twist,"Machine: T07, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T07,Pending
R8-PPL-TRB1CR-TRB1YR,CODE128,Composant Twist - R8-PPL-TRB1CR-TRB1YR,Twist,"Machine: T07, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T07,Pending
R8-PPL-TRB1E,CODE128,Composant Twist - R8-PPL-TRB1E,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
R8-PPL-TRB1PN-TRB1OO,CODE128,Composant Twist - R8-PPL-TRB1PN-TRB1OO,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
R8-PPL-TRC1C,CODE128,Composant Twist - R8-PPL-TRC1C,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
R8-PPL-TRC1D,CODE128,Composant Twist - R8-PPL-TRC1D,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
R8-PPL-TRC1F,CODE128,Composant Twist - R8-PPL-TRC1F,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
R8-PPL-TU1,CODE128,Composant Twist - R8-PPL-TU1,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
R8-PPL-TU2,CODE128,Composant Twist - R8-PPL-TU2,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
R8-PPL-TU3,CODE128,Composant Twist - R8-PPL-TU3,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
X74-PPL-TPB2F-TPB2E,CODE128,Composant Twist - X74-PPL-TPB2F-TPB2E,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
X74-PPL-TRA1N-1-TRC1M-1,CODE128,Composant Twist - X74-PPL-TRA1N-1-TRC1M-1,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
X74-PPL-TRB1M-TRA1R,CODE128,Composant Twist - X74-PPL-TRB1M-TRA1R,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
X74-PPL-TRB1T,CODE128,Composant Twist - X74-PPL-TRB1T,Twist,"Machine: T07, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T07,Pending
X74-PPL-TRB1T-1,CODE128,Composant Twist - X74-PPL-TRB1T-1,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
X74-PPL-TRC1M-TRC1D,CODE128,Composant Twist - X74-PPL-TRC1M-TRC1D,Twist,"Machine: T07, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T07,Pending
EK9-PPL-TPC1R-MCA,CODE128,Composant Twist - EK9-PPL-TPC1R-MCA,Twist,"Machine: T08, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400.0,,Aptiv Internal,T08,Pending
K9-HAB-TPB1Z,CODE128,Composant Twist - K9-HAB-TPB1Z,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
K9-HAB-TPB2M,CODE128,Composant Twist - K9-HAB-TPB2M,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
K9-PDB/DGM-TPB1Q,CODE128,Composant Twist - K9-PDB/DGM-TPB1Q,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
K9-PDB/DGM-TPB2D,CODE128,Composant Twist - K9-PDB/DGM-TPB2D,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
K9-PDB/DPSA-TPA1Q,CODE128,Composant Twist - K9-PDB/DPSA-TPA1Q,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
K9-PDB/DPSA-TPB1R,CODE128,Composant Twist - K9-PDB/DPSA-TPB1R,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
K9-PDB/DPSA-TPB2D,CODE128,Composant Twist - K9-PDB/DPSA-TPB2D,Twist,"Machine: T08, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T08,Pending
K9-PDB/D-TAB1XW-MCA,CODE128,Composant Twist - K9-PDB/D-TAB1XW-MCA,Twist,"Machine: T08, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T08,Pending
K9-PDB/D-TAB3B-MCA,CODE128,Composant Twist - K9-PDB/D-TAB3B-MCA,Twist,"Machine: T08, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T08,Pending
K9-PDB/D-TPA1M-MCA,CODE128,Composant Twist - K9-PDB/D-TPA1M-MCA,Twist,"Machine: T08, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T08,Pending
K9-PDB/D-TPA1N-MCA,CODE128,Composant Twist - K9-PDB/D-TPA1N-MCA,Twist,"Machine: T08, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T08,Pending
K9-PDB/D-TPB1A-MCA,CODE128,Composant Twist - K9-PDB/D-TPB1A-MCA,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
K9-PDB/D-TPB1QQ-MCA,CODE128,Composant Twist - K9-PDB/D-TPB1QQ-MCA,Twist,"Machine: T08, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T08,Pending
K9-PDB/D-TPB1Z-MCA,CODE128,Composant Twist - K9-PDB/D-TPB1Z-MCA,Twist,"Machine: T08, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T08,Pending
K9-PDB/D-TRB1B-MCA,CODE128,Composant Twist - K9-PDB/D-TRB1B-MCA,Twist,"Machine: T08, Ordre: 200.0, Reste: 200.0, Actions: Valider Reviser Annuler",,200.0,,Aptiv Internal,T08,Pending
K9-PDB/D-TRB1S-MCA,CODE128,Composant Twist - K9-PDB/D-TRB1S-MCA,Twist,"Machine: T08, Ordre: 200.0, Reste: 200.0, Actions: Valider Reviser Annuler",,200.0,,Aptiv Internal,T08,Pending
K9-PDB/GGM-TYB2C,CODE128,Composant Twist - K9-PDB/GGM-TYB2C,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
K9-PDB/G-TAB1D-MCA,CODE128,Composant Twist - K9-PDB/G-TAB1D-MCA,Twist,"Machine: T08, Ordre: 900.0, Reste: 900.0, Actions: Valider Reviser Annuler",,900.0,,Aptiv Internal,T08,Pending
K9-PDB/G-TAB3Y-MCA,CODE128,Composant Twist - K9-PDB/G-TAB3Y-MCA,Twist,"Machine: T08, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T08,Pending
K9-PDB/G-TAC1A-MCA,CODE128,Composant Twist - K9-PDB/G-TAC1A-MCA,Twist,"Machine: T08, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T08,Pending
K9-PDB/G-TPB1Z-MCA,CODE128,Composant Twist - K9-PDB/G-TPB1Z-MCA,Twist,"Machine: T08, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T08,Pending
K9-PDB/G-TRB1B-MCA,CODE128,Composant Twist - K9-PDB/G-TRB1B-MCA,Twist,"Machine: T08, Ordre: 200.0, Reste: 200.0, Actions: Valider Reviser Annuler",,200.0,,Aptiv Internal,T08,Pending
K9-PDB/G-TRB1S-MCA,CODE128,Composant Twist - K9-PDB/G-TRB1S-MCA,Twist,"Machine: T08, Ordre: 200.0, Reste: 200.0, Actions: Valider Reviser Annuler",,200.0,,Aptiv Internal,T08,Pending
K9-PDB/G-TRC1A-MCA,CODE128,Composant Twist - K9-PDB/G-TRC1A-MCA,Twist,"Machine: T08, Ordre: 800.0, Reste: 800.0, Actions: Valider Reviser Annuler",,800.0,,Aptiv Internal,T08,Pending
K9-PPL-TPA1G,CODE128,Composant Twist - K9-PPL-TPA1G,Twist,"Machine: T08, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T08,Pending
K9-PPL-TRA1H,CODE128,Composant Twist - K9-PPL-TRA1H,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
K9-PPL-TRA1SX,CODE128,Composant Twist - K9-PPL-TRA1SX,Twist,"Machine: T08, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T08,Pending
K9-PPL-TRA1YY,CODE128,Composant Twist - K9-PPL-TRA1YY,Twist,"Machine: T08, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T08,Pending
K9-PPL-TRC1Y-MCA,CODE128,Composant Twist - K9-PPL-TRC1Y-MCA,Twist,"Machine: T08, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T08,Pending
K9-PPL-TRC2C-MCA,CODE128,Composant Twist - K9-PPL-TRC2C-MCA,Twist,"Machine: T08, Ordre: 900.0, Reste: 900.0, Actions: Valider Reviser Annuler",,900.0,,Aptiv Internal,T08,Pending
K9-PPL-TRC2T-MCA,CODE128,Composant Twist - K9-PPL-TRC2T-MCA,Twist,"Machine: T08, Ordre: 900.0, Reste: 900.0, Actions: Valider Reviser Annuler",,900.0,,Aptiv Internal,T08,Pending
K9-PRAVD-TPB2B,CODE128,Composant Twist - K9-PRAVD-TPB2B,Twist,"Machine: T08, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T08,Pending
K9-PRBAT-TPB1B,CODE128,Composant Twist - K9-PRBAT-TPB1B,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
R8-HAB-TPB1AA,CODE128,Composant Twist - R8-HAB-TPB1AA,Twist,"Machine: T08, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T08,Pending
R8-HAB-TPB2G,CODE128,Composant Twist - R8-HAB-TPB2G,Twist,"Machine: T08, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T08,Pending
R8-HAB-TPB3R-MV,CODE128,Composant Twist - R8-HAB-TPB3R-MV,Twist,"Machine: T08, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T08,Pending
R8-HAB-TPB3S-MV,CODE128,Composant Twist - R8-HAB-TPB3S-MV,Twist,"Machine: T08, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T08,Pending
R8-HAB-TPB3T-MV,CODE128,Composant Twist - R8-HAB-TPB3T-MV,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
R8-PAG-TPB2X,CODE128,Composant Twist - R8-PAG-TPB2X,Twist,"Machine: T08, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T08,Pending
R8-PC AV-TPB1B-MV,CODE128,Composant Twist - R8-PC AV-TPB1B-MV,Twist,"Machine: T08, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T08,Pending
R8-PDB/D-TPB2A,CODE128,Composant Twist - R8-PDB/D-TPB2A,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
R8-PDB/D-TPB2C,CODE128,Composant Twist - R8-PDB/D-TPB2C,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
R8-PDB/D-TPB2L,CODE128,Composant Twist - R8-PDB/D-TPB2L,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
R8-PDB/D-TRB1B-MV,CODE128,Composant Twist - R8-PDB/D-TRB1B-MV,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
R8-PDB/G-TPB2L,CODE128,Composant Twist - R8-PDB/G-TPB2L,Twist,"Machine: T08, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T08,Pending
R8-PPL-TPA1I,CODE128,Composant Twist - R8-PPL-TPA1I,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
R8-PPL-TPA1L-MV,CODE128,Composant Twist - R8-PPL-TPA1L-MV,Twist,"Machine: T08, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T08,Pending
R8-PPL-TPB1G,CODE128,Composant Twist - R8-PPL-TPB1G,Twist,"Machine: T08, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T08,Pending
R8-PPL-TPC1A,CODE128,Composant Twist - R8-PPL-TPC1A,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
R8-PPL-TPC2K-MV,CODE128,Composant Twist - R8-PPL-TPC2K-MV,Twist,"Machine: T08, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T08,Pending
R8-SouCPHEV-TPC2M,CODE128,Composant Twist - R8-SouCPHEV-TPC2M,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
R8-Volet R82-TAB1A,CODE128,Composant Twist - R8-Volet R82-TAB1A,Twist,"Machine: T08, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T08,Pending
X74-HAB-TPB2A,CODE128,Composant Twist - X74-HAB-TPB2A,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
X74-HAB-TPB2AA,CODE128,Composant Twist - X74-HAB-TPB2AA,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
X74-HAB-TPB2B,CODE128,Composant Twist - X74-HAB-TPB2B,Twist,"Machine: T08, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T08,Pending
X74-HAB-TPB2BB,CODE128,Composant Twist - X74-HAB-TPB2BB,Twist,"Machine: T08, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T08,Pending
X74-HAB-TPB2L,CODE128,Composant Twist - X74-HAB-TPB2L,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
X74-HAB-TPB2P,CODE128,Composant Twist - X74-HAB-TPB2P,Twist,"Machine: T08, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T08,Pending
X74-HAB-TPB2W,CODE128,Composant Twist - X74-HAB-TPB2W,Twist,"Machine: T08, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T08,Pending
X74-HAB-TPB2X,CODE128,Composant Twist - X74-HAB-TPB2X,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
X74-Pavillon-TPB2A,CODE128,Composant Twist - X74-Pavillon-TPB2A,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
X74-PDB-D-TPB2B,CODE128,Composant Twist - X74-PDB-D-TPB2B,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
X74-PDB-D-TPB2D,CODE128,Composant Twist - X74-PDB-D-TPB2D,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
X74-PDB-D-TPB2F,CODE128,Composant Twist - X74-PDB-D-TPB2F,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
X74-PDB-D-TPB2G,CODE128,Composant Twist - X74-PDB-D-TPB2G,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
X74-PDB-D-TPB2L,CODE128,Composant Twist - X74-PDB-D-TPB2L,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
X74-PPL-TPA1Z,CODE128,Composant Twist - X74-PPL-TPA1Z,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
X74-PPL-TPC2K,CODE128,Composant Twist - X74-PPL-TPC2K,Twist,"Machine: T08, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T08,Pending
X74-PRAVG-TPB2C,CODE128,Composant Twist - X74-PRAVG-TPB2C,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
X74-SSCPHEV-TPC2M,CODE128,Composant Twist - X74-SSCPHEV-TPC2M,Twist,"Machine: T08, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T08,Pending
EK9-PDB/DGM-TRB2X,CODE128,Composant Twist - EK9-PDB/DGM-TRB2X,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
EK9-PDB/DGM-TRB2Z,CODE128,Composant Twist - EK9-PDB/DGM-TRB2Z,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
EK9-PDB/DPSA-TRB2Y,CODE128,Composant Twist - EK9-PDB/DPSA-TRB2Y,Twist,"Machine: T09, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400.0,,Aptiv Internal,T09,Pending
EK9-PDB/DPSA-TRB2Z,CODE128,Composant Twist - EK9-PDB/DPSA-TRB2Z,Twist,"Machine: T09, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400.0,,Aptiv Internal,T09,Pending
EK9-PDB/GGM-TRB2Y,CODE128,Composant Twist - EK9-PDB/GGM-TRB2Y,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
EK9-PDB/GGM-TRB2Z,CODE128,Composant Twist - EK9-PDB/GGM-TRB2Z,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
EK9-PDB/GPSA-TRB2Y,CODE128,Composant Twist - EK9-PDB/GPSA-TRB2Y,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
EK9-PDB/GPSA-TRB2Z,CODE128,Composant Twist - EK9-PDB/GPSA-TRB2Z,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
EK9-PDB/G-TRB2Y-MCA,CODE128,Composant Twist - EK9-PDB/G-TRB2Y-MCA,Twist,"Machine: T09, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400.0,,Aptiv Internal,T09,Pending
EK9-PDB/G-TRB2Z-MCA,CODE128,Composant Twist - EK9-PDB/G-TRB2Z-MCA,Twist,"Machine: T09, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400.0,,Aptiv Internal,T09,Pending
EK9-PPL-TRC2E,CODE128,Composant Twist - EK9-PPL-TRC2E,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-CONSOLE-TPB2A,CODE128,Composant Twist - K9-CONSOLE-TPB2A,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-HAB-TRB1A,CODE128,Composant Twist - K9-HAB-TRB1A,Twist,"Machine: T09, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T09,Pending
K9-HAB-TRB1J,CODE128,Composant Twist - K9-HAB-TRB1J,Twist,"Machine: T09, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T09,Pending
K9-HAB-TRB2K,CODE128,Composant Twist - K9-HAB-TRB2K,Twist,"Machine: T09, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T09,Pending
K9-PDB/DGM-TAB1B,CODE128,Composant Twist - K9-PDB/DGM-TAB1B,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PDB/DGM-TAB1F,CODE128,Composant Twist - K9-PDB/DGM-TAB1F,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PDB/DGM-TAB1H,CODE128,Composant Twist - K9-PDB/DGM-TAB1H,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PDB/DGM-TAB1M,CODE128,Composant Twist - K9-PDB/DGM-TAB1M,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PDB/DGM-TRB2A,CODE128,Composant Twist - K9-PDB/DGM-TRB2A,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PDB/DGM-TRB2B,CODE128,Composant Twist - K9-PDB/DGM-TRB2B,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PDB/DPSA-TAB1B,CODE128,Composant Twist - K9-PDB/DPSA-TAB1B,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PDB/DPSA-TAB1E,CODE128,Composant Twist - K9-PDB/DPSA-TAB1E,Twist,"Machine: T09, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T09,Pending
K9-PDB/DPSA-TAB1F,CODE128,Composant Twist - K9-PDB/DPSA-TAB1F,Twist,"Machine: T09, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T09,Pending
K9-PDB/DPSA-TAB1H,CODE128,Composant Twist - K9-PDB/DPSA-TAB1H,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PDB/DPSA-TRB2A,CODE128,Composant Twist - K9-PDB/DPSA-TRB2A,Twist,"Machine: T09, Ordre: 500.0, Reste: 500.0, Actions: Valider Reviser Annuler",,500.0,,Aptiv Internal,T09,Pending
K9-PDB/DPSA-TRB2B,CODE128,Composant Twist - K9-PDB/DPSA-TRB2B,Twist,"Machine: T09, Ordre: 500.0, Reste: 500.0, Actions: Valider Reviser Annuler",,500.0,,Aptiv Internal,T09,Pending
K9-PDB/GGM-TAB1E,CODE128,Composant Twist - K9-PDB/GGM-TAB1E,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PDB/GGM-TAB1F,CODE128,Composant Twist - K9-PDB/GGM-TAB1F,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PDB/GGM-TPB2B,CODE128,Composant Twist - K9-PDB/GGM-TPB2B,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PDB/GGM-TRB2A,CODE128,Composant Twist - K9-PDB/GGM-TRB2A,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PDB/GGM-TRB2B,CODE128,Composant Twist - K9-PDB/GGM-TRB2B,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PDB/GPSA-TAB1E,CODE128,Composant Twist - K9-PDB/GPSA-TAB1E,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PDB/GPSA-TAB1F,CODE128,Composant Twist - K9-PDB/GPSA-TAB1F,Twist,"Machine: T09, Ordre: 500.0, Reste: 500.0, Actions: Valider Reviser Annuler",,500.0,,Aptiv Internal,T09,Pending
K9-PDB/GPSA-TPB2B,CODE128,Composant Twist - K9-PDB/GPSA-TPB2B,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PDB/GPSA-TRB2A,CODE128,Composant Twist - K9-PDB/GPSA-TRB2A,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PDB/GPSA-TRB2B,CODE128,Composant Twist - K9-PDB/GPSA-TRB2B,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PDB/G-TAB1B-MCA,CODE128,Composant Twist - K9-PDB/G-TAB1B-MCA,Twist,"Machine: T09, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T09,Pending
K9-PDB/G-TAB2R-MCA,CODE128,Composant Twist - K9-PDB/G-TAB2R-MCA,Twist,"Machine: T09, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T09,Pending
K9-PDB/G-TRB2A-MCA,CODE128,Composant Twist - K9-PDB/G-TRB2A-MCA,Twist,"Machine: T09, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T09,Pending
K9-PDB/G-TRB2B-MCA,CODE128,Composant Twist - K9-PDB/G-TRB2B-MCA,Twist,"Machine: T09, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T09,Pending
K9-PPLC-TRC1,CODE128,Composant Twist - K9-PPLC-TRC1,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PPL-TRA1A,CODE128,Composant Twist - K9-PPL-TRA1A,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PPL-TRA1AD,CODE128,Composant Twist - K9-PPL-TRA1AD,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PPL-TRA1AL-MCA,CODE128,Composant Twist - K9-PPL-TRA1AL-MCA,Twist,"Machine: T09, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T09,Pending
K9-PPL-TRA1B,CODE128,Composant Twist - K9-PPL-TRA1B,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PPL-TRA1Y,CODE128,Composant Twist - K9-PPL-TRA1Y,Twist,"Machine: T09, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T09,Pending
K9-PPL-TRB1L-MCA,CODE128,Composant Twist - K9-PPL-TRB1L-MCA,Twist,"Machine: T09, Ordre: 800.0, Reste: 800.0, Actions: Valider Reviser Annuler",,800.0,,Aptiv Internal,T09,Pending
K9-PPL-TRB1-MCA,CODE128,Composant Twist - K9-PPL-TRB1-MCA,Twist,"Machine: T09, Ordre: 900.0, Reste: 900.0, Actions: Valider Reviser Annuler",,900.0,,Aptiv Internal,T09,Pending
K9-PPL-TRB1U-MCA,CODE128,Composant Twist - K9-PPL-TRB1U-MCA,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
K9-PPL-TRB1W-MCA,CODE128,Composant Twist - K9-PPL-TRB1W-MCA,Twist,"Machine: T09, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T09,Pending
K9-PPL-TRC1K-MCA,CODE128,Composant Twist - K9-PPL-TRC1K-MCA,Twist,"Machine: T09, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T09,Pending
K9-PPL-TRC2A,CODE128,Composant Twist - K9-PPL-TRC2A,Twist,"Machine: T09, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T09,Pending
R8-HAB-TPB3C-MV,CODE128,Composant Twist - R8-HAB-TPB3C-MV,Twist,"Machine: T09, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T09,Pending
R8-PC AV-TPB1G-MV,CODE128,Composant Twist - R8-PC AV-TPB1G-MV,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
R8-PDB/D-TAB2K-MV,CODE128,Composant Twist - R8-PDB/D-TAB2K-MV,Twist,"Machine: T09, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T09,Pending
R8-PDB/G-TAB2K-MV,CODE128,Composant Twist - R8-PDB/G-TAB2K-MV,Twist,"Machine: T09, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T09,Pending
R8-PPL-TPB2D-MV,CODE128,Composant Twist - R8-PPL-TPB2D-MV,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
X74-ATN8-T9-TPC3A,CODE128,Composant Twist - X74-ATN8-T9-TPC3A,Twist,"Machine: T09, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T09,Pending
X74-ATN8-TPC3A,CODE128,Composant Twist - X74-ATN8-TPC3A,Twist,"Machine: T09, Ordre: 1200.0, Reste: 1200.0, Actions: Valider Reviser Annuler",,1200.0,,Aptiv Internal,T09,Pending
X74-ATN8-TPC3B,CODE128,Composant Twist - X74-ATN8-TPC3B,Twist,"Machine: T09, Ordre: 1200.0, Reste: 1200.0, Actions: Valider Reviser Annuler",,1200.0,,Aptiv Internal,T09,Pending
X74-hab-TAB3H,CODE128,Composant Twist - X74-hab-TAB3H,Twist,"Machine: T09, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T09,Pending
X74-hab-TAB3J,CODE128,Composant Twist - X74-hab-TAB3J,Twist,"Machine: T09, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T09,Pending
X74-PDB-G-TAB2D,CODE128,Composant Twist - X74-PDB-G-TAB2D,Twist,"Machine: T09, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T09,Pending
X74-PDB-G-TAB2L,CODE128,Composant Twist - X74-PDB-G-TAB2L,Twist,"Machine: T09, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T09,Pending
X74-PDB-G-TAB2N,CODE128,Composant Twist - X74-PDB-G-TAB2N,Twist,"Machine: T09, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T09,Pending
X74-PDB-G-TAB2Z,CODE128,Composant Twist - X74-PDB-G-TAB2Z,Twist,"Machine: T09, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T09,Pending
EK9-PPL-TRA1O,CODE128,Composant Twist - EK9-PPL-TRA1O,Twist,"Machine: T10, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400.0,,Aptiv Internal,T10,Pending
K9-HAB-TAB3G,CODE128,Composant Twist - K9-HAB-TAB3G,Twist,"Machine: T10, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T10,Pending
K9-PDB/DGM-TAB1E,CODE128,Composant Twist - K9-PDB/DGM-TAB1E,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
K9-PDB/DGM-TAB1X,CODE128,Composant Twist - K9-PDB/DGM-TAB1X,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
K9-PDB/DPSA-TAB1N,CODE128,Composant Twist - K9-PDB/DPSA-TAB1N,Twist,"Machine: T10, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T10,Pending
K9-PDB/D-TAB2R-MCA,CODE128,Composant Twist - K9-PDB/D-TAB2R-MCA,Twist,"Machine: T10, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T10,Pending
K9-PDB/D-TAB3U-MCA,CODE128,Composant Twist - K9-PDB/D-TAB3U-MCA,Twist,"Machine: T10, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T10,Pending
K9-PDB/D-TAB3Y-MCA,CODE128,Composant Twist - K9-PDB/D-TAB3Y-MCA,Twist,"Machine: T10, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T10,Pending
K9-PDB/GGM-TAB1X,CODE128,Composant Twist - K9-PDB/GGM-TAB1X,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
K9-PDB/GPSA-TAB1X,CODE128,Composant Twist - K9-PDB/GPSA-TAB1X,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
K9-PDB/GPSA-TRB2X,CODE128,Composant Twist - K9-PDB/GPSA-TRB2X,Twist,"Machine: T10, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T10,Pending
K9-PPL-TPC3O,CODE128,Composant Twist - K9-PPL-TPC3O,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
K9-PPL-TRA1IX,CODE128,Composant Twist - K9-PPL-TRA1IX,Twist,"Machine: T10, Ordre: 700.0, Reste: 700.0, Actions: Valider Reviser Annuler",,700.0,,Aptiv Internal,T10,Pending
K9-PPL-TRA1J,CODE128,Composant Twist - K9-PPL-TRA1J,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
K9-PPL-TRA1P,CODE128,Composant Twist - K9-PPL-TRA1P,Twist,"Machine: T10, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T10,Pending
K9-PPL-TRA1U,CODE128,Composant Twist - K9-PPL-TRA1U,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
K9-PPL-TRA1X,CODE128,Composant Twist - K9-PPL-TRA1X,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
K9-PPL-TRA1Z,CODE128,Composant Twist - K9-PPL-TRA1Z,Twist,"Machine: T10, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T10,Pending
K9-PPL-TRB1AI/TRB1D,CODE128,Composant Twist - K9-PPL-TRB1AI/TRB1D,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
K9-PPL-TRB1AI-TRB1D-MCA,CODE128,Composant Twist - K9-PPL-TRB1AI-TRB1D-MCA,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
K9-PPL-TRB1B-TRC1B,CODE128,Composant Twist - K9-PPL-TRB1B-TRC1B,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
K9-PPL-TRB1C-TRC1A,CODE128,Composant Twist - K9-PPL-TRB1C-TRC1A,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
K9-PPL-TRB1K-MCA,CODE128,Composant Twist - K9-PPL-TRB1K-MCA,Twist,"Machine: T10, Ordre: 900.0, Reste: 900.0, Actions: Valider Reviser Annuler",,900.0,,Aptiv Internal,T10,Pending
K9-PPL-TRC1K1-TRC1M-MCA,CODE128,Composant Twist - K9-PPL-TRC1K1-TRC1M-MCA,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
K9-PPL-TRC1X-MCA,CODE128,Composant Twist - K9-PPL-TRC1X-MCA,Twist,"Machine: T10, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T10,Pending
R8-CSL BVA-TPB3A,CODE128,Composant Twist - R8-CSL BVA-TPB3A,Twist,"Machine: T10, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T10,Pending
R8-CSLBVA/D-TPB3A,CODE128,Composant Twist - R8-CSLBVA/D-TPB3A,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-HAB-TPB4D,CODE128,Composant Twist - R8-HAB-TPB4D,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-HAB-TPB4E,CODE128,Composant Twist - R8-HAB-TPB4E,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-HAB-TPB4F,CODE128,Composant Twist - R8-HAB-TPB4F,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-PC AV-TPB1C,CODE128,Composant Twist - R8-PC AV-TPB1C,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-PC AV-TPB1D,CODE128,Composant Twist - R8-PC AV-TPB1D,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-PDB/D-TAB1O,CODE128,Composant Twist - R8-PDB/D-TAB1O,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-PDB/D-TRB2D,CODE128,Composant Twist - R8-PDB/D-TRB2D,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-PDB/G-TAB1O,CODE128,Composant Twist - R8-PDB/G-TAB1O,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-PDB/G-TAB1U,CODE128,Composant Twist - R8-PDB/G-TAB1U,Twist,"Machine: T10, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T10,Pending
R8-PDB/G-TAB1V,CODE128,Composant Twist - R8-PDB/G-TAB1V,Twist,"Machine: T10, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T10,Pending
R8-PDB/G-TRB1B,CODE128,Composant Twist - R8-PDB/G-TRB1B,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-PPLPHEV-TPC1D,CODE128,Composant Twist - R8-PPLPHEV-TPC1D,Twist,"Machine: T10, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T10,Pending
R8-PPLPHEV-TPC2J,CODE128,Composant Twist - R8-PPLPHEV-TPC2J,Twist,"Machine: T10, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T10,Pending
R8-PPL-TRA1A,CODE128,Composant Twist - R8-PPL-TRA1A,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-PPL-TRA1D,CODE128,Composant Twist - R8-PPL-TRA1D,Twist,"Machine: T10, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T10,Pending
R8-PPL-TRA1F,CODE128,Composant Twist - R8-PPL-TRA1F,Twist,"Machine: T10, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T10,Pending
R8-PPL-TRA1G,CODE128,Composant Twist - R8-PPL-TRA1G,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-PPL-TRA1H,CODE128,Composant Twist - R8-PPL-TRA1H,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-PPL-TRA1J,CODE128,Composant Twist - R8-PPL-TRA1J,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-PPL-TRA1K,CODE128,Composant Twist - R8-PPL-TRA1K,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-PPL-TRA1N,CODE128,Composant Twist - R8-PPL-TRA1N,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-PPL-TRA1S,CODE128,Composant Twist - R8-PPL-TRA1S,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-PPL-TRB1A,CODE128,Composant Twist - R8-PPL-TRB1A,Twist,"Machine: T10, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T10,Pending
R8-PPL-TRB1R,CODE128,Composant Twist - R8-PPL-TRB1R,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-PPL-TRC1B,CODE128,Composant Twist - R8-PPL-TRC1B,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-PPL-TRC1G,CODE128,Composant Twist - R8-PPL-TRC1G,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-PPL-TRC2D,CODE128,Composant Twist - R8-PPL-TRC2D,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
R8-SouCPHEV-TPC2K,CODE128,Composant Twist - R8-SouCPHEV-TPC2K,Twist,"Machine: T10, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T10,Pending
R8-SS Caisse-TRC2C,CODE128,Composant Twist - R8-SS Caisse-TRC2C,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
X74-AT6-TPC2A,CODE128,Composant Twist - X74-AT6-TPC2A,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
X74-AT6-TPC2B,CODE128,Composant Twist - X74-AT6-TPC2B,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
X74-ATTRMQ-TAB1D,CODE128,Composant Twist - X74-ATTRMQ-TAB1D,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
X74-HAB-TAB1K,CODE128,Composant Twist - X74-HAB-TAB1K,Twist,"Machine: T10, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T10,Pending
X74-HAB-TAB2E,CODE128,Composant Twist - X74-HAB-TAB2E,Twist,"Machine: T10, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T10,Pending
X74-HAB-TAB2G,CODE128,Composant Twist - X74-HAB-TAB2G,Twist,"Machine: T10, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T10,Pending
X74-HAB-TAB2I,CODE128,Composant Twist - X74-HAB-TAB2I,Twist,"Machine: T10, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T10,Pending
X74-HAB-TAB3A,CODE128,Composant Twist - X74-HAB-TAB3A,Twist,"Machine: T10, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T10,Pending
X74-HAB-TPB5C,CODE128,Composant Twist - X74-HAB-TPB5C,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
X74-HAB-TPB5D,CODE128,Composant Twist - X74-HAB-TPB5D,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
X74-PDB-D-TAB2D,CODE128,Composant Twist - X74-PDB-D-TAB2D,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
X74-PDB-D-TAB2F,CODE128,Composant Twist - X74-PDB-D-TAB2F,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
X74-PDB-D-TAB2L,CODE128,Composant Twist - X74-PDB-D-TAB2L,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
X74-PDB-D-TRB1K,CODE128,Composant Twist - X74-PDB-D-TRB1K,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
X74-PDB-G-TAB2F,CODE128,Composant Twist - X74-PDB-G-TAB2F,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
X74-PDB-G-TAB2H,CODE128,Composant Twist - X74-PDB-G-TAB2H,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
X74-PDB-G-TRB2A,CODE128,Composant Twist - X74-PDB-G-TRB2A,Twist,"Machine: T10, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T10,Pending
X74-PPL-TPA1B,CODE128,Composant Twist - X74-PPL-TPA1B,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
X74-PPL-TPB2A,CODE128,Composant Twist - X74-PPL-TPB2A,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
X74-PPL-TPB2B,CODE128,Composant Twist - X74-PPL-TPB2B,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
X74-PPL-TPB2E-1,CODE128,Composant Twist - X74-PPL-TPB2E-1,Twist,"Machine: T10, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T10,Pending
X74-PPL-TRA1E,CODE128,Composant Twist - X74-PPL-TRA1E,Twist,"Machine: T10, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T10,Pending
X74-PPL-TRA1H,CODE128,Composant Twist - X74-PPL-TRA1H,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
X74-PPL-TRB1H,CODE128,Composant Twist - X74-PPL-TRB1H,Twist,"Machine: T10, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T10,Pending
X74-PPL-TRB1S,CODE128,Composant Twist - X74-PPL-TRB1S,Twist,"Machine: T10, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T10,Pending
X74-PPL-TRB2A,CODE128,Composant Twist - X74-PPL-TRB2A,Twist,"Machine: T10, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T10,Pending
X74-PPL-TRC1J,CODE128,Composant Twist - X74-PPL-TRC1J,Twist,"Machine: T10, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T10,Pending
X74-PPL-TRC2W,CODE128,Composant Twist - X74-PPL-TRC2W,Twist,"Machine: T10, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300.0,,Aptiv Internal,T10,Pending
X74-SouC-TRC2C,CODE128,Composant Twist - X74-SouC-TRC2C,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T10,Pending
EK9-PPL-TPC2G-MCA,CODE128,Composant Twist - EK9-PPL-TPC2G-MCA,Twist,"Machine: T11, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400.0,,Aptiv Internal,T11,Pending
K9-HAB-TRB2M-MCA,CODE128,Composant Twist - K9-HAB-TRB2M-MCA,Twist,"Machine: T11, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T11,Pending
K9-PDB/G-TPB1N-MCA,CODE128,Composant Twist - K9-PDB/G-TPB1N-MCA,Twist,"Machine: T11, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T11,Pending
K9-PDB/G-TPB1O-MCA,CODE128,Composant Twist - K9-PDB/G-TPB1O-MCA,Twist,"Machine: T11, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T11,Pending
K9-PDB/G-TPB1Y-MCA,CODE128,Composant Twist - K9-PDB/G-TPB1Y-MCA,Twist,"Machine: T11, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T11,Pending
K9-PPL-TRA1Q,CODE128,Composant Twist - K9-PPL-TRA1Q,Twist,"Machine: T11, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T11,Pending
K9-PRAVD-TPB2A,CODE128,Composant Twist - K9-PRAVD-TPB2A,Twist,"Machine: T11, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T11,Pending
K9-PRAVG-TPB2B,CODE128,Composant Twist - K9-PRAVG-TPB2B,Twist,"Machine: T11, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T11,Pending
B787-STTA-TPC2,CODE128,Composant Twist - B787-STTA-TPC2,Twist,"Machine: T14, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400.0,,Aptiv Internal,T14,Pending
K9-HAB-TRB1X-MCA,CODE128,Composant Twist - K9-HAB-TRB1X-MCA,Twist,"Machine: T14, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T14,Pending
K9-HAB-TRB1Y-MCA,CODE128,Composant Twist - K9-HAB-TRB1Y-MCA,Twist,"Machine: T14, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T14,Pending
K9-HAB-TRB2W-MCA,CODE128,Composant Twist - K9-HAB-TRB2W-MCA,Twist,"Machine: T14, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T14,Pending
K9-HAB-TRC1A-MCA,CODE128,Composant Twist - K9-HAB-TRC1A-MCA,Twist,"Machine: T14, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T14,Pending
K9-HAB-TRC1M-MCA,CODE128,Composant Twist - K9-HAB-TRC1M-MCA,Twist,"Machine: T14, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T14,Pending
K9-HAB-TRC1N-MCA,CODE128,Composant Twist - K9-HAB-TRC1N-MCA,Twist,"Machine: T14, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T14,Pending
K9-PAVILLON-TRC1X-MCA,CODE128,Composant Twist - K9-PAVILLON-TRC1X-MCA,Twist,"Machine: T14, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T14,Pending
K9-PAVILLON-TRC1Y-MCA,CODE128,Composant Twist - K9-PAVILLON-TRC1Y-MCA,Twist,"Machine: T14, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T14,Pending
K9-PAVILLON-TRC1Z-MCA,CODE128,Composant Twist - K9-PAVILLON-TRC1Z-MCA,Twist,"Machine: T14, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T14,Pending
K9-PCAV-TRC1L-MCA,CODE128,Composant Twist - K9-PCAV-TRC1L-MCA,Twist,"Machine: T14, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T14,Pending
K9-PCAV-TRC1L-N-MCA,CODE128,Composant Twist - K9-PCAV-TRC1L-N-MCA,Twist,"Machine: T14, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T14,Pending
K9-PDB/D-TAB1CC-MCA,CODE128,Composant Twist - K9-PDB/D-TAB1CC-MCA,Twist,"Machine: T14, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T14,Pending
K9-PDB/D-TAB1EE-MCA,CODE128,Composant Twist - K9-PDB/D-TAB1EE-MCA,Twist,"Machine: T14, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100.0,,Aptiv Internal,T14,Pending
K9-PDB/G-TAB1CC-MCA,CODE128,Composant Twist - K9-PDB/G-TAB1CC-MCA,Twist,"Machine: T14, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T14,Pending
EK9-PPL-TPC3R-MCA,CODE128,Composant Twist - EK9-PPL-TPC3R-MCA,Twist,"Machine: T24, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400.0,,Aptiv Internal,T24,Pending
K9-PDB/G-TAB1MW-MCA,CODE128,Composant Twist - K9-PDB/G-TAB1MW-MCA,Twist,"Machine: T14, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T14,Pending
K9-PDB/G-TAB1P-MCA,CODE128,Composant Twist - K9-PDB/G-TAB1P-MCA,Twist,"Machine: T14, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T14,Pending
K9-PDB/G-TAB2K-MCA,CODE128,Composant Twist - K9-PDB/G-TAB2K-MCA,Twist,"Machine: T14, Ordre: 1050.0, Reste: 1050.0, Actions: Valider Reviser Annuler",,1050.0,,Aptiv Internal,T14,Pending
K9-PDB/G-TPB1K-MCA,CODE128,Composant Twist - K9-PDB/G-TPB1K-MCA,Twist,"Machine: T14, Ordre: 700.0, Reste: 700.0, Actions: Valider Reviser Annuler",,700.0,,Aptiv Internal,T14,Pending
K9-PDB/G-TPB1M-MCA,CODE128,Composant Twist - K9-PDB/G-TPB1M-MCA,Twist,"Machine: T14, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100.0,,Aptiv Internal,T14,Pending
K9-PDB/G-TRB1Q-MCA,CODE128,Composant Twist - K9-PDB/G-TRB1Q-MCA,Twist,"Machine: T14, Ordre: 500.0, Reste: 500.0, Actions: Valider Reviser Annuler",,500.0,,Aptiv Internal,T14,Pending
K9-PPL-TRA1DX,CODE128,Composant Twist - K9-PPL-TRA1DX,Twist,"Machine: T14, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T14,Pending
K9-PPL-TRA1EE-MCA,CODE128,Composant Twist - K9-PPL-TRA1EE-MCA,Twist,"Machine: T14, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T14,Pending
K9-PPL-TRA1FF-MCA,CODE128,Composant Twist - K9-PPL-TRA1FF-MCA,Twist,"Machine: T14, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T14,Pending
K9-PPL-TRA1N,CODE128,Composant Twist - K9-PPL-TRA1N,Twist,"Machine: T14, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000.0,,Aptiv Internal,T14,Pending
K9-PPL-TRA1W1-MCA,CODE128,Composant Twist - K9-PPL-TRA1W1-MCA,Twist,"Machine: T14, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T14,Pending
K9-PPL-TRA1ZZ-MCA,CODE128,Composant Twist - K9-PPL-TRA1ZZ-MCA,Twist,"Machine: T14, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600.0,,Aptiv Internal,T14,Pending
K9-PPL-TRC1N-MCA,CODE128,Composant Twist - K9-PPL-TRC1N-MCA,Twist,"Machine: T14, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T14,Pending
K9-PPL-TRC1O-MCA,CODE128,Composant Twist - K9-PPL-TRC1O-MCA,Twist,"Machine: T14, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50.0,,Aptiv Internal,T14,Pending
K9-PPL-TRC1P1-MCA,CODE128,Composant Twist - K9-PPL-TRC1P1-MCA,Twist,"Machine: T14, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25.0,,Aptiv Internal,T14,Pending
K9-PPL-TRC2U-MCA,CODE128,Composant Twist - K9-PPL-TRC2U-MCA,Twist,"Machine: T14, Ordre: 900.0, Reste: 900.0, Actions: Valider Reviser Annuler",,900.0,,Aptiv Internal,T14,Pending
K9-SousFR-TRB3A,CODE128,Composant Twist - K9-SousFR-TRB3A,Twist,"Machine: T14, Ordre: 0, Reste: 0, Actions: Valider Reviser Annuler",,,,Aptiv Internal,T14,Pending
R8-PPL-TRA1Z,CODE128,Composant Twist - R8-PPL-TRA1Z,Twist,"Machine: T14, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T14,Pending
R8-PR AR-TPB6,CODE128,Composant Twist - R8-PR AR-TPB6,Twist,"Machine: T14, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T14,Pending
X74-PDB-G-TAB3D-MV,CODE128,Composant Twist - X74-PDB-G-TAB3D-MV,Twist,"Machine: T14, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T14,Pending
X74-PDB-G-TRB1K,CODE128,Composant Twist - X74-PDB-G-TRB1K,Twist,"Machine: T14, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T14,Pending
X74-PPL-TRA1D,CODE128,Composant Twist - X74-PPL-TRA1D,Twist,"Machine: T14, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T14,Pending
X74-PPL-TRB2O,CODE128,Composant Twist - X74-PPL-TRB2O,Twist,"Machine: T14, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T14,Pending
X74-PPL-TRC1H,CODE128,Composant Twist - X74-PPL-TRC1H,Twist,"Machine: T14, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T14,Pending
X74-PPL-TRC1I,CODE128,Composant Twist - X74-PPL-TRC1I,Twist,"Machine: T14, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T14,Pending
EK9-PDB/GGM-TPB1FF,CODE128,Composant Twist - EK9-PDB/GGM-TPB1FF,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
EK9-PDB/GPSA-TPB1FF,CODE128,Composant Twist - EK9-PDB/GPSA-TPB1FF,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
EK9-PPL-TPC3R,CODE128,Composant Twist - EK9-PPL-TPC3R,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-HAB-TPB1HH-MCA,CODE128,Composant Twist - K9-HAB-TPB1HH-MCA,Twist,"Machine: T15, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T15,Pending
K9-HAB-TPB2BH,CODE128,Composant Twist - K9-HAB-TPB2BH,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-HAB-TPB2O-MCA,CODE128,Composant Twist - K9-HAB-TPB2O-MCA,Twist,"Machine: T15, Ordre: 900.0, Reste: 900.0, Actions: Valider Reviser Annuler",,900,,Aptiv Internal,T15,Pending
K9-PDB/DGM-TAB1Y,CODE128,Composant Twist - K9-PDB/DGM-TAB1Y,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DGM-TAB3B,CODE128,Composant Twist - K9-PDB/DGM-TAB3B,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DGM-TAB3C,CODE128,Composant Twist - K9-PDB/DGM-TAB3C,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DGM-TAB3D,CODE128,Composant Twist - K9-PDB/DGM-TAB3D,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DGM-TAB3E,CODE128,Composant Twist - K9-PDB/DGM-TAB3E,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DGM-TAB3F,CODE128,Composant Twist - K9-PDB/DGM-TAB3F,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DGM-TAB3G,CODE128,Composant Twist - K9-PDB/DGM-TAB3G,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DGM-TAB3H,CODE128,Composant Twist - K9-PDB/DGM-TAB3H,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DGM-TPB1B,CODE128,Composant Twist - K9-PDB/DGM-TPB1B,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DGM-TPB1D,CODE128,Composant Twist - K9-PDB/DGM-TPB1D,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DGM-TPB1E,CODE128,Composant Twist - K9-PDB/DGM-TPB1E,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DGM-TPB1W,CODE128,Composant Twist - K9-PDB/DGM-TPB1W,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DGM-TPB2B,CODE128,Composant Twist - K9-PDB/DGM-TPB2B,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DPSA-TAB1Y,CODE128,Composant Twist - K9-PDB/DPSA-TAB1Y,Twist,"Machine: T15, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600,,Aptiv Internal,T15,Pending
K9-PDB/DPSA-TAB3B,CODE128,Composant Twist - K9-PDB/DPSA-TAB3B,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DPSA-TAB3C,CODE128,Composant Twist - K9-PDB/DPSA-TAB3C,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DPSA-TAB3D,CODE128,Composant Twist - K9-PDB/DPSA-TAB3D,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DPSA-TAB3E,CODE128,Composant Twist - K9-PDB/DPSA-TAB3E,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DPSA-TAB3F,CODE128,Composant Twist - K9-PDB/DPSA-TAB3F,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DPSA-TAB3G,CODE128,Composant Twist - K9-PDB/DPSA-TAB3G,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DPSA-TAB3H,CODE128,Composant Twist - K9-PDB/DPSA-TAB3H,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DPSA-TAB3I,CODE128,Composant Twist - K9-PDB/DPSA-TAB3I,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DPSA-TPB1D,CODE128,Composant Twist - K9-PDB/DPSA-TPB1D,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DPSA-TPB1E,CODE128,Composant Twist - K9-PDB/DPSA-TPB1E,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/DPSA-TPB1W,CODE128,Composant Twist - K9-PDB/DPSA-TPB1W,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/D-TAB3A-MCA,CODE128,Composant Twist - K9-PDB/D-TAB3A-MCA,Twist,"Machine: T15, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T15,Pending
K9-PDB/D-TPB1C-MCA,CODE128,Composant Twist - K9-PDB/D-TPB1C-MCA,Twist,"Machine: T15, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T15,Pending
K9-PDB/D-TPC1C-MCA,CODE128,Composant Twist - K9-PDB/D-TPC1C-MCA,Twist,"Machine: T15, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T15,Pending
K9-PDB/GGM-TAB1M,CODE128,Composant Twist - K9-PDB/GGM-TAB1M,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/GGM-TAB1N,CODE128,Composant Twist - K9-PDB/GGM-TAB1N,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/GGM-TAB1Y,CODE128,Composant Twist - K9-PDB/GGM-TAB1Y,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/GGM-TAB3B,CODE128,Composant Twist - K9-PDB/GGM-TAB3B,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/GGM-TAB3I,CODE128,Composant Twist - K9-PDB/GGM-TAB3I,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/GGM-TPB1W,CODE128,Composant Twist - K9-PDB/GGM-TPB1W,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/GPSA-TAB1Y,CODE128,Composant Twist - K9-PDB/GPSA-TAB1Y,Twist,"Machine: T15, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T15,Pending
K9-PDB/GPSA-TAB2G,CODE128,Composant Twist - K9-PDB/GPSA-TAB2G,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/GPSA-TAB3B,CODE128,Composant Twist - K9-PDB/GPSA-TAB3B,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/GPSA-TAB3D,CODE128,Composant Twist - K9-PDB/GPSA-TAB3D,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/GPSA-TAB3I,CODE128,Composant Twist - K9-PDB/GPSA-TAB3I,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/GPSA-TPA1R,CODE128,Composant Twist - K9-PDB/GPSA-TPA1R,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/GPSA-TPB1D,CODE128,Composant Twist - K9-PDB/GPSA-TPB1D,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/GPSA-TPB1E,CODE128,Composant Twist - K9-PDB/GPSA-TPB1E,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/GPSA-TPB1F,CODE128,Composant Twist - K9-PDB/GPSA-TPB1F,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/GPSA-TPB1J,CODE128,Composant Twist - K9-PDB/GPSA-TPB1J,Twist,"Machine: T15, Ordre: 700.0, Reste: 700.0, Actions: Valider Reviser Annuler",,700,,Aptiv Internal,T15,Pending
K9-PDB/GPSA-TPB1T,CODE128,Composant Twist - K9-PDB/GPSA-TPB1T,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
K9-PDB/G-TAB1A-MCA,CODE128,Composant Twist - K9-PDB/G-TAB1A-MCA,Twist,"Machine: T15, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T15,Pending
K9-PDB/G-TPA1N-MCA,CODE128,Composant Twist - K9-PDB/G-TPA1N-MCA,Twist,"Machine: T15, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T15,Pending
K9-PLC-TAC4A-A,CODE128,Composant Twist - K9-PLC-TAC4A-A,Twist,"Machine: T15, Ordre: 900.0, Reste: 900.0, Actions: Valider Reviser Annuler",,900,,Aptiv Internal,T15,Pending
K9-PLC-TPC2A-A,CODE128,Composant Twist - K9-PLC-TPC2A-A,Twist,"Machine: T15, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600,,Aptiv Internal,T15,Pending
K9-PPL-TPA1D,CODE128,Composant Twist - K9-PPL-TPA1D,Twist,"Machine: T15, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T15,Pending
K9-PPL-TPA1J,CODE128,Composant Twist - K9-PPL-TPA1J,Twist,"Machine: T15, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600,,Aptiv Internal,T15,Pending
K9-PRAVG-TRB1A,CODE128,Composant Twist - K9-PRAVG-TRB1A,Twist,"Machine: T15, Ordre: 700.0, Reste: 700.0, Actions: Valider Reviser Annuler",,700,,Aptiv Internal,T15,Pending
R8-HAB-TPB1AC,CODE128,Composant Twist - R8-HAB-TPB1AC,Twist,"Machine: T15, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T15,Pending
R8-HAB-TPB1AD,CODE128,Composant Twist - R8-HAB-TPB1AD,Twist,"Machine: T15, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T15,Pending
R8-HAB-TPB1D,CODE128,Composant Twist - R8-HAB-TPB1D,Twist,"Machine: T15, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T15,Pending
R8-HAB-TPB2A-MV,CODE128,Composant Twist - R8-HAB-TPB2A-MV,Twist,"Machine: T15, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T15,Pending
R8-HAB-TPB3D-MV,CODE128,Composant Twist - R8-HAB-TPB3D-MV,Twist,"Machine: T15, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T15,Pending
R8-PAG-TPB1D,CODE128,Composant Twist - R8-PAG-TPB1D,Twist,"Machine: T15, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T15,Pending
R8-pavillon-TPB2B-MV,CODE128,Composant Twist - R8-pavillon-TPB2B-MV,Twist,"Machine: T15, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T15,Pending
R8-PDB/D-TPB2Q,CODE128,Composant Twist - R8-PDB/D-TPB2Q,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
R8-PDB/D-TPB3A-MV,CODE128,Composant Twist - R8-PDB/D-TPB3A-MV,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
R8-PDB/D-TPB3G-MV,CODE128,Composant Twist - R8-PDB/D-TPB3G-MV,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
R8-PDB/D-TPB3H-MV,CODE128,Composant Twist - R8-PDB/D-TPB3H-MV,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
R8-PDB/D-TPB3I-MV,CODE128,Composant Twist - R8-PDB/D-TPB3I-MV,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
R8-PDB/D-TRB1C,CODE128,Composant Twist - R8-PDB/D-TRB1C,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
R8-PDB/G-TPB1B,CODE128,Composant Twist - R8-PDB/G-TPB1B,Twist,"Machine: T15, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T15,Pending
R8-PDB/G-TPB1C,CODE128,Composant Twist - R8-PDB/G-TPB1C,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
R8-PDB/G-TPB1D,CODE128,Composant Twist - R8-PDB/G-TPB1D,Twist,"Machine: T15, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T15,Pending
R8-PDB/G-TPB1E,CODE128,Composant Twist - R8-PDB/G-TPB1E,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
R8-PDB/G-TPB1M,CODE128,Composant Twist - R8-PDB/G-TPB1M,Twist,"Machine: T15, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T15,Pending
R8-PDB/G-TPB2A,CODE128,Composant Twist - R8-PDB/G-TPB2A,Twist,"Machine: T15, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T15,Pending
R8-PPLPHEV-TPA1D,CODE128,Composant Twist - R8-PPLPHEV-TPA1D,Twist,"Machine: T15, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T15,Pending
R8-PPLPHEV-TPA1F,CODE128,Composant Twist - R8-PPLPHEV-TPA1F,Twist,"Machine: T15, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T15,Pending
R8-PPLPHEV-TPC2L,CODE128,Composant Twist - R8-PPLPHEV-TPC2L,Twist,"Machine: T15, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T15,Pending
R8-PPL-TPA1G,CODE128,Composant Twist - R8-PPL-TPA1G,Twist,"Machine: T15, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T15,Pending
R8-PPL-TRA1L,CODE128,Composant Twist - R8-PPL-TRA1L,Twist,"Machine: T15, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T15,Pending
R8-PPL-TRA1R,CODE128,Composant Twist - R8-PPL-TRA1R,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
R8-PPL-TRC1T,CODE128,Composant Twist - R8-PPL-TRC1T,Twist,"Machine: T15, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T15,Pending
R8-SouCPHEV-TPC2F,CODE128,Composant Twist - R8-SouCPHEV-TPC2F,Twist,"Machine: T15, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T15,Pending
X74-HAB-TPB2C,CODE128,Composant Twist - X74-HAB-TPB2C,Twist,"Machine: T15, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T15,Pending
X74-HAB-TPC1B-MV,CODE128,Composant Twist - X74-HAB-TPC1B-MV,Twist,"Machine: T15, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T15,Pending
X74-Pavillon-TPB1C,CODE128,Composant Twist - X74-Pavillon-TPB1C,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
X74-Pavillon-TPB1D,CODE128,Composant Twist - X74-Pavillon-TPB1D,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
X74-Pavillon-TPB3C,CODE128,Composant Twist - X74-Pavillon-TPB3C,Twist,"Machine: T15, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T15,Pending
X74-Pavillon-TPB3D-MV,CODE128,Composant Twist - X74-Pavillon-TPB3D-MV,Twist,"Machine: T15, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T15,Pending
X74-PDB-D-TPB1A-MV,CODE128,Composant Twist - X74-PDB-D-TPB1A-MV,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
X74-PDB-D-TPB2C-MV,CODE128,Composant Twist - X74-PDB-D-TPB2C-MV,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
X74-PDB-D-TPB3F-MV,CODE128,Composant Twist - X74-PDB-D-TPB3F-MV,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
X74-PDB-D-TPB3G-MV,CODE128,Composant Twist - X74-PDB-D-TPB3G-MV,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
X74-PDB-D-TPB3H-MV,CODE128,Composant Twist - X74-PDB-D-TPB3H-MV,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
X74-PDB-D-TPB3Q-MV,CODE128,Composant Twist - X74-PDB-D-TPB3Q-MV,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
X74-PDB-D-TRB2F,CODE128,Composant Twist - X74-PDB-D-TRB2F,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
X74-PDB-G-TPB2A,CODE128,Composant Twist - X74-PDB-G-TPB2A,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
X74-PDB-G-TPB3F-MV,CODE128,Composant Twist - X74-PDB-G-TPB3F-MV,Twist,"Machine: T15, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T15,Pending
X74-PDB-G-TPB3G-MV,CODE128,Composant Twist - X74-PDB-G-TPB3G-MV,Twist,"Machine: T15, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T15,Pending
X74-PDB-G-TPB3H-MV,CODE128,Composant Twist - X74-PDB-G-TPB3H-MV,Twist,"Machine: T15, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T15,Pending
X74-PDB-G-TPB3J,CODE128,Composant Twist - X74-PDB-G-TPB3J,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
X74-PDB-G-TPB3Q-MV,CODE128,Composant Twist - X74-PDB-G-TPB3Q-MV,Twist,"Machine: T15, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T15,Pending
X74-PHEVPPL-TPA1D,CODE128,Composant Twist - X74-PHEVPPL-TPA1D,Twist,"Machine: T15, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T15,Pending
X74-PHEVPPL-TPA1T,CODE128,Composant Twist - X74-PHEVPPL-TPA1T,Twist,"Machine: T15, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T15,Pending
X74-PHEVPPL-TPC2G,CODE128,Composant Twist - X74-PHEVPPL-TPC2G,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
X74-PHEVPPL-TPC2J,CODE128,Composant Twist - X74-PHEVPPL-TPC2J,Twist,"Machine: T15, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T15,Pending
X74-PHEVPPL-TPC2M,CODE128,Composant Twist - X74-PHEVPPL-TPC2M,Twist,"Machine: T15, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T15,Pending
X74-PPL-TPA1K-MV,CODE128,Composant Twist - X74-PPL-TPA1K-MV,Twist,"Machine: T15, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T15,Pending
X74-PPL-TPA1X,CODE128,Composant Twist - X74-PPL-TPA1X,Twist,"Machine: T15, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T15,Pending
X74-PPL-TPC2R-MV,CODE128,Composant Twist - X74-PPL-TPC2R-MV,Twist,"Machine: T15, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T15,Pending
X74-PPL-TPC2T,CODE128,Composant Twist - X74-PPL-TPC2T,Twist,"Machine: T15, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T15,Pending
X74-PPL-TRA1N,CODE128,Composant Twist - X74-PPL-TRA1N,Twist,"Machine: T15, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T15,Pending
X74-PPL-TRC2B,CODE128,Composant Twist - X74-PPL-TRC2B,Twist,"Machine: T15, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T15,Pending
X74-PRAVG-TAB5A,CODE128,Composant Twist - X74-PRAVG-TAB5A,Twist,"Machine: T15, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T15,Pending
EK9-PPL-TRA1WA,CODE128,Composant Twist - EK9-PPL-TRA1WA,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
EK9-PPL-TRC2F-MCA,CODE128,Composant Twist - EK9-PPL-TRC2F-MCA,Twist,"Machine: T24, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T24,Pending
K9-ARTIV-TRC1L-MCA,CODE128,Composant Twist - K9-ARTIV-TRC1L-MCA,Twist,"Machine: T24, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T24,Pending
K9-ARTIV-TRC1P-MCA,CODE128,Composant Twist - K9-ARTIV-TRC1P-MCA,Twist,"Machine: T24, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T24,Pending
K9-HAB-TPB2BC,CODE128,Composant Twist - K9-HAB-TPB2BC,Twist,"Machine: T24, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T24,Pending
K9-HAB-TPB2BS,CODE128,Composant Twist - K9-HAB-TPB2BS,Twist,"Machine: T24, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T24,Pending
K9-HAB-TRB1H,CODE128,Composant Twist - K9-HAB-TRB1H,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
K9-HAB-TRB1P-MCA,CODE128,Composant Twist - K9-HAB-TRB1P-MCA,Twist,"Machine: T24, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600,,Aptiv Internal,T24,Pending
K9-HAB-TRB2G,CODE128,Composant Twist - K9-HAB-TRB2G,Twist,"Machine: T24, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T24,Pending
K9-HAB-TRB2L,CODE128,Composant Twist - K9-HAB-TRB2L,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
K9-PDB/DGM-TAB1G,CODE128,Composant Twist - K9-PDB/DGM-TAB1G,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
K9-PDB/DGM-TAB2C,CODE128,Composant Twist - K9-PDB/DGM-TAB2C,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
K9-PDB/DPSA-TAB1G,CODE128,Composant Twist - K9-PDB/DPSA-TAB1G,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
K9-PDB/DPSA-TAB1M,CODE128,Composant Twist - K9-PDB/DPSA-TAB1M,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
K9-PDB/DPSA-TAB2C,CODE128,Composant Twist - K9-PDB/DPSA-TAB2C,Twist,"Machine: T24, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600,,Aptiv Internal,T24,Pending
K9-PDB/DPSA-TAB2M,CODE128,Composant Twist - K9-PDB/DPSA-TAB2M,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
K9-PDB/DPSA-TPB1C,CODE128,Composant Twist - K9-PDB/DPSA-TPB1C,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
K9-PDB/DPSA-TPB1H,CODE128,Composant Twist - K9-PDB/DPSA-TPB1H,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
K9-PDB/DPSA-TPB1K,CODE128,Composant Twist - K9-PDB/DPSA-TPB1K,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
K9-PDB/DPSA-TPB1T,CODE128,Composant Twist - K9-PDB/DPSA-TPB1T,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
K9-PDB/D-TAB1A-MCA,CODE128,Composant Twist - K9-PDB/D-TAB1A-MCA,Twist,"Machine: T24, Ordre: 700.0, Reste: 700.0, Actions: Valider Reviser Annuler",,700,,Aptiv Internal,T24,Pending
K9-PDB/D-TAB1B-MCA,CODE128,Composant Twist - K9-PDB/D-TAB1B-MCA,Twist,"Machine: T24, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T24,Pending
K9-PDB/D-TAB1FF-MCA,CODE128,Composant Twist - K9-PDB/D-TAB1FF-MCA,Twist,"Machine: T24, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T24,Pending
K9-PDB/D-TAB2K-MCA,CODE128,Composant Twist - K9-PDB/D-TAB2K-MCA,Twist,"Machine: T24, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T24,Pending
K9-PDB/GGM-TAB1B,CODE128,Composant Twist - K9-PDB/GGM-TAB1B,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
K9-PDB/GGM-TAB1G,CODE128,Composant Twist - K9-PDB/GGM-TAB1G,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
K9-PDB/GGM-TAB2A,CODE128,Composant Twist - K9-PDB/GGM-TAB2A,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
K9-PDB/GGM-TAB2C,CODE128,Composant Twist - K9-PDB/GGM-TAB2C,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
K9-PDB/GPSA-TAB1B,CODE128,Composant Twist - K9-PDB/GPSA-TAB1B,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
K9-PDB/GPSA-TAB1M,CODE128,Composant Twist - K9-PDB/GPSA-TAB1M,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
K9-PDB/GPSA-TAB2A,CODE128,Composant Twist - K9-PDB/GPSA-TAB2A,Twist,"Machine: T24, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T24,Pending
K9-PDB/GPSA-TAB2C,CODE128,Composant Twist - K9-PDB/GPSA-TAB2C,Twist,"Machine: T24, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T24,Pending
K9-PDB/GPSA-TAB2F,CODE128,Composant Twist - K9-PDB/GPSA-TAB2F,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
K9-PDB/G-TAB1FF-MCA,CODE128,Composant Twist - K9-PDB/G-TAB1FF-MCA,Twist,"Machine: T24, Ordre: 800.0, Reste: 800.0, Actions: Valider Reviser Annuler",,800,,Aptiv Internal,T24,Pending
K9-PDB/G-TPB1C-MCA,CODE128,Composant Twist - K9-PDB/G-TPB1C-MCA,Twist,"Machine: T24, Ordre: 500.0, Reste: 500.0, Actions: Valider Reviser Annuler",,500,,Aptiv Internal,T24,Pending
K9-PPL-TPA1I,CODE128,Composant Twist - K9-PPL-TPA1I,Twist,"Machine: T24, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600,,Aptiv Internal,T24,Pending
X74-SSCPHEV-TPC2K,CODE128,Composant Twist - X74-SSCPHEV-TPC2K,Twist,"Machine: T10, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T10,Pending
K9-PPL-TRA1L,CODE128,Composant Twist - K9-PPL-TRA1L,Twist,"Machine: T24, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T24,Pending
K9-PPL-TRA1T,CODE128,Composant Twist - K9-PPL-TRA1T,Twist,"Machine: T24, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T24,Pending
K9-PPL-TRA1UU-MCA,CODE128,Composant Twist - K9-PPL-TRA1UU-MCA,Twist,"Machine: T24, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T24,Pending
K9-PPL-TRA1V-MCA,CODE128,Composant Twist - K9-PPL-TRA1V-MCA,Twist,"Machine: T24, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T24,Pending
K9-PPL-TRA1XX-MCA,CODE128,Composant Twist - K9-PPL-TRA1XX-MCA,Twist,"Machine: T24, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T24,Pending
K9-PRAVG-TPB2A,CODE128,Composant Twist - K9-PRAVG-TPB2A,Twist,"Machine: T24, Ordre: 700.0, Reste: 700.0, Actions: Valider Reviser Annuler",,700,,Aptiv Internal,T24,Pending
R8-CSLBVA/D-TAB3A-MV,CODE128,Composant Twist - R8-CSLBVA/D-TAB3A-MV,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-CSLBVA/D-TRB2F-MV,CODE128,Composant Twist - R8-CSLBVA/D-TRB2F-MV,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-CSLBVA/D-TRB2G-MV,CODE128,Composant Twist - R8-CSLBVA/D-TRB2G-MV,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-CSLBVA-TAB3A-mv-BVA-MV,CODE128,Composant Twist - R8-CSLBVA-TAB3A-mv-BVA-MV,Twist,"Machine: T24, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T24,Pending
R8-CSLBVA-TRB2F-MV,CODE128,Composant Twist - R8-CSLBVA-TRB2F-MV,Twist,"Machine: T24, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T24,Pending
R8-CSLBVA-TRB2G-MV,CODE128,Composant Twist - R8-CSLBVA-TRB2G-MV,Twist,"Machine: T24, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T24,Pending
R8-HAB-TAB2E,CODE128,Composant Twist - R8-HAB-TAB2E,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-HAB-TPB1E,CODE128,Composant Twist - R8-HAB-TPB1E,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-HAB-TPB1K,CODE128,Composant Twist - R8-HAB-TPB1K,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-HAB-TPB2AQ,CODE128,Composant Twist - R8-HAB-TPB2AQ,Twist,"Machine: T24, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T24,Pending
R8-Pavillon-TPB2A,CODE128,Composant Twist - R8-Pavillon-TPB2A,Twist,"Machine: T24, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T24,Pending
R8-Pavillon-TPB2B,CODE128,Composant Twist - R8-Pavillon-TPB2B,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-PDB/D-TAB1A,CODE128,Composant Twist - R8-PDB/D-TAB1A,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-PDB/D-TAB1M,CODE128,Composant Twist - R8-PDB/D-TAB1M,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-PDB/D-TAB1N,CODE128,Composant Twist - R8-PDB/D-TAB1N,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-PDB/D-TAB1T,CODE128,Composant Twist - R8-PDB/D-TAB1T,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-PDB/D-TAB1V,CODE128,Composant Twist - R8-PDB/D-TAB1V,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-PDB/D-TAB3A-MV,CODE128,Composant Twist - R8-PDB/D-TAB3A-MV,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-PDB/D-TAB3B,CODE128,Composant Twist - R8-PDB/D-TAB3B,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-PDB/D-TAB3C,CODE128,Composant Twist - R8-PDB/D-TAB3C,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-PDB/D-TPC1A-MV,CODE128,Composant Twist - R8-PDB/D-TPC1A-MV,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-PDB/G-TAB3A-MV,CODE128,Composant Twist - R8-PDB/G-TAB3A-MV,Twist,"Machine: T24, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T24,Pending
R8-PDB/G-TPB2P,CODE128,Composant Twist - R8-PDB/G-TPB2P,Twist,"Machine: T24, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T24,Pending
R8-PDB/G-TPB3A,CODE128,Composant Twist - R8-PDB/G-TPB3A,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-PDB/G-TPB3C,CODE128,Composant Twist - R8-PDB/G-TPB3C,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-PDB/G-TPB3G-MV,CODE128,Composant Twist - R8-PDB/G-TPB3G-MV,Twist,"Machine: T24, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T24,Pending
R8-PDB/G-TPB3H-MV,CODE128,Composant Twist - R8-PDB/G-TPB3H-MV,Twist,"Machine: T24, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T24,Pending
R8-PDB/G-TPC1A-MV,CODE128,Composant Twist - R8-PDB/G-TPC1A-MV,Twist,"Machine: T24, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T24,Pending
R8-PDB/G-TRB1A,CODE128,Composant Twist - R8-PDB/G-TRB1A,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-PDB/G-TRB1BC,CODE128,Composant Twist - R8-PDB/G-TRB1BC,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-PDB/G-TRB1B-MV,CODE128,Composant Twist - R8-PDB/G-TRB1B-MV,Twist,"Machine: T24, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T24,Pending
R8-PPLPHEV-TPC2O,CODE128,Composant Twist - R8-PPLPHEV-TPC2O,Twist,"Machine: T24, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T24,Pending
R8-PPLPHEV-TPC2W,CODE128,Composant Twist - R8-PPLPHEV-TPC2W,Twist,"Machine: T24, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T24,Pending
R8-PPLPHEV-TPC2Y,CODE128,Composant Twist - R8-PPLPHEV-TPC2Y,Twist,"Machine: T24, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T24,Pending
R8-PPL-TRC2E,CODE128,Composant Twist - R8-PPL-TRC2E,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-PPL-TRC2G,CODE128,Composant Twist - R8-PPL-TRC2G,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-SouCPHEV-TPC2C,CODE128,Composant Twist - R8-SouCPHEV-TPC2C,Twist,"Machine: T24, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T24,Pending
R8-SouCPHEV-TPC2J,CODE128,Composant Twist - R8-SouCPHEV-TPC2J,Twist,"Machine: T24, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T24,Pending
R8-SS Caisse-TRA1D,CODE128,Composant Twist - R8-SS Caisse-TRA1D,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
R8-STTA-TPC2A,CODE128,Composant Twist - R8-STTA-TPC2A,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-Artive-TPC21,CODE128,Composant Twist - X74-Artive-TPC21,Twist,"Machine: T24, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T24,Pending
X74-Artive-TPC2H,CODE128,Composant Twist - X74-Artive-TPC2H,Twist,"Machine: T24, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T24,Pending
X74-HAB-TAB2A,CODE128,Composant Twist - X74-HAB-TAB2A,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-HAB-TPB2E,CODE128,Composant Twist - X74-HAB-TPB2E,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-PDB-D-TAB2C-MV,CODE128,Composant Twist - X74-PDB-D-TAB2C-MV,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-PDB-D-TAB2K-MV,CODE128,Composant Twist - X74-PDB-D-TAB2K-MV,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-PDB-D-TAB2M,CODE128,Composant Twist - X74-PDB-D-TAB2M,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-PDB-D-TAB3D-MV,CODE128,Composant Twist - X74-PDB-D-TAB3D-MV,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-PDB-D-TRB1A,CODE128,Composant Twist - X74-PDB-D-TRB1A,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-PDB-G-TAB2J,CODE128,Composant Twist - X74-PDB-G-TAB2J,Twist,"Machine: T24, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T24,Pending
X74-PDB-G-TAB2K,CODE128,Composant Twist - X74-PDB-G-TAB2K,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-PDB-G-TAB2M,CODE128,Composant Twist - X74-PDB-G-TAB2M,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-PDB-G-TAB2R,CODE128,Composant Twist - X74-PDB-G-TAB2R,Twist,"Machine: T24, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T24,Pending
X74-PDB-G-TAB3B,CODE128,Composant Twist - X74-PDB-G-TAB3B,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-PDB-G-TAB3C,CODE128,Composant Twist - X74-PDB-G-TAB3C,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-PDB-G-TPB1H,CODE128,Composant Twist - X74-PDB-G-TPB1H,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-PDB-G-TPB3B,CODE128,Composant Twist - X74-PDB-G-TPB3B,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-PDB-G-TPB3C,CODE128,Composant Twist - X74-PDB-G-TPB3C,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-PDB-G-TPB3D,CODE128,Composant Twist - X74-PDB-G-TPB3D,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-PHEVPPL-TPB2A,CODE128,Composant Twist - X74-PHEVPPL-TPB2A,Twist,"Machine: T24, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T24,Pending
X74-PHEVPPL-TPB2W,CODE128,Composant Twist - X74-PHEVPPL-TPB2W,Twist,"Machine: T24, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T24,Pending
X74-PPL-TPB2C,CODE128,Composant Twist - X74-PPL-TPB2C,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-PPL-TRA1V,CODE128,Composant Twist - X74-PPL-TRA1V,Twist,"Machine: T24, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T24,Pending
X74-PPL-TRC2F,CODE128,Composant Twist - X74-PPL-TRC2F,Twist,"Machine: T24, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T24,Pending
X74-PRAR-TAB3B,CODE128,Composant Twist - X74-PRAR-TAB3B,Twist,"Machine: T24, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T24,Pending
X74-PRAVG-TPB2D,CODE128,Composant Twist - X74-PRAVG-TPB2D,Twist,"Machine: T24, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T24,Pending
X74-SouC-TRA1D,CODE128,Composant Twist - X74-SouC-TRA1D,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-SSCPHEV-TPC2C,CODE128,Composant Twist - X74-SSCPHEV-TPC2C,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-SSCPHEV-TPC2E,CODE128,Composant Twist - X74-SSCPHEV-TPC2E,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-SSCPHEV-TPC2G,CODE128,Composant Twist - X74-SSCPHEV-TPC2G,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
X74-STTA-TPA1A,CODE128,Composant Twist - X74-STTA-TPA1A,Twist,"Machine: T24, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T24,Pending
EK9-PDB/DGM-TAB1L,CODE128,Composant Twist - EK9-PDB/DGM-TAB1L,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
EK9-PDB/DGM-TAB1O,CODE128,Composant Twist - EK9-PDB/DGM-TAB1O,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
EK9-PDB/DPSA-TAB1W,CODE128,Composant Twist - EK9-PDB/DPSA-TAB1W,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
EK9-PDB/DPSA-TAB1Z,CODE128,Composant Twist - EK9-PDB/DPSA-TAB1Z,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
EK9-PDB/GGM-TAB1Z,CODE128,Composant Twist - EK9-PDB/GGM-TAB1Z,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
EK9-PDB/GPSA-TAB1O,CODE128,Composant Twist - EK9-PDB/GPSA-TAB1O,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
EK9-PDB/GPSA-TAB1Z,CODE128,Composant Twist - EK9-PDB/GPSA-TAB1Z,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
EK9-PPL-TPC3P,CODE128,Composant Twist - EK9-PPL-TPC3P,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-HAB-TPB2AC,CODE128,Composant Twist - K9-HAB-TPB2AC,Twist,"Machine: T25, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T25,Pending
K9-HAB-TPB2T,CODE128,Composant Twist - K9-HAB-TPB2T,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-HAB-TRB1N-MCA,CODE128,Composant Twist - K9-HAB-TRB1N-MCA,Twist,"Machine: T25, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T25,Pending
K9-HAB-TRB2H,CODE128,Composant Twist - K9-HAB-TRB2H,Twist,"Machine: T25, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T25,Pending
K9-PDB/DGM-TAB1P,CODE128,Composant Twist - K9-PDB/DGM-TAB1P,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-PDB/DGM-TPB1K,CODE128,Composant Twist - K9-PDB/DGM-TPB1K,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-PDB/DPSA-TAB1BB,CODE128,Composant Twist - K9-PDB/DPSA-TAB1BB,Twist,"Machine: T25, Ordre: 0, Reste: 0, Actions: Valider Reviser Annuler",,,,Aptiv Internal,T25,Pending
K9-PDB/DPSA-TAB1L,CODE128,Composant Twist - K9-PDB/DPSA-TAB1L,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-PDB/DPSA-TAB1P,CODE128,Composant Twist - K9-PDB/DPSA-TAB1P,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-PDB/DPSA-TAB2F,CODE128,Composant Twist - K9-PDB/DPSA-TAB2F,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-PDB/DPSA-TPB1P,CODE128,Composant Twist - K9-PDB/DPSA-TPB1P,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-PDB/DPSA-TPB1Q,CODE128,Composant Twist - K9-PDB/DPSA-TPB1Q,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-PDB/DPSA-TPB2H,CODE128,Composant Twist - K9-PDB/DPSA-TPB2H,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-PDB/D-TAB2B-MCA,CODE128,Composant Twist - K9-PDB/D-TAB2B-MCA,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-PDB/D-TAC1A-MCA,CODE128,Composant Twist - K9-PDB/D-TAC1A-MCA,Twist,"Machine: T25, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T25,Pending
K9-PDB/D-TPB1B-MCA,CODE128,Composant Twist - K9-PDB/D-TPB1B-MCA,Twist,"Machine: T25, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T25,Pending
K9-PDB/D-TRC1A-MCA,CODE128,Composant Twist - K9-PDB/D-TRC1A-MCA,Twist,"Machine: T25, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T25,Pending
K9-PDB/GGM-TAB1H,CODE128,Composant Twist - K9-PDB/GGM-TAB1H,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-PDB/GGM-TAB1P,CODE128,Composant Twist - K9-PDB/GGM-TAB1P,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-PDB/GGM-TPB1E,CODE128,Composant Twist - K9-PDB/GGM-TPB1E,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-PDB/GPSA-TAB1H,CODE128,Composant Twist - K9-PDB/GPSA-TAB1H,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-PDB/GPSA-TAB1P,CODE128,Composant Twist - K9-PDB/GPSA-TAB1P,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-PDB/GPSA-TPB1C,CODE128,Composant Twist - K9-PDB/GPSA-TPB1C,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-PDB/GPSA-TPB1K,CODE128,Composant Twist - K9-PDB/GPSA-TPB1K,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-PDB/GPSA-TPB1Q,CODE128,Composant Twist - K9-PDB/GPSA-TPB1Q,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-PDB/GPSA-TPB2H,CODE128,Composant Twist - K9-PDB/GPSA-TPB2H,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-PDB/G-TAB1I-MCA,CODE128,Composant Twist - K9-PDB/G-TAB1I-MCA,Twist,"Machine: T25, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T25,Pending
K9-PDB/G-TAB1MX-MCA,CODE128,Composant Twist - K9-PDB/G-TAB1MX-MCA,Twist,"Machine: T25, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T25,Pending
K9-PDB/G-TAB2B-MCA,CODE128,Composant Twist - K9-PDB/G-TAB2B-MCA,Twist,"Machine: T25, Ordre: 700.0, Reste: 700.0, Actions: Valider Reviser Annuler",,700,,Aptiv Internal,T25,Pending
K9-PDB/G-TPB2E-MCA,CODE128,Composant Twist - K9-PDB/G-TPB2E-MCA,Twist,"Machine: T25, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T25,Pending
K9-PDB/G-TRB1A-MCA,CODE128,Composant Twist - K9-PDB/G-TRB1A-MCA,Twist,"Machine: T25, Ordre: 500.0, Reste: 500.0, Actions: Valider Reviser Annuler",,500,,Aptiv Internal,T25,Pending
K9-PLC-TAC4A-B,CODE128,Composant Twist - K9-PLC-TAC4A-B,Twist,"Machine: T25, Ordre: 900.0, Reste: 900.0, Actions: Valider Reviser Annuler",,900,,Aptiv Internal,T25,Pending
K9-PPL-TRA1G,CODE128,Composant Twist - K9-PPL-TRA1G,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
K9-PPL-TRA1W,CODE128,Composant Twist - K9-PPL-TRA1W,Twist,"Machine: T25, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T25,Pending
K9-PRAVD-TPB2C,CODE128,Composant Twist - K9-PRAVD-TPB2C,Twist,"Machine: T25, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T25,Pending
K9-PRAVD-TPB2D,CODE128,Composant Twist - K9-PRAVD-TPB2D,Twist,"Machine: T25, Ordre: 700.0, Reste: 700.0, Actions: Valider Reviser Annuler",,700,,Aptiv Internal,T25,Pending
K9-PRAVD-TRB1A,CODE128,Composant Twist - K9-PRAVD-TRB1A,Twist,"Machine: T25, Ordre: 500.0, Reste: 500.0, Actions: Valider Reviser Annuler",,500,,Aptiv Internal,T25,Pending
K9-PRAVG-TPB2C,CODE128,Composant Twist - K9-PRAVG-TPB2C,Twist,"Machine: T25, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T25,Pending
R8-ARTIV-TRC1A,CODE128,Composant Twist - R8-ARTIV-TRC1A,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-ARTIV-TRC1B,CODE128,Composant Twist - R8-ARTIV-TRC1B,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-CSL BVA-TRB1A,CODE128,Composant Twist - R8-CSL BVA-TRB1A,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-CSLBVA/D-TRB1A,CODE128,Composant Twist - R8-CSLBVA/D-TRB1A,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-HAB-TAB1AC,CODE128,Composant Twist - R8-HAB-TAB1AC,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-HAB-TAB1Q,CODE128,Composant Twist - R8-HAB-TAB1Q,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-HAB-TPB2AN,CODE128,Composant Twist - R8-HAB-TPB2AN,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-HAB-TPB4D-MV,CODE128,Composant Twist - R8-HAB-TPB4D-MV,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-HAB-TPB4E-MV,CODE128,Composant Twist - R8-HAB-TPB4E-MV,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-HAB-TPB4F-MV,CODE128,Composant Twist - R8-HAB-TPB4F-MV,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-PAD-TAA1B,CODE128,Composant Twist - R8-PAD-TAA1B,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PAD-TPB2A,CODE128,Composant Twist - R8-PAD-TPB2A,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-PAG-TPB2B,CODE128,Composant Twist - R8-PAG-TPB2B,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-Pavillon-TPB3A,CODE128,Composant Twist - R8-Pavillon-TPB3A,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PDB/D-TAB1J,CODE128,Composant Twist - R8-PDB/D-TAB1J,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PDB/D-TAB1K,CODE128,Composant Twist - R8-PDB/D-TAB1K,Twist,"Machine: T25, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T25,Pending
R8-PDB/D-TAB1U,CODE128,Composant Twist - R8-PDB/D-TAB1U,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PDB/D-TPB1C,CODE128,Composant Twist - R8-PDB/D-TPB1C,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PDB/D-TPB1E,CODE128,Composant Twist - R8-PDB/D-TPB1E,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PDB/D-TPB1N,CODE128,Composant Twist - R8-PDB/D-TPB1N,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PDB/D-TPB2P,CODE128,Composant Twist - R8-PDB/D-TPB2P,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PDB/D-TPB3A,CODE128,Composant Twist - R8-PDB/D-TPB3A,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PDB/D-TPB3B,CODE128,Composant Twist - R8-PDB/D-TPB3B,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PDB/D-TPB3C,CODE128,Composant Twist - R8-PDB/D-TPB3C,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PDB/D-TPB3E,CODE128,Composant Twist - R8-PDB/D-TPB3E,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PDB/D-TPB3F,CODE128,Composant Twist - R8-PDB/D-TPB3F,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PDB/D-TRB1A,CODE128,Composant Twist - R8-PDB/D-TRB1A,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PDB/G-TAB1A,CODE128,Composant Twist - R8-PDB/G-TAB1A,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-PDB/G-TAB1J,CODE128,Composant Twist - R8-PDB/G-TAB1J,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-PDB/G-TAB1K,CODE128,Composant Twist - R8-PDB/G-TAB1K,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-PDB/G-TAB1M,CODE128,Composant Twist - R8-PDB/G-TAB1M,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PDB/G-TAB3B,CODE128,Composant Twist - R8-PDB/G-TAB3B,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PDB/G-TAB3C,CODE128,Composant Twist - R8-PDB/G-TAB3C,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PDB/G-TAB3E,CODE128,Composant Twist - R8-PDB/G-TAB3E,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PDB/G-TPB1F,CODE128,Composant Twist - R8-PDB/G-TPB1F,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-PDB/G-TPB1N,CODE128,Composant Twist - R8-PDB/G-TPB1N,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PDB/G-TPB3A-MV,CODE128,Composant Twist - R8-PDB/G-TPB3A-MV,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-PDB/G-TPB3F,CODE128,Composant Twist - R8-PDB/G-TPB3F,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PDB/G-TPB3I-MV,CODE128,Composant Twist - R8-PDB/G-TPB3I-MV,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-PDB/G-TRB1C,CODE128,Composant Twist - R8-PDB/G-TRB1C,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PDB/G-TRB2C-MV,CODE128,Composant Twist - R8-PDB/G-TRB2C-MV,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-PPLPHEV-TPB2A,CODE128,Composant Twist - R8-PPLPHEV-TPB2A,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-PPLPHEV-TPC1BD,CODE128,Composant Twist - R8-PPLPHEV-TPC1BD,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-PPLPHEV-TRA1O,CODE128,Composant Twist - R8-PPLPHEV-TRA1O,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-PPL-TRA1E,CODE128,Composant Twist - R8-PPL-TRA1E,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-PPL-TRA1Q,CODE128,Composant Twist - R8-PPL-TRA1Q,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-PPL-TRB1G,CODE128,Composant Twist - R8-PPL-TRB1G,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-PPL-TRB1X,CODE128,Composant Twist - R8-PPL-TRB1X,Twist,"Machine: T25, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T25,Pending
R8-PPL-TRC1J,CODE128,Composant Twist - R8-PPL-TRC1J,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
R8-SouCPHEV-TPC2N,CODE128,Composant Twist - R8-SouCPHEV-TPC2N,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-HAB-TAB2L,CODE128,Composant Twist - X74-HAB-TAB2L,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-HAB-TAB2M,CODE128,Composant Twist - X74-HAB-TAB2M,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-HAB-TAB3Y-MV,CODE128,Composant Twist - X74-HAB-TAB3Y-MV,Twist,"Machine: T25, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T25,Pending
X74-HAB-TPB2AQ,CODE128,Composant Twist - X74-HAB-TPB2AQ,Twist,"Machine: T25, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T25,Pending
X74-HAB-TPB3I-MV,CODE128,Composant Twist - X74-HAB-TPB3I-MV,Twist,"Machine: T25, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T25,Pending
X74-HAB-TPB3N,CODE128,Composant Twist - X74-HAB-TPB3N,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-PCAVG-TPB1A,CODE128,Composant Twist - X74-PCAVG-TPB1A,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-PDB-D-TAB1H,CODE128,Composant Twist - X74-PDB-D-TAB1H,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-PDB-D-TAB1N,CODE128,Composant Twist - X74-PDB-D-TAB1N,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-PDB-D-TAB1Z,CODE128,Composant Twist - X74-PDB-D-TAB1Z,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-PDB-D-TAB2R,CODE128,Composant Twist - X74-PDB-D-TAB2R,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-PDB-D-TPB1D,CODE128,Composant Twist - X74-PDB-D-TPB1D,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-PDB-D-TPB1H,CODE128,Composant Twist - X74-PDB-D-TPB1H,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-PDB-D-TPB2A,CODE128,Composant Twist - X74-PDB-D-TPB2A,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-PDB-D-TPB3B,CODE128,Composant Twist - X74-PDB-D-TPB3B,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-PDB-D-TPB3C,CODE128,Composant Twist - X74-PDB-D-TPB3C,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-PDB-D-TPB3D,CODE128,Composant Twist - X74-PDB-D-TPB3D,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-PDB-D-TPB3N,CODE128,Composant Twist - X74-PDB-D-TPB3N,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-PDB-G-TAB1H,CODE128,Composant Twist - X74-PDB-G-TAB1H,Twist,"Machine: T25, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T25,Pending
X74-PDB-G-TAB1N,CODE128,Composant Twist - X74-PDB-G-TAB1N,Twist,"Machine: T25, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T25,Pending
X74-PDB-G-TAB1Z,CODE128,Composant Twist - X74-PDB-G-TAB1Z,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-PDB-G-TPB1E,CODE128,Composant Twist - X74-PDB-G-TPB1E,Twist,"Machine: T25, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T25,Pending
X74-PDB-G-TPB2L,CODE128,Composant Twist - X74-PDB-G-TPB2L,Twist,"Machine: T25, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T25,Pending
X74-PDB-G-TPB2P,CODE128,Composant Twist - X74-PDB-G-TPB2P,Twist,"Machine: T25, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T25,Pending
X74-PDB-G-TRB1B,CODE128,Composant Twist - X74-PDB-G-TRB1B,Twist,"Machine: T25, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T25,Pending
X74-PDB-G-TRB1C,CODE128,Composant Twist - X74-PDB-G-TRB1C,Twist,"Machine: T25, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T25,Pending
X74-PDB-G-TRB1F,CODE128,Composant Twist - X74-PDB-G-TRB1F,Twist,"Machine: T25, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T25,Pending
X74-PHEVPPL-TPA1J,CODE128,Composant Twist - X74-PHEVPPL-TPA1J,Twist,"Machine: T25, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T25,Pending
X74-PHEVPPL-TPB2B,CODE128,Composant Twist - X74-PHEVPPL-TPB2B,Twist,"Machine: T25, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T25,Pending
X74-PHEVPPL-TPC2V,CODE128,Composant Twist - X74-PHEVPPL-TPC2V,Twist,"Machine: T25, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T25,Pending
X74-PHEVPPL-TPC3C,CODE128,Composant Twist - X74-PHEVPPL-TPC3C,Twist,"Machine: T25, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T25,Pending
X74-PPL-TPB3A-MV,CODE128,Composant Twist - X74-PPL-TPB3A-MV,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-PPL-TPC2A,CODE128,Composant Twist - X74-PPL-TPC2A,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-PPL-TPC2F,CODE128,Composant Twist - X74-PPL-TPC2F,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-PPL-TRA1J,CODE128,Composant Twist - X74-PPL-TRA1J,Twist,"Machine: T25, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T25,Pending
X74-PPL-TRA1K,CODE128,Composant Twist - X74-PPL-TRA1K,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-PPL-TRB2R,CODE128,Composant Twist - X74-PPL-TRB2R,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
X74-PRAR-TPB3A,CODE128,Composant Twist - X74-PRAR-TPB3A,Twist,"Machine: T25, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T25,Pending
X74-PRAR-TPB5A,CODE128,Composant Twist - X74-PRAR-TPB5A,Twist,"Machine: T25, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T25,Pending
X74-PRAVD-TPB1A,CODE128,Composant Twist - X74-PRAVD-TPB1A,Twist,"Machine: T25, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T25,Pending
X74-PRAVD-TPB3A,CODE128,Composant Twist - X74-PRAVD-TPB3A,Twist,"Machine: T25, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T25,Pending
X74-PRAVD-TPB5A,CODE128,Composant Twist - X74-PRAVD-TPB5A,Twist,"Machine: T25, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T25,Pending
X74-PRAVG-TPB5A,CODE128,Composant Twist - X74-PRAVG-TPB5A,Twist,"Machine: T25, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T25,Pending
X74-SSCPHEV-TPC2J,CODE128,Composant Twist - X74-SSCPHEV-TPC2J,Twist,"Machine: T25, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T25,Pending
EK9-PDB/DPSA-TAB1U,CODE128,Composant Twist - EK9-PDB/DPSA-TAB1U,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
EK9-PDB/GPSA-TAB1T,CODE128,Composant Twist - EK9-PDB/GPSA-TAB1T,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
EK9-PPL-TPC3Q,CODE128,Composant Twist - EK9-PPL-TPC3Q,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
EK9-PPL-TRA1AH-MCA,CODE128,Composant Twist - EK9-PPL-TRA1AH-MCA,Twist,"Machine: T26, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T26,Pending
EK9-PPL-TRA1FZ,CODE128,Composant Twist - EK9-PPL-TRA1FZ,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-HAB-TPB1EE-MCA,CODE128,Composant Twist - K9-HAB-TPB1EE-MCA,Twist,"Machine: T26, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T26,Pending
K9-HAB-TPB1R-MCA,CODE128,Composant Twist - K9-HAB-TPB1R-MCA,Twist,"Machine: T26, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T26,Pending
K9-HAB-TPB2TA-MCA,CODE128,Composant Twist - K9-HAB-TPB2TA-MCA,Twist,"Machine: T26, Ordre: 200.0, Reste: 200.0, Actions: Valider Reviser Annuler",,200,,Aptiv Internal,T26,Pending
K9-HAB-TPB2U,CODE128,Composant Twist - K9-HAB-TPB2U,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-HAB-TPB2UA-MCA,CODE128,Composant Twist - K9-HAB-TPB2UA-MCA,Twist,"Machine: T26, Ordre: 700.0, Reste: 700.0, Actions: Valider Reviser Annuler",,700,,Aptiv Internal,T26,Pending
K9-HAB-TPB2W,CODE128,Composant Twist - K9-HAB-TPB2W,Twist,"Machine: T26, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T26,Pending
K9-HAB-TRB1B-MCA,CODE128,Composant Twist - K9-HAB-TRB1B-MCA,Twist,"Machine: T26, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T26,Pending
K9-HAB-TRB1Z-MCA,CODE128,Composant Twist - K9-HAB-TRB1Z-MCA,Twist,"Machine: T26, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T26,Pending
K9-PDB/DGM-TAB1Q,CODE128,Composant Twist - K9-PDB/DGM-TAB1Q,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-PDB/DPSA-TAB1X,CODE128,Composant Twist - K9-PDB/DPSA-TAB1X,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-PDB/DPSA-TPB1A,CODE128,Composant Twist - K9-PDB/DPSA-TPB1A,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-PDB/GGM-TAB1BB,CODE128,Composant Twist - K9-PDB/GGM-TAB1BB,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-PDB/GGM-TAB1V,CODE128,Composant Twist - K9-PDB/GGM-TAB1V,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-PDB/GGM-TPB1A,CODE128,Composant Twist - K9-PDB/GGM-TPB1A,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-PDB/GPSA-TAB1BB,CODE128,Composant Twist - K9-PDB/GPSA-TAB1BB,Twist,"Machine: T26, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600,,Aptiv Internal,T26,Pending
K9-PDB/GPSA-TAB1G,CODE128,Composant Twist - K9-PDB/GPSA-TAB1G,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-PDB/GPSA-TAB1L,CODE128,Composant Twist - K9-PDB/GPSA-TAB1L,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-PDB/GPSA-TAB2M,CODE128,Composant Twist - K9-PDB/GPSA-TAB2M,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-PDB/GPSA-TAB3E,CODE128,Composant Twist - K9-PDB/GPSA-TAB3E,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-PDB/GPSA-TAB3F,CODE128,Composant Twist - K9-PDB/GPSA-TAB3F,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-PDB/GPSA-TAB3H,CODE128,Composant Twist - K9-PDB/GPSA-TAB3H,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-PDB/GPSA-TPB1A,CODE128,Composant Twist - K9-PDB/GPSA-TPB1A,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-PDB/GPSA-TPB1B,CODE128,Composant Twist - K9-PDB/GPSA-TPB1B,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-PDB/GPSA-TPB1P,CODE128,Composant Twist - K9-PDB/GPSA-TPB1P,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-PDB/GPSA-TPB1Z,CODE128,Composant Twist - K9-PDB/GPSA-TPB1Z,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-PDB/G-TAB1AA-MCA,CODE128,Composant Twist - K9-PDB/G-TAB1AA-MCA,Twist,"Machine: T26, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T26,Pending
K9-PDB/G-TAB3A-MCA,CODE128,Composant Twist - K9-PDB/G-TAB3A-MCA,Twist,"Machine: T26, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600,,Aptiv Internal,T26,Pending
K9-PDB/G-TAB3B-MCA,CODE128,Composant Twist - K9-PDB/G-TAB3B-MCA,Twist,"Machine: T26, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600,,Aptiv Internal,T26,Pending
K9-PDB/G-TAB3U-MCA,CODE128,Composant Twist - K9-PDB/G-TAB3U-MCA,Twist,"Machine: T26, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T26,Pending
K9-PDB/G-TPB1A-MCA,CODE128,Composant Twist - K9-PDB/G-TPB1A-MCA,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-PDB/G-TPB1B-MCA,CODE128,Composant Twist - K9-PDB/G-TPB1B-MCA,Twist,"Machine: T26, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T26,Pending
K9-PDB/G-TPC1A-MCA,CODE128,Composant Twist - K9-PDB/G-TPC1A-MCA,Twist,"Machine: T26, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T26,Pending
K9-PPL-TPA1A,CODE128,Composant Twist - K9-PPL-TPA1A,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-PPL-TPA1B,CODE128,Composant Twist - K9-PPL-TPA1B,Twist,"Machine: T26, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T26,Pending
K9-PPL-TRA1C,CODE128,Composant Twist - K9-PPL-TRA1C,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-PPL-TRA1F,CODE128,Composant Twist - K9-PPL-TRA1F,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
K9-PPL-TRC1Z-MCA,CODE128,Composant Twist - K9-PPL-TRC1Z-MCA,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-HAB-TPB6F,CODE128,Composant Twist - X74-HAB-TPB6F,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T01,Pending
X74-PHEVPPL-TPC2S,CODE128,Composant Twist - X74-PHEVPPL-TPC2S,Twist,"Machine: T01, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T01,Pending
R8-HAB-TAB1A,CODE128,Composant Twist - R8-HAB-TAB1A,Twist,"Machine: T26, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T26,Pending
R8-PAG-TAB1B,CODE128,Composant Twist - R8-PAG-TAB1B,Twist,"Machine: T26, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T26,Pending
R8-PDB/D-TPB3U-MV,CODE128,Composant Twist - R8-PDB/D-TPB3U-MV,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
R8-PDB/D-TPC1B-MV,CODE128,Composant Twist - R8-PDB/D-TPC1B-MV,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
R8-PDB/G-TAB1S,CODE128,Composant Twist - R8-PDB/G-TAB1S,Twist,"Machine: T26, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T26,Pending
R8-PDB/G-TPB3B,CODE128,Composant Twist - R8-PDB/G-TPB3B,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
R8-PDB/G-TPB3E,CODE128,Composant Twist - R8-PDB/G-TPB3E,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
R8-PDB/G-TPB3U-MV,CODE128,Composant Twist - R8-PDB/G-TPB3U-MV,Twist,"Machine: T26, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T26,Pending
R8-PDB/G-TRB2D,CODE128,Composant Twist - R8-PDB/G-TRB2D,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
R8-PPLPHEV-TPC1F,CODE128,Composant Twist - R8-PPLPHEV-TPC1F,Twist,"Machine: T26, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T26,Pending
R8-PPLPHEV-TPC2C,CODE128,Composant Twist - R8-PPLPHEV-TPC2C,Twist,"Machine: T26, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T26,Pending
R8-PPL-TPA1C,CODE128,Composant Twist - R8-PPL-TPA1C,Twist,"Machine: T26, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T26,Pending
R8-PPL-TRA1AF,CODE128,Composant Twist - R8-PPL-TRA1AF,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
R8-PPL-TRB1W-1,CODE128,Composant Twist - R8-PPL-TRB1W-1,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-HAB-TPB2AN,CODE128,Composant Twist - X74-HAB-TPB2AN,Twist,"Machine: T26, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T26,Pending
X74-HAB-TPB3O,CODE128,Composant Twist - X74-HAB-TPB3O,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-HAB-TPB3P,CODE128,Composant Twist - X74-HAB-TPB3P,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-HAB-TPB3Y-MV,CODE128,Composant Twist - X74-HAB-TPB3Y-MV,Twist,"Machine: T26, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T26,Pending
X74-HAB-TPB3Z,CODE128,Composant Twist - X74-HAB-TPB3Z,Twist,"Machine: T26, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T26,Pending
X74-PDB-D-m-TRB2C,CODE128,Composant Twist - X74-PDB-D-m-TRB2C,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-PDB-D-TAB2H,CODE128,Composant Twist - X74-PDB-D-TAB2H,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-PDB-D-TAB2Z,CODE128,Composant Twist - X74-PDB-D-TAB2Z,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-PDB-D-TAB3B,CODE128,Composant Twist - X74-PDB-D-TAB3B,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-PDB-D-TAB3C,CODE128,Composant Twist - X74-PDB-D-TAB3C,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-PDB-D-TPB1E,CODE128,Composant Twist - X74-PDB-D-TPB1E,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-PDB-D-TPB3J,CODE128,Composant Twist - X74-PDB-D-TPB3J,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-PDB-D-TPB3S,CODE128,Composant Twist - X74-PDB-D-TPB3S,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-PDB-D-TRB1B,CODE128,Composant Twist - X74-PDB-D-TRB1B,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-PDB-D-TRB1C,CODE128,Composant Twist - X74-PDB-D-TRB1C,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-PDB-G-m-TRB1A,CODE128,Composant Twist - X74-PDB-G-m-TRB1A,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-PDB-G-TAB2G,CODE128,Composant Twist - X74-PDB-G-TAB2G,Twist,"Machine: T26, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T26,Pending
X74-PDB-G-TPB3E,CODE128,Composant Twist - X74-PDB-G-TPB3E,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-PDB-G-TPB3N,CODE128,Composant Twist - X74-PDB-G-TPB3N,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-PDB-G-TPB3S,CODE128,Composant Twist - X74-PDB-G-TPB3S,Twist,"Machine: T26, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T26,Pending
X74-PPL-TPA1C,CODE128,Composant Twist - X74-PPL-TPA1C,Twist,"Machine: T26, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T26,Pending
X74-PPL-TPA1F,CODE128,Composant Twist - X74-PPL-TPA1F,Twist,"Machine: T26, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T26,Pending
X74-PPL-TRA1B,CODE128,Composant Twist - X74-PPL-TRA1B,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-PPL-TRC1A,CODE128,Composant Twist - X74-PPL-TRC1A,Twist,"Machine: T26, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T26,Pending
X74-PPL-TRC2D,CODE128,Composant Twist - X74-PPL-TRC2D,Twist,"Machine: T26, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T26,Pending
X74-PPL-TRC2G,CODE128,Composant Twist - X74-PPL-TRC2G,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-PRAR-TAB3A,CODE128,Composant Twist - X74-PRAR-TAB3A,Twist,"Machine: T26, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T26,Pending
X74-PRAVD-TAB3A,CODE128,Composant Twist - X74-PRAVD-TAB3A,Twist,"Machine: T26, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T26,Pending
X74-PRAVD-TPB1H,CODE128,Composant Twist - X74-PRAVD-TPB1H,Twist,"Machine: T26, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T26,Pending
X74-PRAVG-TAB3B,CODE128,Composant Twist - X74-PRAVG-TAB3B,Twist,"Machine: T26, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T26,Pending
X74-PRAVG-TAB3C,CODE128,Composant Twist - X74-PRAVG-TAB3C,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-PRAVG-TPB2A,CODE128,Composant Twist - X74-PRAVG-TPB2A,Twist,"Machine: T26, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T26,Pending
X74-PRAVG-TPB3C,CODE128,Composant Twist - X74-PRAVG-TPB3C,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-SouC-TRA1B,CODE128,Composant Twist - X74-SouC-TRA1B,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
X74-SSCPHEV-TPC2P,CODE128,Composant Twist - X74-SSCPHEV-TPC2P,Twist,"Machine: T26, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T26,Pending
EK9-PPL-TRA1WB-MCA,CODE128,Composant Twist - EK9-PPL-TRA1WB-MCA,Twist,"Machine: T27, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T27,Pending
K9-Pavillon-TRB1A,CODE128,Composant Twist - K9-Pavillon-TRB1A,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-Pavillon-TRB1B,CODE128,Composant Twist - K9-Pavillon-TRB1B,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
k9-pavillon-TRB1X-MCA,CODE128,Composant Twist - k9-pavillon-TRB1X-MCA,Twist,"Machine: T27, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T27,Pending
K9-PDB/DGM-TIPB1A,CODE128,Composant Twist - K9-PDB/DGM-TIPB1A,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/DGM-TIPB1C,CODE128,Composant Twist - K9-PDB/DGM-TIPB1C,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/DGM-TIPB1L,CODE128,Composant Twist - K9-PDB/DGM-TIPB1L,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/DGM-TIPB3J,CODE128,Composant Twist - K9-PDB/DGM-TIPB3J,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/DPSA-TIPB1A,CODE128,Composant Twist - K9-PDB/DPSA-TIPB1A,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/DPSA-TIPB1L,CODE128,Composant Twist - K9-PDB/DPSA-TIPB1L,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/DPSA-TIPB3J,CODE128,Composant Twist - K9-PDB/DPSA-TIPB3J,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/DPSA-TYB2M,CODE128,Composant Twist - K9-PDB/DPSA-TYB2M,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/D-TAB3X-MCA,CODE128,Composant Twist - K9-PDB/D-TAB3X-MCA,Twist,"Machine: T27, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T27,Pending
K9-PDB/D-TAB3Z-MCA,CODE128,Composant Twist - K9-PDB/D-TAB3Z-MCA,Twist,"Machine: T27, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T27,Pending
K9-PDB/D-TYB2N-MCA,CODE128,Composant Twist - K9-PDB/D-TYB2N-MCA,Twist,"Machine: T27, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T27,Pending
K9-PDB/GGM-TAB3BB,CODE128,Composant Twist - K9-PDB/GGM-TAB3BB,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/GGM-TAB3DD,CODE128,Composant Twist - K9-PDB/GGM-TAB3DD,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/GGM-TAB3F,CODE128,Composant Twist - K9-PDB/GGM-TAB3F,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/GGM-TAB3GG,CODE128,Composant Twist - K9-PDB/GGM-TAB3GG,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/GGM-TAB3II,CODE128,Composant Twist - K9-PDB/GGM-TAB3II,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/GGM-TIPB1C,CODE128,Composant Twist - K9-PDB/GGM-TIPB1C,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/GGM-TIPB1L,CODE128,Composant Twist - K9-PDB/GGM-TIPB1L,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/GGM-TIPB3J,CODE128,Composant Twist - K9-PDB/GGM-TIPB3J,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/GPSA-TAB3BB,CODE128,Composant Twist - K9-PDB/GPSA-TAB3BB,Twist,"Machine: T27, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600,,Aptiv Internal,T27,Pending
K9-PDB/GPSA-TAB3DD,CODE128,Composant Twist - K9-PDB/GPSA-TAB3DD,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/GPSA-TAB3GG,CODE128,Composant Twist - K9-PDB/GPSA-TAB3GG,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/GPSA-TAB3II,CODE128,Composant Twist - K9-PDB/GPSA-TAB3II,Twist,"Machine: T27, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600,,Aptiv Internal,T27,Pending
K9-PDB/GPSA-TIPB1C,CODE128,Composant Twist - K9-PDB/GPSA-TIPB1C,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/GPSA-TIPB1K,CODE128,Composant Twist - K9-PDB/GPSA-TIPB1K,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/GPSA-TIPB1L,CODE128,Composant Twist - K9-PDB/GPSA-TIPB1L,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/GPSA-TIPB3J,CODE128,Composant Twist - K9-PDB/GPSA-TIPB3J,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/G-TPB3D-MCA,CODE128,Composant Twist - K9-PDB/G-TPB3D-MCA,Twist,"Machine: T27, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T27,Pending
K9-PDB/G-TYB2O-MCA,CODE128,Composant Twist - K9-PDB/G-TYB2O-MCA,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
K9-PDB/G-TYB2P-MCA,CODE128,Composant Twist - K9-PDB/G-TYB2P-MCA,Twist,"Machine: T27, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T27,Pending
K9-PPL-TRC1P-MCA,CODE128,Composant Twist - K9-PPL-TRC1P-MCA,Twist,"Machine: T27, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T27,Pending
R8-CSL BVA-TIPB3,CODE128,Composant Twist - R8-CSL BVA-TIPB3,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
R8-CSLBVA/D-TIPB3,CODE128,Composant Twist - R8-CSLBVA/D-TIPB3,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
R8-PAD-TIPB3,CODE128,Composant Twist - R8-PAD-TIPB3,Twist,"Machine: T27, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T27,Pending
R8-PAG-TIPB3A,CODE128,Composant Twist - R8-PAG-TIPB3A,Twist,"Machine: T27, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T27,Pending
R8-PDB/D-TIPB1B,CODE128,Composant Twist - R8-PDB/D-TIPB1B,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
R8-PDB/D-TIPB1B1,CODE128,Composant Twist - R8-PDB/D-TIPB1B1,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
R8-PDB/G-TIPB1B,CODE128,Composant Twist - R8-PDB/G-TIPB1B,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
R8-PDB/G-TIPB1B1,CODE128,Composant Twist - R8-PDB/G-TIPB1B1,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
R8-PPLPHEV-T4PC3A,CODE128,Composant Twist - R8-PPLPHEV-T4PC3A,Twist,"Machine: T27, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T27,Pending
R8-PR AR-TIPB3,CODE128,Composant Twist - R8-PR AR-TIPB3,Twist,"Machine: T27, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T27,Pending
X74-HAB-TAB3D,CODE128,Composant Twist - X74-HAB-TAB3D,Twist,"Machine: T27, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T27,Pending
X74-HAB-TAB3G,CODE128,Composant Twist - X74-HAB-TAB3G,Twist,"Machine: T27, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T27,Pending
X74-PDB-D-TIPB3,CODE128,Composant Twist - X74-PDB-D-TIPB3,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
X74-PDB-G-TIPB3,CODE128,Composant Twist - X74-PDB-G-TIPB3,Twist,"Machine: T27, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T27,Pending
X74-PHEVPPL-T4PC3,CODE128,Composant Twist - X74-PHEVPPL-T4PC3,Twist,"Machine: T27, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T27,Pending
X74-PRAVD-TAB5A,CODE128,Composant Twist - X74-PRAVD-TAB5A,Twist,"Machine: T27, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T27,Pending
EK9-PPL-TRA1AG,CODE128,Composant Twist - EK9-PPL-TRA1AG,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
K9-HAB-TPB2BA-MCA,CODE128,Composant Twist - K9-HAB-TPB2BA-MCA,Twist,"Machine: T28, Ordre: 900.0, Reste: 900.0, Actions: Valider Reviser Annuler",,900,,Aptiv Internal,T28,Pending
K9-HAB-TPB2BO,CODE128,Composant Twist - K9-HAB-TPB2BO,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
K9-JaugeCrb-TRC2B,CODE128,Composant Twist - K9-JaugeCrb-TRC2B,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
K9-Pavillon-TPB1K,CODE128,Composant Twist - K9-Pavillon-TPB1K,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
k9-pavillon-TPB1T-MCA,CODE128,Composant Twist - k9-pavillon-TPB1T-MCA,Twist,"Machine: T28, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T28,Pending
K9-PDB/DGM-TAB2A,CODE128,Composant Twist - K9-PDB/DGM-TAB2A,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
K9-PDB/DGM-TAB3I,CODE128,Composant Twist - K9-PDB/DGM-TAB3I,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
K9-PDB/DPSA-TAB2A,CODE128,Composant Twist - K9-PDB/DPSA-TAB2A,Twist,"Machine: T28, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600,,Aptiv Internal,T28,Pending
K9-PDB/DPSA-TPB1B,CODE128,Composant Twist - K9-PDB/DPSA-TPB1B,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
K9-PDB/DPSA-TPB1F,CODE128,Composant Twist - K9-PDB/DPSA-TPB1F,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
K9-PDB/DPSA-TPB1J,CODE128,Composant Twist - K9-PDB/DPSA-TPB1J,Twist,"Machine: T28, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T28,Pending
K9-PDB/DPSA-TPB1L,CODE128,Composant Twist - K9-PDB/DPSA-TPB1L,Twist,"Machine: T28, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T28,Pending
K9-PDB/DPSA-TPB1S,CODE128,Composant Twist - K9-PDB/DPSA-TPB1S,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
K9-PDB/GGM-TAB3G,CODE128,Composant Twist - K9-PDB/GGM-TAB3G,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
K9-PDB/GGM-TRB2X,CODE128,Composant Twist - K9-PDB/GGM-TRB2X,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
K9-PDB/GGM-TYB2A,CODE128,Composant Twist - K9-PDB/GGM-TYB2A,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
K9-PDB/GPSA-TAB1N,CODE128,Composant Twist - K9-PDB/GPSA-TAB1N,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
K9-PDB/GPSA-TPA1Q,CODE128,Composant Twist - K9-PDB/GPSA-TPA1Q,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
X74-PPL-TPC2L,CODE128,Composant Twist - X74-PPL-TPC2L,Twist,"Machine: T01, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T01,Pending
K9-PDB/G-TPA1M-MCA,CODE128,Composant Twist - K9-PDB/G-TPA1M-MCA,Twist,"Machine: T28, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T28,Pending
K9-PLC-TPC2A-B,CODE128,Composant Twist - K9-PLC-TPC2A-B,Twist,"Machine: T28, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T28,Pending
K9-PPL-TPA1C,CODE128,Composant Twist - K9-PPL-TPA1C,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
K9-PPL-TRA1I,CODE128,Composant Twist - K9-PPL-TRA1I,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
K9-PPL-TRA1K,CODE128,Composant Twist - K9-PPL-TRA1K,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
K9-PPL-TRA1M,CODE128,Composant Twist - K9-PPL-TRA1M,Twist,"Machine: T28, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T28,Pending
K9-SSCaisse-TPH3A,CODE128,Composant Twist - K9-SSCaisse-TPH3A,Twist,"Machine: T28, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T28,Pending
R8-HAB-TPB1AB,CODE128,Composant Twist - R8-HAB-TPB1AB,Twist,"Machine: T28, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T28,Pending
R8-HAB-TPB2A3C-MV,CODE128,Composant Twist - R8-HAB-TPB2A3C-MV,Twist,"Machine: T28, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T28,Pending
R8-HAB-TPB3J,CODE128,Composant Twist - R8-HAB-TPB3J,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
R8-HAB-TPB3N,CODE128,Composant Twist - R8-HAB-TPB3N,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
R8-PAD-TPA1A,CODE128,Composant Twist - R8-PAD-TPA1A,Twist,"Machine: T28, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T28,Pending
R8-Pavillon-TRB1C,CODE128,Composant Twist - R8-Pavillon-TRB1C,Twist,"Machine: T28, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T28,Pending
R8-Pavillon-TRB1D,CODE128,Composant Twist - R8-Pavillon-TRB1D,Twist,"Machine: T28, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T28,Pending
R8-PC AV-TPB1A,CODE128,Composant Twist - R8-PC AV-TPB1A,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
R8-PDB/D-TAC1A-MV,CODE128,Composant Twist - R8-PDB/D-TAC1A-MV,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
R8-PDB/D-TPB1J,CODE128,Composant Twist - R8-PDB/D-TPB1J,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
R8-PDB/D-TPB3D,CODE128,Composant Twist - R8-PDB/D-TPB3D,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
R8-PDB/D-TRB1BC,CODE128,Composant Twist - R8-PDB/D-TRB1BC,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
R8-PDB/D-TRB2C-MV,CODE128,Composant Twist - R8-PDB/D-TRB2C-MV,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
R8-PDB/G-TAB1N,CODE128,Composant Twist - R8-PDB/G-TAB1N,Twist,"Machine: T28, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T28,Pending
R8-PDB/G-TAB1T,CODE128,Composant Twist - R8-PDB/G-TAB1T,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
R8-PDB/G-TPB2C,CODE128,Composant Twist - R8-PDB/G-TPB2C,Twist,"Machine: T28, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T28,Pending
R8-PDB/G-TPB3D,CODE128,Composant Twist - R8-PDB/G-TPB3D,Twist,"Machine: T28, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T28,Pending
R8-PPLPHEV-TPA1B,CODE128,Composant Twist - R8-PPLPHEV-TPA1B,Twist,"Machine: T28, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T28,Pending
R8-PPL-TPC2Q-MV,CODE128,Composant Twist - R8-PPL-TPC2Q-MV,Twist,"Machine: T28, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T28,Pending
R8-Volet-TAB1A,CODE128,Composant Twist - R8-Volet-TAB1A,Twist,"Machine: T28, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T28,Pending
X74-HAB-TAB2F,CODE128,Composant Twist - X74-HAB-TAB2F,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
X74-HAB-TPB2D,CODE128,Composant Twist - X74-HAB-TPB2D,Twist,"Machine: T28, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T28,Pending
X74-HAB-TPB3S,CODE128,Composant Twist - X74-HAB-TPB3S,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
X74-HAB-TPB3V,CODE128,Composant Twist - X74-HAB-TPB3V,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
X74-PDB-D-TAB2G,CODE128,Composant Twist - X74-PDB-D-TAB2G,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
X74-PDB-D-TPB1Q,CODE128,Composant Twist - X74-PDB-D-TPB1Q,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
X74-PDB-D-TPB2P,CODE128,Composant Twist - X74-PDB-D-TPB2P,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
X74-PDB-D-TRB1A2F,CODE128,Composant Twist - X74-PDB-D-TRB1A2F,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
X74-PDB-G-TPB1A-MV,CODE128,Composant Twist - X74-PDB-G-TPB1A-MV,Twist,"Machine: T28, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T28,Pending
X74-PDB-G-TPB1C,CODE128,Composant Twist - X74-PDB-G-TPB1C,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
X74-PDB-G-TPB1Q,CODE128,Composant Twist - X74-PDB-G-TPB1Q,Twist,"Machine: T28, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T28,Pending
X74-PDB-G-TPB2B,CODE128,Composant Twist - X74-PDB-G-TPB2B,Twist,"Machine: T28, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T28,Pending
X74-PDB-G-TPB2D,CODE128,Composant Twist - X74-PDB-G-TPB2D,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
X74-PDB-G-TPB2F,CODE128,Composant Twist - X74-PDB-G-TPB2F,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
X74-PDB-G-TPB3L,CODE128,Composant Twist - X74-PDB-G-TPB3L,Twist,"Machine: T28, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T28,Pending
X74-PPL-TPA1G,CODE128,Composant Twist - X74-PPL-TPA1G,Twist,"Machine: T28, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T28,Pending
EK9-HAB-TPB2LZ,CODE128,Composant Twist - EK9-HAB-TPB2LZ,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
EK9-HAB-TPB2MZ,CODE128,Composant Twist - EK9-HAB-TPB2MZ,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
EK9-PPL-TPC2A,CODE128,Composant Twist - EK9-PPL-TPC2A,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
EK9-PPL-TPC2C,CODE128,Composant Twist - EK9-PPL-TPC2C,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
K9-HAB-TPB2L,CODE128,Composant Twist - K9-HAB-TPB2L,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
K9-HAB-TPB2LB-MCA,CODE128,Composant Twist - K9-HAB-TPB2LB-MCA,Twist,"Machine: T29, Ordre: 900.0, Reste: 900.0, Actions: Valider Reviser Annuler",,900,,Aptiv Internal,T29,Pending
K9-HAB-TPB2LD-MCA,CODE128,Composant Twist - K9-HAB-TPB2LD-MCA,Twist,"Machine: T29, Ordre: 200.0, Reste: 200.0, Actions: Valider Reviser Annuler",,200,,Aptiv Internal,T29,Pending
K9-HAB-TPB2MC-MCA,CODE128,Composant Twist - K9-HAB-TPB2MC-MCA,Twist,"Machine: T29, Ordre: 200.0, Reste: 200.0, Actions: Valider Reviser Annuler",,200,,Aptiv Internal,T29,Pending
K9-Pavillon-TPB1Z,CODE128,Composant Twist - K9-Pavillon-TPB1Z,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
K9-Pavillon-TPB2A,CODE128,Composant Twist - K9-Pavillon-TPB2A,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
K9-Pavillon-TPB2B,CODE128,Composant Twist - K9-Pavillon-TPB2B,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
k9-pavillon-TRB1Z-MCA,CODE128,Composant Twist - k9-pavillon-TRB1Z-MCA,Twist,"Machine: T29, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T29,Pending
K9-PDB/DPSA-TPA1R,CODE128,Composant Twist - K9-PDB/DPSA-TPA1R,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
K9-PDB/DPSA-TPB1G,CODE128,Composant Twist - K9-PDB/DPSA-TPB1G,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
K9-PDB/GGM-TAB1C,CODE128,Composant Twist - K9-PDB/GGM-TAB1C,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
K9-PDB/GGM-TPB1I,CODE128,Composant Twist - K9-PDB/GGM-TPB1I,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
K9-PDB/GGM-TPB1K,CODE128,Composant Twist - K9-PDB/GGM-TPB1K,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
K9-PDB/GGM-TPB1Q,CODE128,Composant Twist - K9-PDB/GGM-TPB1Q,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
K9-PDB/GGM-TPB1Z,CODE128,Composant Twist - K9-PDB/GGM-TPB1Z,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
K9-PDB/GPSA-TAB3C,CODE128,Composant Twist - K9-PDB/GPSA-TAB3C,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
K9-PDB/GPSA-TPB1I,CODE128,Composant Twist - K9-PDB/GPSA-TPB1I,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
K9-PDB/G-TPC1C-MCA,CODE128,Composant Twist - K9-PDB/G-TPC1C-MCA,Twist,"Machine: T29, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T29,Pending
K9-PPL-TPA1E,CODE128,Composant Twist - K9-PPL-TPA1E,Twist,"Machine: T29, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T29,Pending
K9-PPL-TPA1F,CODE128,Composant Twist - K9-PPL-TPA1F,Twist,"Machine: T29, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T29,Pending
K9-PPL-TPA1H,CODE128,Composant Twist - K9-PPL-TPA1H,Twist,"Machine: T29, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600,,Aptiv Internal,T29,Pending
K9-PPL-TPC2X-MCA,CODE128,Composant Twist - K9-PPL-TPC2X-MCA,Twist,"Machine: T29, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T29,Pending
K9-PPL-TPC2Y-MCA,CODE128,Composant Twist - K9-PPL-TPC2Y-MCA,Twist,"Machine: T29, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T29,Pending
K9-PRAVG-TPB2D,CODE128,Composant Twist - K9-PRAVG-TPB2D,Twist,"Machine: T29, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T29,Pending
K9-PRBAT-TPB1A,CODE128,Composant Twist - K9-PRBAT-TPB1A,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
R8-PPL-TRC1A,CODE128,Composant Twist - R8-PPL-TRC1A,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
R8-PR AR-TPB2,CODE128,Composant Twist - R8-PR AR-TPB2,Twist,"Machine: T29, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T29,Pending
R8-SouCPHEV-TPC2A,CODE128,Composant Twist - R8-SouCPHEV-TPC2A,Twist,"Machine: T29, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T29,Pending
R8-SouCPHEV-TPC2D,CODE128,Composant Twist - R8-SouCPHEV-TPC2D,Twist,"Machine: T29, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T29,Pending
R8-SS Caisse-TRA1B,CODE128,Composant Twist - R8-SS Caisse-TRA1B,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
X74-PDB-G-TPB2C-MV,CODE128,Composant Twist - X74-PDB-G-TPB2C-MV,Twist,"Machine: T29, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T29,Pending
X74-PDB-G-TPB2G,CODE128,Composant Twist - X74-PDB-G-TPB2G,Twist,"Machine: T29, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T29,Pending
X74-PDB-G-TRB2A1F,CODE128,Composant Twist - X74-PDB-G-TRB2A1F,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
X74-PPL-TPA1M,CODE128,Composant Twist - X74-PPL-TPA1M,Twist,"Machine: T29, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T29,Pending
X74-PPL-TPA1P,CODE128,Composant Twist - X74-PPL-TPA1P,Twist,"Machine: T29, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T29,Pending
X74-PRAR-TPB2A,CODE128,Composant Twist - X74-PRAR-TPB2A,Twist,"Machine: T29, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T29,Pending
X74-SouC-TPC1A,CODE128,Composant Twist - X74-SouC-TPC1A,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
X74-SSCPHEV-TPC2F,CODE128,Composant Twist - X74-SSCPHEV-TPC2F,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
X74-SSCPHEV-TPC2L,CODE128,Composant Twist - X74-SSCPHEV-TPC2L,Twist,"Machine: T29, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T29,Pending
EK9-HAB-TPB2MY,CODE128,Composant Twist - EK9-HAB-TPB2MY,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
EK9-HAB-TPB2TY,CODE128,Composant Twist - EK9-HAB-TPB2TY,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
EK9-HAB-TPB2XH,CODE128,Composant Twist - EK9-HAB-TPB2XH,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
EK9-HAB-TPB2XO,CODE128,Composant Twist - EK9-HAB-TPB2XO,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
EK9-PDB/DGM-TRB2W,CODE128,Composant Twist - EK9-PDB/DGM-TRB2W,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
EK9-PPL-TPC3S-MCA,CODE128,Composant Twist - EK9-PPL-TPC3S-MCA,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
EK9-PPL-TRA1DZ,CODE128,Composant Twist - EK9-PPL-TRA1DZ,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
EK9-PPL-TRA1EA,CODE128,Composant Twist - EK9-PPL-TRA1EA,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
EK9-PPL-TRA1RA,CODE128,Composant Twist - EK9-PPL-TRA1RA,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-HAB-TPB1H,CODE128,Composant Twist - K9-HAB-TPB1H,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-HAB-TPB2MB-MCA,CODE128,Composant Twist - K9-HAB-TPB2MB-MCA,Twist,"Machine: T31, Ordre: 200.0, Reste: 200.0, Actions: Valider Reviser Annuler",,200,,Aptiv Internal,T31,Pending
K9-HAB-TPB2TB-MCA,CODE128,Composant Twist - K9-HAB-TPB2TB-MCA,Twist,"Machine: T31, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T31,Pending
K9-HAB-TPB2XB-MCA,CODE128,Composant Twist - K9-HAB-TPB2XB-MCA,Twist,"Machine: T31, Ordre: 200.0, Reste: 200.0, Actions: Valider Reviser Annuler",,200,,Aptiv Internal,T31,Pending
K9-HAB-TPB2XC-MCA,CODE128,Composant Twist - K9-HAB-TPB2XC-MCA,Twist,"Machine: T31, Ordre: 200.0, Reste: 200.0, Actions: Valider Reviser Annuler",,200,,Aptiv Internal,T31,Pending
K9-JaugeCrb-TRC2A,CODE128,Composant Twist - K9-JaugeCrb-TRC2A,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
k9-pavillon-TPB1S-MCA,CODE128,Composant Twist - k9-pavillon-TPB1S-MCA,Twist,"Machine: T31, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T31,Pending
K9-PDB/DGM-TPB1A,CODE128,Composant Twist - K9-PDB/DGM-TPB1A,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PDB/DGM-TPB1Z,CODE128,Composant Twist - K9-PDB/DGM-TPB1Z,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PDB/DGM-TYB2M,CODE128,Composant Twist - K9-PDB/DGM-TYB2M,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PDB/DPSA-TPB1Z,CODE128,Composant Twist - K9-PDB/DPSA-TPB1Z,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PDB/DPSA-TPB2B,CODE128,Composant Twist - K9-PDB/DPSA-TPB2B,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PDB/DPSA-TRB2X,CODE128,Composant Twist - K9-PDB/DPSA-TRB2X,Twist,"Machine: T31, Ordre: 900.0, Reste: 900.0, Actions: Valider Reviser Annuler",,900,,Aptiv Internal,T31,Pending
K9-PDB/D-TRB1A-MCA,CODE128,Composant Twist - K9-PDB/D-TRB1A-MCA,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PDB/D-TRB1Q-MCA,CODE128,Composant Twist - K9-PDB/D-TRB1Q-MCA,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PDB/GGM-TAB3C,CODE128,Composant Twist - K9-PDB/GGM-TAB3C,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PDB/GGM-TAB3D,CODE128,Composant Twist - K9-PDB/GGM-TAB3D,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PDB/GGM-TPB1B,CODE128,Composant Twist - K9-PDB/GGM-TPB1B,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PDB/GGM-TPB1CC,CODE128,Composant Twist - K9-PDB/GGM-TPB1CC,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PDB/GGM-TPB1D,CODE128,Composant Twist - K9-PDB/GGM-TPB1D,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PDB/GPSA-TPB1CC,CODE128,Composant Twist - K9-PDB/GPSA-TPB1CC,Twist,"Machine: T31, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600,,Aptiv Internal,T31,Pending
K9-PDB/GPSA-TPB1G,CODE128,Composant Twist - K9-PDB/GPSA-TPB1G,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PDB/GPSA-TPB1H,CODE128,Composant Twist - K9-PDB/GPSA-TPB1H,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PDB/GPSA-TPB1HH,CODE128,Composant Twist - K9-PDB/GPSA-TPB1HH,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PDB/GPSA-TPB1L,CODE128,Composant Twist - K9-PDB/GPSA-TPB1L,Twist,"Machine: T31, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T31,Pending
K9-PDB/GPSA-TPB1S,CODE128,Composant Twist - K9-PDB/GPSA-TPB1S,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PDB/GPSA-TPB1W,CODE128,Composant Twist - K9-PDB/GPSA-TPB1W,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PDB/GPSA-TYB2A,CODE128,Composant Twist - K9-PDB/GPSA-TYB2A,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PDB/GPSA-TYB2C,CODE128,Composant Twist - K9-PDB/GPSA-TYB2C,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PDB/G-TAB3Z-MCA,CODE128,Composant Twist - K9-PDB/G-TAB3Z-MCA,Twist,"Machine: T31, Ordre: 800.0, Reste: 800.0, Actions: Valider Reviser Annuler",,800,,Aptiv Internal,T31,Pending
K9-PDB/G-TPB3A-MCA,CODE128,Composant Twist - K9-PDB/G-TPB3A-MCA,Twist,"Machine: T31, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T31,Pending
K9-PDB/G-TPB3B-MCA,CODE128,Composant Twist - K9-PDB/G-TPB3B-MCA,Twist,"Machine: T31, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T31,Pending
K9-PDB/G-TPB3C-MCA,CODE128,Composant Twist - K9-PDB/G-TPB3C-MCA,Twist,"Machine: T31, Ordre: 1100.0, Reste: 1100.0, Actions: Valider Reviser Annuler",,1100,,Aptiv Internal,T31,Pending
K9-PDB/G-TYB2R-MCA,CODE128,Composant Twist - K9-PDB/G-TYB2R-MCA,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PPL-TPC2B,CODE128,Composant Twist - K9-PPL-TPC2B,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-PPL-TRA1R,CODE128,Composant Twist - K9-PPL-TRA1R,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
K9-SSCaissePHEV-TPC3B,CODE128,Composant Twist - K9-SSCaissePHEV-TPC3B,Twist,"Machine: T31, Ordre: 200.0, Reste: 200.0, Actions: Valider Reviser Annuler",,200,,Aptiv Internal,T31,Pending
K9-SSCaisse-TPC3A,CODE128,Composant Twist - K9-SSCaisse-TPC3A,Twist,"Machine: T31, Ordre: 1000.0, Reste: 1000.0, Actions: Valider Reviser Annuler",,1000,,Aptiv Internal,T31,Pending
R8-HAB-TPB1H,CODE128,Composant Twist - R8-HAB-TPB1H,Twist,"Machine: T31, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T31,Pending
R8-HAB-TPB3K,CODE128,Composant Twist - R8-HAB-TPB3K,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
R8-PAD-TPA1B,CODE128,Composant Twist - R8-PAD-TPA1B,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
R8-PAD-TPB2C,CODE128,Composant Twist - R8-PAD-TPB2C,Twist,"Machine: T31, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T31,Pending
R8-PAD-TPB2D,CODE128,Composant Twist - R8-PAD-TPB2D,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
R8-PAG-TPB2A,CODE128,Composant Twist - R8-PAG-TPB2A,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
R8-PAG-TPB2C,CODE128,Composant Twist - R8-PAG-TPB2C,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
R8-PDB/D-TAB1S,CODE128,Composant Twist - R8-PDB/D-TAB1S,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
R8-PDB/D-TPC1C-MV,CODE128,Composant Twist - R8-PDB/D-TPC1C-MV,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
R8-PDB/D-TRB1B,CODE128,Composant Twist - R8-PDB/D-TRB1B,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
R8-PDB/G-TAC1A-MV,CODE128,Composant Twist - R8-PDB/G-TAC1A-MV,Twist,"Machine: T31, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T31,Pending
R8-PDB/G-TPB1A,CODE128,Composant Twist - R8-PDB/G-TPB1A,Twist,"Machine: T31, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T31,Pending
R8-PDB/G-TPB1J,CODE128,Composant Twist - R8-PDB/G-TPB1J,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
R8-PDB/G-TPB2Q,CODE128,Composant Twist - R8-PDB/G-TPB2Q,Twist,"Machine: T31, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T31,Pending
R8-PDB/G-TPC1B-MV,CODE128,Composant Twist - R8-PDB/G-TPC1B-MV,Twist,"Machine: T31, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T31,Pending
R8-PDB/G-TPC1C-MV,CODE128,Composant Twist - R8-PDB/G-TPC1C-MV,Twist,"Machine: T31, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T31,Pending
R8-PPLPHEV-TPB1N,CODE128,Composant Twist - R8-PPLPHEV-TPB1N,Twist,"Machine: T31, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T31,Pending
R8-PPLPHEV-TPC1C,CODE128,Composant Twist - R8-PPLPHEV-TPC1C,Twist,"Machine: T31, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T31,Pending
R8-PPLPHEV-TPC1I,CODE128,Composant Twist - R8-PPLPHEV-TPC1I,Twist,"Machine: T31, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T31,Pending
R8-PPLPHEV-TPC2I,CODE128,Composant Twist - R8-PPLPHEV-TPC2I,Twist,"Machine: T31, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T31,Pending
R8-PPLPHEV-TPC2N,CODE128,Composant Twist - R8-PPLPHEV-TPC2N,Twist,"Machine: T31, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T31,Pending
R8-PPLPHEV-TPC3D,CODE128,Composant Twist - R8-PPLPHEV-TPC3D,Twist,"Machine: T31, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T31,Pending
R8-PPL-TPA1E,CODE128,Composant Twist - R8-PPL-TPA1E,Twist,"Machine: T31, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T31,Pending
R8-PPL-TPA1X,CODE128,Composant Twist - R8-PPL-TPA1X,Twist,"Machine: T31, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T31,Pending
R8-PPL-TRC1E,CODE128,Composant Twist - R8-PPL-TRC1E,Twist,"Machine: T31, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T31,Pending
R8-PPL-TRC1T-X,CODE128,Composant Twist - R8-PPL-TRC1T-X,Twist,"Machine: T31, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T31,Pending
R8-SouCPHEV-TPC2G,CODE128,Composant Twist - R8-SouCPHEV-TPC2G,Twist,"Machine: T31, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T31,Pending
R8-SouCPHEV-TPC2H,CODE128,Composant Twist - R8-SouCPHEV-TPC2H,Twist,"Machine: T31, Ordre: 25.0, Reste: 25.0, Actions: Valider Reviser Annuler",,25,,Aptiv Internal,T31,Pending
R8-SS Caisse-TRA1E,CODE128,Composant Twist - R8-SS Caisse-TRA1E,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
X74-ADML-TPB2A,CODE128,Composant Twist - X74-ADML-TPB2A,Twist,"Machine: T31, Ordre: 600.0, Reste: 600.0, Actions: Valider Reviser Annuler",,600,,Aptiv Internal,T31,Pending
X74-HAB-TPB1E,CODE128,Composant Twist - X74-HAB-TPB1E,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
X74-HAB-TPB1K,CODE128,Composant Twist - X74-HAB-TPB1K,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
X74-HAB-TPB3K,CODE128,Composant Twist - X74-HAB-TPB3K,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
X74-HAB-TPB4I,CODE128,Composant Twist - X74-HAB-TPB4I,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
X74-Pavillon-TPB3B,CODE128,Composant Twist - X74-Pavillon-TPB3B,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
X74-PDB-D-TPB1C,CODE128,Composant Twist - X74-PDB-D-TPB1C,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
X74-PDB-G-TAB2C-MV,CODE128,Composant Twist - X74-PDB-G-TAB2C-MV,Twist,"Machine: T31, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T31,Pending
X74-PDB-G-TAB2K-MV,CODE128,Composant Twist - X74-PDB-G-TAB2K-MV,Twist,"Machine: T31, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T31,Pending
X74-PHEVPPL-TPB1D,CODE128,Composant Twist - X74-PHEVPPL-TPB1D,Twist,"Machine: T31, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T31,Pending
X74-PHEVPPL-TPC2D,CODE128,Composant Twist - X74-PHEVPPL-TPC2D,Twist,"Machine: T31, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T31,Pending
X74-PHEVPPL-TPC2N,CODE128,Composant Twist - X74-PHEVPPL-TPC2N,Twist,"Machine: T31, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T31,Pending
X74-PHEVPPL-TPC2P,CODE128,Composant Twist - X74-PHEVPPL-TPC2P,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
X74-PHEVPPL-TPC2Q,CODE128,Composant Twist - X74-PHEVPPL-TPC2Q,Twist,"Machine: T31, Ordre: 100.0, Reste: 100.0, Actions: Valider Reviser Annuler",,100,,Aptiv Internal,T31,Pending
X74-PHEVPPL-TPC2W,CODE128,Composant Twist - X74-PHEVPPL-TPC2W,Twist,"Machine: T31, Ordre: 50.0, Reste: 50.0, Actions: Valider Reviser Annuler",,50,,Aptiv Internal,T31,Pending
X74-PRAVD-TPB2K,CODE128,Composant Twist - X74-PRAVD-TPB2K,Twist,"Machine: T31, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T31,Pending
X74-PRAVG-TPB1G,CODE128,Composant Twist - X74-PRAVG-TPB1G,Twist,"Machine: T31, Ordre: 400.0, Reste: 400.0, Actions: Valider Reviser Annuler",,400,,Aptiv Internal,T31,Pending
X74-PRAVG-TPB3B,CODE128,Composant Twist - X74-PRAVG-TPB3B,Twist,"Machine: T31, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T31,Pending
X74-SouC-TRA1E,CODE128,Composant Twist - X74-SouC-TRA1E,Twist,"Machine: T31, Ordre: 300.0, Reste: 300.0, Actions: Valider Reviser Annuler",,300,,Aptiv Internal,T31,Pending
