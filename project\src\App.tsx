import React, { useState } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { DataProvider } from './contexts/DataContext';
import LoginForm from './components/Auth/LoginForm';
import Header from './components/Layout/Header';
import Sidebar from './components/Layout/Sidebar';
import Dashboard from './components/Dashboard/Dashboard';
import Scanner from './components/Scanner';

// Import other components as needed
// import CustomersPage from './components/Customers/CustomersPage';
// import SuppliersPage from './components/Suppliers/SuppliersPage';
// import InventoryPage from './components/Inventory/InventoryPage';
// import TransactionsPage from './components/Transactions/TransactionsPage';
// import ReportsPage from './components/Reports/ReportsPage';

const MainApp: React.FC = () => {
  const { isAuthenticated } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');

  if (!isAuthenticated) {
    return <LoginForm />;
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <Dashboard />;
      case 'scan':
        return <Scanner />;
      // case 'customers':
      //   return <CustomersPage />;
      // case 'suppliers':
      //   return <SuppliersPage />;
      // case 'inventory':
      //   return <InventoryPage />;
      // case 'transactions':
      //   return <TransactionsPage />;
      // case 'reports':
      //   return <ReportsPage />;
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="flex">
        <Sidebar activeTab={activeTab} onTabChange={setActiveTab} />
        <main className="flex-1 p-6">
          {renderContent()}
        </main>
      </div>
    </div>
  );
};

const App: React.FC = () => {
  return (
    <AuthProvider>
      <DataProvider>
        <MainApp />
      </DataProvider>
    </AuthProvider>
  );
};

export default App;