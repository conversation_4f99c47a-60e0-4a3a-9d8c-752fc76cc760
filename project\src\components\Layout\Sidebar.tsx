import React from 'react';
import { 
  LayoutDashboard, 
  Users, 
  Package, 
  TrendingUp, 
  FileText, 
  Settings,
  Building2,
  Truck,
  ScanLine
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

interface SidebarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ activeTab, onTabChange }) => {
  const { hasPermission } = useAuth();

  const menuItems = [
    { 
      id: 'dashboard', 
      label: 'Tableau de Bord', 
      icon: LayoutDashboard, 
      permission: 'read' 
    },
    { 
      id: 'scan', 
      label: 'Scanner', 
      icon: ScanLine, 
      permission: 'read' 
    },
    { 
      id: 'customers', 
      label: 'Clients', 
      icon: Building2, 
      permission: 'read' 
    },
    { 
      id: 'suppliers', 
      label: 'Fournisseurs', 
      icon: Truck, 
      permission: 'read' 
    },
    { 
      id: 'inventory', 
      label: 'Inventaire', 
      icon: Package, 
      permission: 'read' 
    },
    { 
      id: 'transactions', 
      label: 'Transactions', 
      icon: TrendingUp, 
      permission: 'read' 
    },
    { 
      id: 'reports', 
      label: 'Rapports', 
      icon: FileText, 
      permission: 'reports' 
    },
    { 
      id: 'users', 
      label: 'Utilisateurs', 
      icon: Users, 
      permission: 'users' 
    },
    { 
      id: 'settings', 
      label: 'Paramètres', 
      icon: Settings, 
      permission: 'write' 
    },
  ];

  const visibleItems = menuItems.filter(item => hasPermission(item.permission));

  return (
    <aside className="w-64 bg-gray-900 text-white">
      <nav className="mt-8">
        <div className="px-4 space-y-2">
          {visibleItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeTab === item.id;
            
            return (
              <button
                key={item.id}
                onClick={() => onTabChange(item.id)}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  isActive
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                }`}
              >
                <Icon className="w-5 h-5 mr-3" />
                {item.label}
              </button>
            );
          })}
        </div>
      </nav>
    </aside>
  );
};

export default Sidebar;