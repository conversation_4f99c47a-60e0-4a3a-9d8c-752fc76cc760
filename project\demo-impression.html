<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Démonstration - Impression Scanner</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-print me-2"></i>
                            Démonstration - Fonctionnalités d'Impression
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>À propos de cette démonstration</h6>
                            <p class="mb-0">Cette page démontre les fonctionnalités d'impression du scanner de code-barres. 
                            Vous pouvez tester l'impression avec des données d'exemple sans affecter la base de données principale.</p>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <h5>🎯 Fonctionnalités Disponibles</h5>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">
                                        <i class="fas fa-file-alt text-primary me-2"></i>
                                        <strong>Rapport de Scan Individuel</strong><br>
                                        <small class="text-muted">Impression détaillée d'un code scanné</small>
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-history text-success me-2"></i>
                                        <strong>Historique des Scans</strong><br>
                                        <small class="text-muted">Tableau des derniers scans effectués</small>
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-vial text-warning me-2"></i>
                                        <strong>Rapport de Test</strong><br>
                                        <small class="text-muted">Version simplifiée pour les tests</small>
                                    </li>
                                </ul>
                            </div>
                            
                            <div class="col-md-6">
                                <h5>📋 Codes de Démonstration</h5>
                                <div class="mb-3">
                                    <label class="form-label">Sélectionnez un code pour démonstration:</label>
                                    <select id="demoCode" class="form-select">
                                        <option value="">-- Choisir un code --</option>
                                        <optgroup label="Squib-Commande (CODE128)">
                                            <option value="EK9-HAB-Brg40">EK9-HAB-Brg40 - Stock: 1000</option>
                                            <option value="EK9-HAB-Brg41">EK9-HAB-Brg41 - Stock: 300</option>
                                            <option value="EK9-HAB-Brg42">EK9-HAB-Brg42 - Stock: 500</option>
                                        </optgroup>
                                        <optgroup label="Data Twist (Production)">
                                            <option value="EK9-HAB-TAB3U">EK9-HAB-TAB3U - Production</option>
                                            <option value="EK9-HAB-TAB3V">EK9-HAB-TAB3V - Production</option>
                                        </optgroup>
                                    </select>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" onclick="demonstrateDetailedPrint()">
                                        <i class="fas fa-print me-2"></i>Démonstration Rapport Détaillé
                                    </button>
                                    <button class="btn btn-success" onclick="demonstrateHistoryPrint()">
                                        <i class="fas fa-history me-2"></i>Démonstration Historique
                                    </button>
                                    <button class="btn btn-warning" onclick="demonstrateTestPrint()">
                                        <i class="fas fa-vial me-2"></i>Démonstration Test
                                    </button>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <div class="row">
                            <div class="col-md-12">
                                <h5>🔗 Liens vers les Applications</h5>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="card h-100">
                                            <div class="card-body text-center">
                                                <i class="fas fa-search fa-3x text-primary mb-3"></i>
                                                <h6>Scanner Principal</h6>
                                                <p class="text-muted">Interface complète avec impression</p>
                                                <a href="index.html" class="btn btn-primary btn-sm">Ouvrir</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card h-100">
                                            <div class="card-body text-center">
                                                <i class="fas fa-flask fa-3x text-success mb-3"></i>
                                                <h6>Page de Test</h6>
                                                <p class="text-muted">Tests et diagnostics</p>
                                                <a href="test-scanner.html" class="btn btn-success btn-sm">Ouvrir</a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card h-100">
                                            <div class="card-body text-center">
                                                <i class="fas fa-barcode fa-3x text-warning mb-3"></i>
                                                <h6>Générateur</h6>
                                                <p class="text-muted">Génération de codes-barres</p>
                                                <a href="barcode-integration.html" class="btn btn-warning btn-sm">Ouvrir</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <div class="alert alert-light">
                            <h6><i class="fas fa-lightbulb me-2"></i>Instructions d'Utilisation</h6>
                            <ol>
                                <li><strong>Sélectionnez un code</strong> dans la liste déroulante ci-dessus</li>
                                <li><strong>Cliquez sur un bouton</strong> de démonstration pour voir l'aperçu d'impression</li>
                                <li><strong>Autorisez les pop-ups</strong> si votre navigateur le demande</li>
                                <li><strong>L'impression se lance automatiquement</strong> dans la nouvelle fenêtre</li>
                                <li><strong>La fenêtre se ferme</strong> automatiquement après impression</li>
                            </ol>
                            
                            <div class="mt-3">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>
                                    <strong>Note:</strong> Cette démonstration utilise des données d'exemple. 
                                    Pour utiliser avec de vraies données, utilisez le scanner principal.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/data-loader.js"></script>
    <script>
        // Données d'exemple pour la démonstration
        const demoData = {
            "EK9-HAB-Brg40": {
                code: "EK9-HAB-Brg40",
                format: "CODE128",
                name: "Squib EK9-HAB-Brg40",
                category: "HAB-Squib",
                description: "APN-Cable: 47307798, Ordre: 1000.0, Qt/Box: 500.0, Commande/Box: 2.0",
                section: "internalCodes",
                apnCable: "47307798",
                order: 1000,
                totalStock: 1000,
                status: "Active",
                supplier: "Aptiv Squib",
                location: "Commande",
                lastUpdated: new Date().toISOString()
            },
            "EK9-HAB-Brg41": {
                code: "EK9-HAB-Brg41",
                format: "CODE128",
                name: "Squib EK9-HAB-Brg41",
                category: "HAB-Squib",
                description: "APN-Cable: 47316019, Ordre: 300.0, Qt/Box: 300.0, Commande/Box: 1.0",
                section: "internalCodes",
                apnCable: "47316019",
                order: 300,
                totalStock: 300,
                status: "Active",
                supplier: "Aptiv Squib",
                location: "Commande",
                lastUpdated: new Date().toISOString()
            },
            "EK9-HAB-TAB3U": {
                code: "EK9-HAB-TAB3U",
                format: "CODE128",
                name: "Composant HAB - EK9-HAB-TAB3U",
                category: "HAB",
                description: "Machine: MC01, Ordre: 100, Reste: 50",
                section: "internalCodes",
                machine: "MC01",
                order: 100,
                remaining: 50,
                status: "Active",
                supplier: "Aptiv Internal",
                location: "MC01",
                lastUpdated: new Date().toISOString()
            }
        };

        function getSelectedCode() {
            const select = document.getElementById('demoCode');
            const code = select.value;
            if (!code) {
                alert('Veuillez sélectionner un code pour la démonstration.');
                return null;
            }
            return demoData[code];
        }

        function demonstrateDetailedPrint() {
            const result = getSelectedCode();
            if (!result) return;

            // Utiliser la même fonction que le scanner principal
            const printContent = generateDetailedPrintContent(result);
            openPrintWindow(printContent);
        }

        function demonstrateHistoryPrint() {
            // Créer un historique d'exemple
            const sampleHistory = [
                { code: "EK9-HAB-Brg40", action: "Search", timestamp: new Date(Date.now() - 300000).toISOString(), user: "Demo User", location: "Demo Scanner" },
                { code: "EK9-HAB-Brg41", action: "Print", timestamp: new Date(Date.now() - 240000).toISOString(), user: "Demo User", location: "Demo Scanner" },
                { code: "EK9-HAB-TAB3U", action: "Search", timestamp: new Date(Date.now() - 180000).toISOString(), user: "Demo User", location: "Demo Scanner" },
                { code: "EK9-HAB-Brg42", action: "Print", timestamp: new Date(Date.now() - 120000).toISOString(), user: "Demo User", location: "Demo Scanner" },
                { code: "EK9-HAB-TAB3V", action: "Search", timestamp: new Date(Date.now() - 60000).toISOString(), user: "Demo User", location: "Demo Scanner" }
            ];

            const printContent = generateHistoryPrintContent(sampleHistory);
            openPrintWindow(printContent);
        }

        function demonstrateTestPrint() {
            const result = getSelectedCode();
            if (!result) return;

            const printContent = generateTestPrintContent(result);
            openPrintWindow(printContent);
        }

        function openPrintWindow(content) {
            const printWindow = window.open('', '_blank', 'width=800,height=600');
            if (!printWindow) {
                alert('Impossible d\'ouvrir la fenêtre d\'impression. Vérifiez que les pop-ups sont autorisés.');
                return;
            }

            printWindow.document.write(content);
            printWindow.document.close();
            
            printWindow.onload = function() {
                printWindow.focus();
                printWindow.print();
                setTimeout(() => {
                    printWindow.close();
                }, 1000);
            };
        }

        // Fonctions de génération de contenu (simplifiées pour la démo)
        function generateDetailedPrintContent(result) {
            return `
                <!DOCTYPE html>
                <html><head><meta charset="UTF-8"><title>Démonstration - Rapport Détaillé</title>
                <style>body{font-family:Arial,sans-serif;margin:20px;}.header{text-align:center;border-bottom:2px solid #007bff;padding-bottom:20px;margin-bottom:30px;}.code-display{font-size:24px;font-weight:bold;text-align:center;background-color:#f8f9fa;padding:15px;border-radius:8px;margin:20px 0;}</style>
                </head><body>
                <div class="header"><h2>APTIV - Démonstration Impression</h2><p>Rapport détaillé généré le ${new Date().toLocaleString('fr-FR')}</p></div>
                <div class="code-display">${result.code}</div>
                <p><strong>Nom:</strong> ${result.name}</p>
                <p><strong>Format:</strong> ${result.format}</p>
                <p><strong>Catégorie:</strong> ${result.category}</p>
                <p><strong>Description:</strong> ${result.description}</p>
                <p><strong>Statut:</strong> ${result.status}</p>
                <div style="margin-top:30px;text-align:center;font-size:12px;color:#666;">Démonstration - APTIV Twisting Monitoring Tool</div>
                </body></html>
            `;
        }

        function generateHistoryPrintContent(history) {
            const rows = history.map(scan => `<tr><td>${scan.code}</td><td>${scan.action}</td><td>${new Date(scan.timestamp).toLocaleString('fr-FR')}</td><td>${scan.user}</td><td>${scan.location}</td></tr>`).join('');
            return `
                <!DOCTYPE html>
                <html><head><meta charset="UTF-8"><title>Démonstration - Historique</title>
                <style>body{font-family:Arial,sans-serif;margin:20px;}table{width:100%;border-collapse:collapse;}th,td{border:1px solid #ddd;padding:8px;text-align:left;}th{background-color:#f2f2f2;}</style>
                </head><body>
                <h2 style="text-align:center;">APTIV - Démonstration Historique</h2>
                <p style="text-align:center;">Rapport généré le ${new Date().toLocaleString('fr-FR')}</p>
                <table><thead><tr><th>Code</th><th>Action</th><th>Date/Heure</th><th>Utilisateur</th><th>Emplacement</th></tr></thead><tbody>${rows}</tbody></table>
                <div style="margin-top:30px;text-align:center;font-size:12px;color:#666;">Démonstration - Total: ${history.length} scans</div>
                </body></html>
            `;
        }

        function generateTestPrintContent(result) {
            return `
                <!DOCTYPE html>
                <html><head><meta charset="UTF-8"><title>Démonstration - Test</title>
                <style>body{font-family:Arial,sans-serif;margin:20px;}.code-display{font-size:20px;font-weight:bold;text-align:center;background-color:#f8f9fa;padding:10px;border-radius:8px;}</style>
                </head><body>
                <h2 style="text-align:center;">APTIV - Démonstration Test</h2>
                <div class="code-display">${result.code}</div>
                <p><strong>Format:</strong> ${result.format}</p>
                <p><strong>Statut:</strong> Code trouvé avec succès</p>
                <div style="margin-top:30px;text-align:center;font-size:12px;color:#666;">Test de démonstration réussi</div>
                </body></html>
            `;
        }
    </script>
</body>
</html>
